package com.swcares.aiot.base.controller;

import com.swcares.aiot.base.service.IConfBankInfoBizService;
import com.swcares.aiot.client.IConfBankInfoBizClient;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aiot.base.controller.ConfBankInfoBizController <br>
 * Description：银行信息管理 <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024/10/9 11:59 <br>
 * @version v1.0 <br>
 */

@Api(tags = "控制器-银行信息管理")
@RestController
@RequestMapping("/bankInfo")
public class ConfBankInfoBizController implements IConfBankInfoBizClient {
    @Resource
    private IConfBankInfoBizService iConfBankInfoBizService;

    @Override
    @ApiOperation("根据银行名称获取银行编码")
    @GetMapping("/getCodeByName")
    public BaseResult<String> getCodeByName(String name) {
        return BaseResult.ok(iConfBankInfoBizService.getCodeByName(name));
    }

    @Override
    @ApiOperation("根据银行名称获取银行编码")
    @PostMapping("/getCodeByNames")
    public BaseResult<Map<String, String>> getCodeByNames(@RequestBody @Valid List<String> names) {
        return BaseResult.ok(iConfBankInfoBizService.getCodeByNames(names));
    }
}

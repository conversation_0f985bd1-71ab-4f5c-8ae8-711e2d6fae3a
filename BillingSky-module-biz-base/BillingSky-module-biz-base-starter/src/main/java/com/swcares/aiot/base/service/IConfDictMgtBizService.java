package com.swcares.aiot.base.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.base.model.dto.ConfDictMgtDto;
import com.swcares.aiot.base.model.dto.ConfDictMgtPageDto;
import com.swcares.aiot.base.model.vo.ConfDictMgtVo;
import com.swcares.aiot.base.model.vo.ConfDictTreeVo;
import com.swcares.aiot.model.vo.ConfDictCacheVo;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aiot.base.service.IConfDictMgtBizService <br>
 * Description：数据字典管理业务接口 <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024/10/11 14:16 <br>
 * @version v1.0 <br>
 */
public interface IConfDictMgtBizService {

    /**
     * Description 查询数据字典分页数据 <br>
     * author dengbanglin <br>
     * Date 2024/10/11 14:34
     * @param pageDto 分页参数
     * @return IPage<ConfDictMgtVo>
     */
    IPage<ConfDictMgtVo> page(ConfDictMgtPageDto pageDto);

    /**
     * Description 新建数据字典 <br>
     * author dengbanglin <br>
     * Date 2024/10/11 14:35
     * @param saveDto 新建参数
     * @return ConfDictMgtVo
     */
    ConfDictMgtVo save(ConfDictMgtDto saveDto);

    /**
     * Description 编辑数据字典 <br>
     * author dengbanglin <br>
     * Date 2024/10/11 14:35
     * @param updateDto 编辑参数
     * @return ConfDictMgtVo
     */
    ConfDictMgtVo update(ConfDictMgtDto updateDto);

    /**
     * Description 获取数据字典详情 <br>
     * author dengbanglin <br>
     * Date 2024/10/11 14:35
     * @param id 逐渐id
     * @return ConfDictMgtVo
     */
    ConfDictMgtVo get(Long id);

    /**
     * Description 逻辑删除数据字典 <br>
     * author dengbanglin <br>
     * Date 2024/10/11 14:36
     * @param id 逐渐id
     * @return boolean
     */
    ConfDictMgtVo delete(Long id);

    /**
     * Description 获取数据字典树 <br>
     * author dengbanglin <br>
     * Date 2024/10/11 16:03
     * @return List<ConfDictTreeVo>
     */
    List<ConfDictTreeVo> getDictTree();

    /**
     * Description 根据字典编码获取字典（缓存） <br>
     * author dengbanglin <br>
     * Date 2024/10/15 09:23
     * @param code 字典编码
     * @return Map<字典集编码, 字典值列表>
     */
    Map<String,List<ConfDictCacheVo>> getDictCache(String code);

    /**
     * Description 按字典编码删除字典缓存 <br>
     * author dengbanglin <br>
     * Date 2024/10/15 14:40
     * @param code 字典编码
     */
    void deleteDictCache(String code);
}

package com.swcares.aiot.properties;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "swcares.king.dee")
@NoArgsConstructor
public class KingDeeProperties {
    private Boolean enable = true;
    /**
     * 应用系统编码
     */
    private String clientId;
    /**
     * AccessToken认证密钥
     */
    private String clientSecret;
    /**
     * 代理用户的用户名
     */
    private String username;
    /**
     * 数据中心id
     */
    private String accountId;
    /**
     * 登录地址
     */
    private String loginUrl;
    /**
     * 提交报销地址
     */
    private String subReiEstBillUrl;
    /**
     * 部门编号
     */
    private String applierNumber;
    /**
     * 申请人部门编码
     */
    private String orgNum;
    /**
     * 申请人公司编码
     */
    private String companyNumber;
    /**
     * 费用承担部门编码
     */
    private String costDeptNumber;
    /**
     * 费用承担公司编码
     */
    private String costCompanyNumber;
    /**
     * 费用类型编码
     */
    private String expenseItemNumber;
    /**
     * 收款人类型
     */
    private String payerType;
    /**
     * 往来类型
     */
    private String billPayerType;
    /**
     * 支付方式编码
     */
    private String payMode;

}

package com.swcares.aiot.convert;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.annotation.*;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：ConvertCodeAnnotation <br>
 * Package：com.swcares.base.flight.api.common.convert <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description: 编码转换注解类       <br>
 *
 * <AUTHOR> <br>
 * date 2022年 05月06日 18:14 <br>
 * @version v1.0 <br>
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotationsInside
@JsonSerialize(using = ConvertCodeSerialize.class)
public @interface ConvertCodeAnnotation {
    /**
     * Description : 枚举转换类型 <br/>
     */
    ConvertCodeTypeEnum typeEnum();
    /**
     * Description : type = ConvertCodeTypeEnum.DICT   dictType：此项必填<br/>
     */
    String dictType() default "";

    /**
     * Description : RelationAttributeName 关联属性名,空则使用当前注解所在属性 <br/>
     */
    String relationAttributeName() default "";
}

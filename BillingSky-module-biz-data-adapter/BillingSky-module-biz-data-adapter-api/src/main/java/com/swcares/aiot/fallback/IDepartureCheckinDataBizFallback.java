package com.swcares.aiot.fallback;

import com.swcares.aiot.client.IDepartureCheckinDataBizClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;

/**
 * <AUTHOR>
 */
@Slf4j
public class IDepartureCheckinDataBizFallback implements FallbackFactory<IDepartureCheckinDataBizClient> {
    @Override
    public IDepartureCheckinDataBizClient create(Throwable cause) {
        return null;
    }
}

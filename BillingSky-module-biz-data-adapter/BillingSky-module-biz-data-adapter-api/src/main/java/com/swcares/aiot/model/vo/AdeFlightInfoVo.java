package com.swcares.aiot.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ClassName：AdeFlightInfoVo
 * Description：pic数据Vo
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2025/3/24 13:13
 * Version v1.0
 */
@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AdeFlightInfoVo {

    @ApiModelProperty(value = "来源主键id")
    private String id;

    @ApiModelProperty(value = "航班号")
    private String fltCode;

    @ApiModelProperty(value = "航班日期")
    private String fltDate;

    @ApiModelProperty(value = "始发站")
    private String departure;

    @ApiModelProperty(value = "目的站")
    private String destination;

    @ApiModelProperty(value = "时间戳 (毫秒级)")
    private String opTm;

    @ApiModelProperty(value = "舱单报文-已登机旅客数")
    private String boardpsrcnt;

    @ApiModelProperty(value = "舱单报文-行李件数")
    private String baggagecount;

    @ApiModelProperty(value = "舱单报文-行李重量")
    private String baggageweight;

    @ApiModelProperty(value = "舱单报文-货物重量")
    private String cargoweight;

    @ApiModelProperty(value = "舱单报文-邮件重量")
    private String mailweight;

    @ApiModelProperty(value = "舱单报文-舱位旅客人数")
    private String berthpsrcount;

    @ApiModelProperty(value = "舱单报文-成人儿童婴儿旅客人数")
    private String acipsrcount;

    @ApiModelProperty(value = "舱单报文-成人旅客人数")
    @JsonProperty(value = "aPsrcount")
    private String aPsrcount;

    @ApiModelProperty(value = "舱单报文-儿童旅客人数")
    @JsonProperty(value = "cPsrcount")
    private String cPsrcount;

    @ApiModelProperty(value = "舱单报文-婴儿旅客人数")
    @JsonProperty(value = "iPsrcount")
    private String iPsrcount;

    @ApiModelProperty(value = "舱单报文-头等舱旅客人数")
    private String berthFPsrcount;

    @ApiModelProperty(value = "舱单报文-商务舱旅客人数")
    private String berthCPsrcount;

    @ApiModelProperty(value = "舱单报文-经济舱旅客人数")
    private String berthYPsrcount;

    @ApiModelProperty(value = "恒载重量")
    private String loadtotal;

    @ApiModelProperty(value = "中转货邮行重量")
    private String tra;

    @ApiModelProperty(value = "成人过站数")
    private String csadu;

    @ApiModelProperty(value = "儿童过站数")
    private String cschd;

    @ApiModelProperty(value = "婴儿过站数")
    private String csift;

    @ApiModelProperty(value = "机组人数")
    private String cscrew;

    @ApiModelProperty(value = "随机机组人数")
    private String csextcrew;

    @ApiModelProperty(value = "随身行李重量")
    private String carryonw;

    @ApiModelProperty(value = "本地机场三字码")
    private String airportId;

    @ApiModelProperty(value = "始发成人旅客数")
    private String depAdu;

    @ApiModelProperty(value = "始发儿童旅客数")
    private String depChd;

    @ApiModelProperty(value = "始发婴儿旅客数")
    private String depIft;

    @ApiModelProperty(value = "电子客票旅客数")
    private String eTicketCnt;

    @ApiModelProperty(value = "VIP 旅客数")
    private String vipCnt;

    @ApiModelProperty(value = "速运行李件数")
    private String expressCnt;

    @ApiModelProperty(value = "速运行李重量")
    private String expressWt;

    @ApiModelProperty(value = "AVIH 行李件数")
    private String avihBaggageCnt;

    @ApiModelProperty(value = "AVIH 行李重量")
    private String avihBaggageWt;

    @ApiModelProperty(value = "机号")
    private String regno;

    @ApiModelProperty(value = "座位布局配置")
    private String spsrSeatcap;

    @ApiModelProperty(value = "过站行李件数")
    private String traBagCount;

    @ApiModelProperty(value = "过站邮件重量")
    private String traMailWeight;

    @ApiModelProperty(value = "过站行李重量")
    private String traBagWeight;

    @ApiModelProperty(value = "过站货物重量")
    private String traCargoWeight;

    @ApiModelProperty(value = "过站业载总重量(TRA中解析的)")
    private String traPayloadWeightSI;

    @ApiModelProperty(value = "过站货物重量:(TRA中解析的)")
    private String traCargoWeightSI;

    @ApiModelProperty(value = "过站邮件重量(TRA中解析的)")
    private String traMailWeightSI;

    @ApiModelProperty(value = "过站行李重量(TRA中解析的)")
    private String traBagWeightSI;

    @ApiModelProperty(value = "继续转运业载重量(TRA中解析的)")
    private String transferPayloadWeightSI;

    @ApiModelProperty(value = "压舱物(BAL)和航材(EIC)")
    private String ldmExtInfo;
    
}

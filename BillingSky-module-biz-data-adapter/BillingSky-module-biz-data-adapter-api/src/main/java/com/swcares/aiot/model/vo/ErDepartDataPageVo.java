package com.swcares.aiot.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * ClassName：ErDepartDataPageVo
 * Description：离港业务数据  PageVo
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2025/2/27 10:54
 * Version v1.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ErDepartDataPageVo", description = "离港业务数据PageVo")
public class ErDepartDataPageVo implements Serializable {
    @ApiModelProperty(value = "日期[航班日期yyyy-MM-dd]")
    private LocalDate flightDate;
    @ApiModelProperty(value = "航班号")
    private String flightNo;
    @ApiModelProperty(value = "出发机场")
    private String org;
    @ApiModelProperty(value = "到达机场")
    private String dst;
    @ApiModelProperty(value = "航空公司[二字码]")
    private String airlineCode;
    @ApiModelProperty(value = "机号")
    private String regNo;
    @ApiModelProperty(value = "舱位")
    private String cabin;
    @ApiModelProperty(value = "舱位人数")
    private Integer psgNum;
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;
}

package com.swcares.aiot.client;

import com.swcares.aiot.fallback.IPicChainBizFallback;
import com.swcares.aiot.model.dto.QueryPicInfoDto;
import com.swcares.aiot.model.vo.AdeFlightInfoVo;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.feign.FeignClientInterceptor;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * ClassName：IPicChainBizClient
 * Description：pic数据外部调用接口
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2025/3/24 14:30
 * Version v1.0
 */
@FeignClient(value = "flight",
        contextId = "IPicChainBizClient",
        path = "/flight/picChainBiz",
        fallbackFactory = IPicChainBizFallback.class,
        configuration = FeignClientInterceptor.class)
public interface IPicChainBizClient {

    @PostMapping("/getChainPicInfo")
    @ApiOperation(value = "从航旅链获取pic数据")
    BaseResult<List<AdeFlightInfoVo>> getChainPicInfo(@RequestBody @Validated QueryPicInfoDto dto);

}

package com.swcares.aiot.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * ClassName：ExcessLuggageTransOrderVo
 * Description：逾重行李划拨账单Vo
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2025/4/16 21:41
 * Version v1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ExcessLuggageTransOrderVo对象", description="逾重行李划拨账单Vo")
public class ExcessLuggageTransOrderVo {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "划拨账单id")
    private String billId;

    @ApiModelProperty(value = "划拨账单的账单日期（yyyymmdd）")
    private String billDate;

    @ApiModelProperty(value = "主体标识")
    private String partyCode;

    @ApiModelProperty(value = "主体名称")
    private String partyName;

    @ApiModelProperty(value = "机场标识")
    private String airportCode;

    @ApiModelProperty(value = "划拨金额")
    private String transAmt;

    @ApiModelProperty(value = "业务开始日期（yyyymmdd）")
    private String beginDate;

    @ApiModelProperty(value = "业务结束日期（yyyymmdd）")
    private String endDate;

    @ApiModelProperty(value = "hash值")
    private String hashValue;

    @ApiModelProperty(value = "版本号")
    private Integer version;

    @ApiModelProperty(value = "同步时间")
    private LocalDateTime syncTime;

    @ApiModelProperty(value = "删除标识（1：删除 0：正常）")
    private Boolean deleted;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

}

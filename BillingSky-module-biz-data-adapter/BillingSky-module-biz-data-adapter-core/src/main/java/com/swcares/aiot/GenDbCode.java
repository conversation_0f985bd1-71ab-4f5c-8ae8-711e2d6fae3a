package com.swcares.aiot;

import cn.hutool.setting.Setting;
import com.worm.MybatisPlusGenProperties;
import com.worm.MybatisPlusGenUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class GenDbCode {
    private static final Setting SETTING_DATASOURCE = new Setting("datasource.setting");
    public static void main(String[] args) {
        SETTING_DATASOURCE.autoLoad(true);
        MybatisPlusGenProperties mybatisPlusGenProperties = new MybatisPlusGenProperties();
        mybatisPlusGenProperties.setEnable(true);
        //
        mybatisPlusGenProperties.setOutputPath("./BillingSky-module-biz-data-adapter/BillingSky-module-biz-data-adapter-core");
        // 数据库名
        mybatisPlusGenProperties.setDbName(SETTING_DATASOURCE.getStr("dbName"));
        // 主机ip
        mybatisPlusGenProperties.setHost(SETTING_DATASOURCE.getStr("host"));
        // 端口
        mybatisPlusGenProperties.setPort(SETTING_DATASOURCE.getInt("port"));
        //
        mybatisPlusGenProperties.setUname(SETTING_DATASOURCE.getStr("uName"));
        //
        mybatisPlusGenProperties.setPwd(SETTING_DATASOURCE.getStr("pwd"));
        //
        mybatisPlusGenProperties.setIncludeTableNames(Arrays.asList("excess_luggage_trans_order", ""));
        MybatisPlusGenUtils.rebuild(mybatisPlusGenProperties);
    }
}

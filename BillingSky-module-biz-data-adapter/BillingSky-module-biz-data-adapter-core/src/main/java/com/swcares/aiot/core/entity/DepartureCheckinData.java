package com.swcares.aiot.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 离岗系统值机数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("departure_checkin_data")
@ApiModel(value="DepartureCheckinData对象", description="离岗系统值机数据")
public class DepartureCheckinData extends Model<DepartureCheckinData> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "航班第一航节起飞日期")
    @TableField("flight_one_departure_date")
    private LocalDate flightOneDepartureDate;

    @ApiModelProperty(value = "航空公司")
    @TableField("airline_code")
    private String airlineCode;

    @ApiModelProperty(value = "航班号")
    @TableField("flight_no")
    private String flightNo;

    @ApiModelProperty(value = "航班号后缀")
    @TableField("flight_no_suffix")
    private String flightNoSuffix;

    @ApiModelProperty(value = "机型")
    @TableField("model")
    private String model;

    @ApiModelProperty(value = "飞机注册号")
    @TableField("reg_no")
    private String regNo;

    @ApiModelProperty(value = "航班航线")
    @TableField("airline")
    private String airline;

    @ApiModelProperty(value = "航班国际国内标记")
    @TableField("flight_mark")
    private String flightMark;

    @ApiModelProperty(value = "出发机场")
    @TableField("departure_airport")
    private String departureAirport;

    @ApiModelProperty(value = "到达机场")
    @TableField("arrival_airport")
    private String arrivalAirport;

    @ApiModelProperty(value = "本航节计划起飞时间（当地时间）")
    @TableField("departure_time")
    private String departureTime;

    @ApiModelProperty(value = "本航节计划到达时间（当地时间）")
    @TableField("arrival_time")
    private String arrivalTime;

    @ApiModelProperty(value = "本航节实际起飞时间（当地时间）")
    @TableField("actual_takeoff_time")
    private String actualTakeoffTime;

    @ApiModelProperty(value = "主舱位")
    @TableField("main_cabin_position")
    private String mainCabinPosition;

    @ApiModelProperty(value = "旅客人数")
    @TableField("passengers_number")
    private String passengersNumber;

    @ApiModelProperty(value = "成人旅客数")
    @TableField("adult_number")
    private String adultNumber;

    @ApiModelProperty(value = "儿童旅客数")
    @TableField("child_number")
    private String childNumber;

    @ApiModelProperty(value = "婴儿人数（不占座）")
    @TableField("baby_number")
    private String babyNumber;

    @ApiModelProperty(value = "Go show人数")
    @TableField("go_show")
    private String goShow;

    @ApiModelProperty(value = "No show人数")
    @TableField("no_show")
    private String noShow;

    @ApiModelProperty(value = "出港联程人数")
    @TableField("connecting_flight_number")
    private String connectingFlightNumber;

    @ApiModelProperty(value = "VIP人数")
    @TableField("vip_number")
    private String vipNumber;

    @ApiModelProperty(value = "同步时间")
    @TableField("sync_time")
    private LocalDateTime syncTime;

    @ApiModelProperty(value = "删除标识（1：删除 0：正常）")
    @TableField(value = "deleted", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

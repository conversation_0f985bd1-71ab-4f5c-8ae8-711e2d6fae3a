package com.swcares.aiot.yuexinplus.service;


import com.swcares.aiot.model.dto.QueryAagreementDto;
import com.swcares.aiot.model.vo.QueryAagreementInfoVo;
import com.travelsky.atc.gateway.sdk.common.TransactionInfo;

import java.util.List;

/**
 * ClassName：IDownChainClientService
 * Description：航旅链-数据下链
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2025/2/20 11:12
 * Version v1.0
 */
public interface IDownChainClientService {

    /**
     * Title：subscribeLuggageOrder
     * Description：订阅逾重行李订单
     * author：李军呈
     * date：2025/2/20 11:15
     * @return : Runnable
     */
    Runnable subscribeLuggageOrder();

    /**
     * Title：getChainExcessLuggageOrder
     * Description：获取航旅链悦行+的账单数据
     * author：李军呈
     * date：2025/2/25 09:08
     * @param transactionInfo : data
     */
    void getChainExcessLuggageOrder(TransactionInfo transactionInfo);

    /**
     * Title：subscribeDepartureCheckinData
     * Description：订阅离岗系统值机、配载数据
     * author：李军呈
     * date：2025/3/19 11:43
     * @return : java.lang.Runnable
     */
    Runnable subscribeDepartureCheckinData();

    /**
     * Title：getChainDepartureCheckinData
     * Description：获取离岗系统值机、配载数据
     * author：李军呈
     * date：2025/3/19 15:09
     * @param transactionInfo : data
     */
    void getChainDepartureCheckinData(TransactionInfo transactionInfo);

    /**
     * Title：querAagreement
     * Description：通程航班分摊协议查询
     * author：李军呈
     * date：2025/4/3 14:07
     * @param dto: dto
     * return: java.util.List<com.swcares.aiot.model.vo.FulfillContractVo>
     */
    List<QueryAagreementInfoVo> querAagreement(QueryAagreementDto dto);

    /**
     * Title：queryTransferFlightAagreement
     * Description：中转航班分摊协议查询
     * author：李军呈
     * date：2025/4/16 20:26
     * @param dto: dto
     * return: java.util.List<com.swcares.aiot.model.vo.QueryAagreementInfoVo>
     */
    List<QueryAagreementInfoVo> queryTransferFlightAagreement(QueryAagreementDto dto);

    /**
     * Title：queryTransBill
     * Description：根据账单日期查询划拨账单
     * author：李军呈
     * date：2025/4/16 21:45
     * @param billDate: 账单日期
     * return: boolean
     */
    boolean queryTransBill(String billDate);

    /**
     * Title：subscribeBankConfirmContract
     * Description：订阅银行确认签约信息
     * author：李军呈
     * date：2025/4/16 16:31
     * @return : void
     */
    Runnable subscribeBankConfirmContract();

    /**
     * Title：subscribeImplementResultContract
     * Description：订阅支付状态信息
     * author：李军呈
     * date：2025/4/16 20:06
     * @return : void
     */
    Runnable subscribeImplementResultContract();

    /**
     * Title：bankConfirmDataAnaly
     * Description：银行确认支付数据解析
     * author：李军呈
     * date：2025/4/18 11:11
     * @param data: 数据
     */
    void bankConfirmDataAnaly(String data);

    /**
     * Title：bankConfirmDataAnaly
     * Description：银行确认签约数据解析
     * author：李军呈
     * date：2025/4/18 11:11
     * @param data: 数据
     */
    void bankConfirmContractDataAnaly(String data);

}

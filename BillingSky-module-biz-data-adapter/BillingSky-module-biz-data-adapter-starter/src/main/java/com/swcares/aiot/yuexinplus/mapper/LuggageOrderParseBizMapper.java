package com.swcares.aiot.yuexinplus.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.model.dto.ExcessLuggageOrderDataPageDataAdapterDto;
import com.swcares.aiot.model.dto.ExcessLuggageTransOrderPageDto;
import com.swcares.aiot.model.vo.ExcessLuggageOrderDataVO;
import com.swcares.aiot.model.vo.ExcessLuggageTransOrderVo;
import com.swcares.aiot.yuexinplus.core.model.vo.ExcessLuggageOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ClassName：ExcessLuggageOrderBizMapper
 * Description：逾重行李账单 Mapper
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2024/11/29 19:51
 * Version v1.0
 */
@Mapper
public interface LuggageOrderParseBizMapper {

    /**
     * Title：page
     * Description：逾重行李分页列表
     * author：李军呈
     * date： 2024/12/2 9:50
     * @param dto dto
     * @param page 分页
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aiot.vo.ExcessLuggageOrderVO>
     */
    Page<ExcessLuggageOrderDataVO> page(@Param("query") ExcessLuggageOrderDataPageDataAdapterDto dto, Page<ExcessLuggageOrderDataVO> page);

    List<String> getListByHashValue(@Param("list") List<String> hashList);

    List<ExcessLuggageOrderVo> getListByNewAppOrderCreateDate(@Param("createDateList") List<String> newAppOrderCreateDateList);

    Page<ExcessLuggageTransOrderVo> transBillPage(@Param("query") ExcessLuggageTransOrderPageDto dto, Page<ExcessLuggageTransOrderVo> page);


}

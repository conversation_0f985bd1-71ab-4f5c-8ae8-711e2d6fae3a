package com.swcares.aiot.yuexinplus.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.google.protobuf.ProtocolStringList;
import com.swcares.aiot.yuexinplus.core.enums.EnumChainHeight;
import com.swcares.aiot.yuexinplus.service.IChainHeightBizService;
import com.swcares.aiot.yuexinplus.service.IDownChainClientService;
import io.grpc.stub.StreamObserver;
import lombok.Data;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.chainmaker.pb.common.ResultOuterClass;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * ClassName：HmImplementResultHandler
 * Description：航贸链订阅银行确认响应类
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2025/4/16 19:43
 * Version v1.0
 */
@Getter
@Slf4j
@Component
@Data
@Service
public class HmImplementResultHandler {

    @Resource
    private IDownChainClientService downChainClientService;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    private ScheduledFuture<?> reconnectTask;

    // 每隔多少分钟启动重连
    private static final int RECONNECT_DELAY_MINUTES = 30;

    @Resource
    private IChainHeightBizService chainHeightBizService;

    /**
     * Title：implResponseObserver
     * Description：创建一个StreamObserver实例，用于处理订阅结果
     * author：李军呈
     * date：2025/4/18 09:47
     */
     StreamObserver<ResultOuterClass.SubscribeResult> implResponseObserver = new StreamObserver<ResultOuterClass.SubscribeResult>() {
        @Override
        public void onNext(ResultOuterClass.SubscribeResult result) {
            try {
                ResultOuterClass.ContractEventInfoList contract = ResultOuterClass.ContractEventInfoList.parseFrom(result.getData());
                log.info("接收航贸链银行确认支付订阅消息:"+ contract);
                if(CollUtil.isEmpty(contract.getContractEventsList())){
                    return;
                }
                for(ResultOuterClass.ContractEventInfo contractEventInfo : contract.getContractEventsList()){
                    chainHeightBizService.saveChainHeight(EnumChainHeight.IMPMENT_RESULT_HEIGHT.getCode(), contractEventInfo.getBlockHeight());
                    log.info("已保存银行确认支付订阅高度：{}", contractEventInfo.getBlockHeight());

                    if(CollUtil.isNotEmpty(contractEventInfo.getEventDataList())
                            && EnumChainHeight.IMPMENT_RESULT_HEIGHT.getCode().equals(contractEventInfo.getTopic())) {
                        ProtocolStringList dataList = contractEventInfo.getEventDataList();
                        downChainClientService.bankConfirmDataAnaly(dataList.get(0));
                    }
                }
            } catch (Exception e) {
                log.error("航贸链银行确认支付状态处理异常:", e);
            }
        }

        @SneakyThrows
        @Override
        public void onError(Throwable t) {
            log.info("航贸链订阅银行确认支付error:" + t);
            log.info("航贸链订阅银行确认支付启动重连任务onError");
            ThreadUtil.sleep(30000);
            reconnectTask = scheduler.scheduleAtFixedRate(downChainClientService.subscribeImplementResultContract(), 5, RECONNECT_DELAY_MINUTES, TimeUnit.MINUTES);
        }

        @Override
        public void onCompleted() {
            log.info("航贸链订阅银行确认支付订阅完成时，输出日志");
        }
    };

}
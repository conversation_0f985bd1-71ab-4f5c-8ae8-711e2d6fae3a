package com.swcares.aiot.yuexinplus.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.aiot.yuexinplus.core.cons.ConsDataAdapterYueXinPlus;
import com.swcares.aiot.yuexinplus.core.enums.EnumChainHeight;
import com.swcares.aiot.yuexinplus.service.IChainHeightBizService;
import com.swcares.aiot.yuexinplus.service.IDownChainClientService;
import com.travelsky.atc.gateway.sdk.SubscribeHandler;
import com.travelsky.atc.gateway.sdk.common.TransactionInfo;
import com.travelsky.atc.gateway.sdk.exception.SdkException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

@Data
@Slf4j
@Component
public class YxSubscribeHandler {

    @Resource
    private IDownChainClientService downChainClientService;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    private ScheduledFuture<?> reconnectTask;

    // 每隔多少分钟启动重连
    private static final int RECONNECT_DELAY_MINUTES = 30;

    @Resource
    private IChainHeightBizService chainHeightBizService;

    SubscribeHandler subscribeHandler = new SubscribeHandler(){

        @Override
        public void onOpen() {
            log.info("悦行+账单订阅已成功建立");
            if (reconnectTask != null) {
                log.info("Yx航旅链连接成功，取消重连任务");
                reconnectTask.cancel(false);
            }
        }

        @Override
        public void onProcess(TransactionInfo transactionInfo) {
            log.info("YxTransactionInfo值= " + JSONUtil.toJsonStr(transactionInfo));
            String method = transactionInfo.getTransaction().getPayload().getMethod();
            if(ConsDataAdapterYueXinPlus.SAVE_YX_FILE.equals(method)) {
                chainHeightBizService.saveChainHeight(EnumChainHeight.YX_DZCC_HEIGHT.getCode(), transactionInfo.getBlockHeight());
                downChainClientService.getChainExcessLuggageOrder(transactionInfo);
            } else {
                log.info("Method方法名不是悦行+账单查询方法");
            }
        }

        @Override
        public void onClose(int code, String reason) {
            log.info("Yx订阅关闭，Connection closed with code = " + code + ", reason: " + reason);
            log.info("Yx启动重连任务onClose");
            reconnectTask = scheduler.scheduleAtFixedRate(downChainClientService.subscribeLuggageOrder(), 5, RECONNECT_DELAY_MINUTES, TimeUnit.MINUTES);
        }

        @Override
        public void onError(SdkException exception) {
            log.error("Yx调用航旅链SDK失败: ", exception);
            log.info("Yx启动重连任务onError");
            ThreadUtil.sleep(30000);
            reconnectTask = scheduler.scheduleAtFixedRate(downChainClientService.subscribeLuggageOrder(), 5, RECONNECT_DELAY_MINUTES, TimeUnit.MINUTES);
        }
    };

}
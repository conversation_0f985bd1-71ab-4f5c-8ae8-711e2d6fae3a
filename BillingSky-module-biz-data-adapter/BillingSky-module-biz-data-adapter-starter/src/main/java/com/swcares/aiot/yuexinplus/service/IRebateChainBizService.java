package com.swcares.aiot.yuexinplus.service;

import com.swcares.aiot.model.dto.ConfirmContractBusinessDto;
import com.swcares.aiot.model.dto.ConfirmContractDto;
import com.swcares.aiot.model.dto.FulfillContractBusinessDto;
import com.swcares.aiot.model.dto.RebateAddErContractDto;
import com.swcares.aiot.model.vo.FulfillContractVo;
import com.swcares.aiot.model.vo.RebateChainRespVo;
import com.swcares.aiot.model.vo.SignInfoVo;

/**
 * ClassName：IRebateChainBizService
 * Description：离岗返还与航旅链接口 Service
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2025/3/7 10:15
 * Version v1.0
 */
public interface IRebateChainBizService {

    /**
     * Title：addErContract
     * Description：新增合约（新增付款方相关信息）
     * author：李军呈
     * date：2025/3/7 10:15
     * @param rebateAddErContractDto : dto
     * @return : RebateChainRespVo
     */
    RebateChainRespVo addErContract(RebateAddErContractDto rebateAddErContractDto);

    /**
     * Title：confirmContract
     * Description：确认签约（收款方相关信息）
     * author：李军呈
     * date：2025/3/7 12:50
     * @param confirmContractDto : dto
     * @return : com.swcares.aiot.model.vo.RebateChainRespVo
     */
    RebateChainRespVo confirmContract(ConfirmContractDto confirmContractDto);

    /**
     * Title：fulfillContractBusiness
     * Description：履约业务接口
     * author：李军呈
     * date：2025/3/7 16:45
     * @param fulfillContractBusinessDto : dto
     * @return : com.swcares.aiot.model.vo.RebateChainRespVo
     */
    RebateChainRespVo fulfillContractBusiness(FulfillContractBusinessDto fulfillContractBusinessDto);

    /**
     * Title：queryFulfillContract
     * Description：查询履约信息
     * author：李军呈
     * date：2025/3/8 09:49
     * @param businessContractPaymentId : 业务合约主动支付唯一ID查询
     * @return : com.swcares.aiot.model.vo.FulfillContractVo
     */
    FulfillContractVo queryFulfillContract(String businessContractPaymentId);

    /**
     * Title：confirmFulfillContract
     * Description：确认履约业务
     * author：李军呈
     * date：2025/3/8 10:30
     * @param confirmContractBusinessDto : dto
     * @return : com.swcares.aiot.model.vo.RebateChainRespVo
     */
    RebateChainRespVo confirmFulfillContract(ConfirmContractBusinessDto confirmContractBusinessDto);

    /**
     * Title：querySignInfo
     * Description：查询签约信息
     * author：李军呈
     * date：2025/4/2 15:41
     * @param businessContractId : 业务合约id
     * @return : com.swcares.aiot.model.vo.SignInfoVo
     */
    SignInfoVo querySignInfo(String businessContractId);

}

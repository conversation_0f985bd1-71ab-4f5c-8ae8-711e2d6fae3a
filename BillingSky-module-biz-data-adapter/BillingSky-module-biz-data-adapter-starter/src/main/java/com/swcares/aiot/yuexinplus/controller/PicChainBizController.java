package com.swcares.aiot.yuexinplus.controller;

import com.swcares.aiot.client.IPicChainBizClient;
import com.swcares.aiot.model.dto.QueryPicInfoDto;
import com.swcares.aiot.model.vo.AdeFlightInfoVo;
import com.swcares.aiot.yuexinplus.core.cons.ConsDataAdapterYueXinPlus;
import com.swcares.aiot.yuexinplus.service.IPicChainBizService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：PicChainBizController
 * Description：控制器-pic与航旅链相关业务
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2025/3/21 15:37
 * Version v1.0
 */
@ApiVersion(ConsDataAdapterYueXinPlus.CONS_DATA_API_VERSION)
@RestController
@RequestMapping("/picChainBiz")
@Api(tags = "控制器-pic与航旅链相关业务")
public class PicChainBizController implements IPicChainBizClient {

    @Resource
    private IPicChainBizService picChainBizService;

    @PostMapping("/getChainPicInfo")
    @ApiOperation(value = "从航旅链获取pic数据")
    @Override
    public BaseResult<List<AdeFlightInfoVo>> getChainPicInfo(@RequestBody @Validated QueryPicInfoDto dto) {
        return BaseResult.ok(picChainBizService.getChainPicInfo(dto));
    }

}

package com.swcares.aiot.yuexinplus.core.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSONObject;
import com.swcares.aiot.client.IConfConfigBizClient;
import com.swcares.aiot.yuexinplus.core.cons.ConsExceptionCode;
import com.swcares.aiot.yuexinplus.core.model.dto.ChainPropertiesDto;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.util.SpringUtils;
import com.travelsky.atc.gateway.sdk.ChainClient;
import com.travelsky.atc.gateway.sdk.HttpClient;
import com.travelsky.atc.gateway.sdk.config.ChainClientConfig;
import com.travelsky.atc.gateway.sdk.config.HttpClientConfig;
import com.travelsky.atc.gateway.sdk.exception.SdkException;
import com.travelsky.atc.gateway.sdk.logger.DefaultLogger;
import com.travelsky.atc.gateway.sdk.logger.Logger;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Base64;
import java.util.Map;
import java.util.Objects;

/**
 * ClassName：ChainConfig
 * Description：航旅链配置类
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2025/2/20 11:54
 * Version v1.0
 */
@Slf4j
@Data
public class ChainConfig {

    private ChainConfig() {
        log.info("可以选择抛出异常，防止反射调用私有构造函数创建实例");
        throw new AssertionError("This class cannot be instantiated.");
    }

    public static ChainClient getChainClientConfig(String configKey){
        ChainPropertiesDto chainPropertiesDto = getChainskyConfigKey(configKey);
        return getChainClient(chainPropertiesDto);
    }

    private static ChainClient getChainClient(ChainPropertiesDto chainPropertiesDto) {
        try {
            // 1. 创建HTTP连接池
            HttpClient httpClient = HttpClient.create(HttpClientConfig.builder()
                    //网关http地址[平台配置提供项]  https://chainsky.travelsky.com
                    .withAddress(chainPropertiesDto.getChainAddress())
                    //访问网关的Key[平台配置提供项]
                    .withAppKey(chainPropertiesDto.getChainAppKey())
                    //访问网关的Secret[平台配置提供项]
                    .withAppSecret(chainPropertiesDto.getChainAppSecret())
                    //http连接池最大Idle连接数,默认10；http连接池最大连接数，默认20
                    .withPool(10, 20)
                    //客户端发送交易超时时间,默认10s； 客户端发送交易超时时间,默认10s
                    .withTimeout(600, 600)
                    .build());
            // 2. 创建日志对象，使用默认配置，控制台输出，日志级别为INFO
            Logger logger = DefaultLogger.create("[CHAINSKY-SDK]");
            // 3. 创建一个客户端
            return ChainClient.create(ChainClientConfig.builder()
                            //客户端用户私钥
                            .withUserKey(chainPropertiesDto.getChainUserKey())
                            //客户端用户证书
                            .withUserCrt(chainPropertiesDto.getChainUserCrt())
                            //同步交易结果模式下，轮询获取交易结果时的最大轮询次数，删除此项或设为<=0则使用默认值 10
                            //同步交易结果模式下，每次轮询交易结果时的等待时间，单位：ms 删除此项或设为<=0则使用默认值 500
                            .withRetry(10, 1000)
                            .build(),
                    // 连接池对象
                    httpClient,
                    // 日志对象
                    logger);
        } catch (SdkException e) {
            log.error("创建连接过程中出错：{}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("创建连接过程中Exception异常：{}", e.getMessage(), e);
            throw new BusinessException(ConsExceptionCode.CREATE_CHAIN_CLIENT_FAIL);
        }
    }

    /**
     * Title：getChainskyConfigKey
     * Description：获取航旅链逾重行李配置
     * author：李军呈
     * date：2025/2/20 13:47
     * @param configKey:
     * return: com.swcares.aiot.yuexinplus.core.model.dto.ChainPropertiesDto
     */
    public static ChainPropertiesDto getChainskyConfigKey(String configKey) {
        IConfConfigBizClient iConfConfigBizClient = SpringUtils.getBean(IConfConfigBizClient.class);
        BaseResult<JSONObject> chainConfig = iConfConfigBizClient.getConfig(configKey);
        if (ObjectUtil.isEmpty(chainConfig) || HttpStatus.HTTP_OK != chainConfig.getCode() || null == chainConfig.getData()) {
            log.error("航旅链逾重行李配置获取失败getChainskyConfigKey, error = {}", chainConfig);
            throw new BusinessException(ConsExceptionCode.CHAIN_LUGGAGE_ORDER_CONFIG_ERROR);
        }
        log.info("读取的配置信息:{}", chainConfig);
        ChainPropertiesDto chainPropertiesDto = chainConfig.getData().toJavaObject(ChainPropertiesDto.class);
        if(Objects.isNull(chainPropertiesDto)){
            throw new BusinessException(ConsExceptionCode.CHAIN_LUGGAGE_ORDER_CONFIG_ERROR);
        }
        return chainPropertiesDto;
    }

    /**
     * Title：getDecodedValue
     * Description：获取解码后的值(不包含content)
     * author：李军呈
     * date：2025/2/25 09:29
     * @param value:
     * return: java.lang.String
     */
    public static String getDecodedValue(String value) {
        log.info("第一次解码前 value={}", value);
        byte[] firstDecodedBytes = Base64.getDecoder().decode(value);
        String firstDecodedString = new String(firstDecodedBytes);
        log.info("第一次解码后 firstDecodedString={}", firstDecodedString);
        byte[] secondDecodedBytes = Base64.getDecoder().decode(firstDecodedString);
        String secondDecodedString = new String(secondDecodedBytes);
        log.info("第二次解码后 secondDecodedString={}", secondDecodedString);
        return secondDecodedString;
    }

    /**
     * Title：getDecodedContentValue
     * Description：获取content解码后的值
     * author：李军呈
     * date：2025/2/25 09:29
     * @param value:
     * return: byte[]
     */
    public static byte[] getDecodedContentValue(String value) {
        log.info("第一次解码前 Content={}", value);
        byte[] firstDecodedBytes = Base64.getDecoder().decode(value);
        String firstDecodedString = new String(firstDecodedBytes);
        log.info("第一次解码后 Content={}", firstDecodedString);
        return Base64.getDecoder().decode(firstDecodedString);
    }

    /**
     * Title：getDecodedValueOne
     * Description：获取解码后的值- 解码一次
     * author：李军呈
     * date：2025/3/24 11:55
     * @param value: 值
     * return: java.lang.String
     */
    public static String getDecodedValueOne(String value) {
        log.info("第一次解码前 value={}", value);
        byte[] firstDecodedBytes = Base64.getDecoder().decode(value);
        String firstDecodedString = new String(firstDecodedBytes);
        log.info("第一次解码后 firstDecodedString={}", firstDecodedString);
        return firstDecodedString;
    }

    /**
     * Title：Result
     * Description：出参
     * author：李军呈
     * date：2025/2/25 09:30
     */
    @Getter
    public static class Result {
        private final Map<String, String> params;
        private final byte[] contentByte;
        public Result(Map<String, String> params, byte[] contentByte) {
            this.params = params;
            this.contentByte = contentByte;
        }
    }

}

package com.swcares.aiot.bell.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * ClassName：com.swcares.aiot.ass.test.common.model.dto.MsgInfoPagedDTO <br>
 * Description：消息信息表 分页数据传输对象 <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2024-04-24 <br>
 * @since v1.0 <br>
 */
@Data
@Accessors(chain = true)
public class BelMsgPageDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "开始时间")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束时间")
    private LocalDate endDate;

    @ApiModelProperty(value = "业务类型：1账单生成、2 账单核对、3 核对反馈", example = "1")
    @NotNull(message = "消息类型不能为空")
    private String msgType;

    @ApiModelProperty(value = "是否已读", example = " ")
    private Boolean readStatus;


}

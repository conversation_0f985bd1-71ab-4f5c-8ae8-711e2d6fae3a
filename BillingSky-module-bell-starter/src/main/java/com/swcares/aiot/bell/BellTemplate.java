package com.swcares.aiot.bell;

import cn.hutool.core.date.DateUtil;
import com.swcares.aiot.bell.core.entity.MsgInfo;
import com.swcares.aiot.bell.model.dto.BelMsgBillCollateSaveDto;
import com.swcares.aiot.bell.model.dto.BelMsgBillProduceFailUpdaDto;
import com.swcares.aiot.bell.model.dto.BelMsgBillProduceSaveDto;
import com.swcares.aiot.bell.model.dto.BelMsgCollateFeedbackSaveDto;
import com.swcares.aiot.bell.service.IBellBizService;
import com.worm.hutool.DateUtils;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class BellTemplate {

    private static final String LOG_MSG_TEMPLATE = "{}:记录消息:{}";
    @Resource
    private IBellBizService iBellBizService;

    /**
     * 生成“账单生成”的消息
     *
     * @return 新增的所有消息
     */
    public List<MsgInfo> saveMsgBillProduce(BelMsgBillProduceSaveDto belMsgBillProduceSaveDto) {
        log.info(LOG_MSG_TEMPLATE, DateUtil.now(), "账单生成");
        return iBellBizService.saveMsgBillProduce(belMsgBillProduceSaveDto);
    }

    /**
     * 更新“账单生成”消息的状态-失败
     *
     * @param belMsgBillProduceFailUpdaDto 要更新的消息的id
     * @return 更新是否成功
     */
    public boolean updateMsgBillProduceFail(BelMsgBillProduceFailUpdaDto belMsgBillProduceFailUpdaDto) {
        log.info(LOG_MSG_TEMPLATE, DateUtil.now(), "更新“账单生成”消息的状态-失败");
        return iBellBizService.updateMsgBillProduceFail(belMsgBillProduceFailUpdaDto);
    }

    /**
     * 更新"账单生成"消息的状态-已完成
     *
     * @param msgInfos 要更新的消息的id
     * @return 更新是否成功
     */
    public Boolean updateMsgBillProduceComplete(List<MsgInfo> msgInfos) {
        log.info(LOG_MSG_TEMPLATE, DateUtil.now(), "更新“账单生成”消息的状态-已完成");
        return iBellBizService.updateMsgBillProduceComplete(msgInfos);
    }

    /**
     * 生成“账单核对”的消息
     *
     * @return 新增是否成功
     */
    public List<MsgInfo> saveMsgBillCollate(BelMsgBillCollateSaveDto belMsgBillCollateSaveDto) {
        log.info(LOG_MSG_TEMPLATE, DateUtil.now(), "账单核对");
        return iBellBizService.saveMsgBillCollate(belMsgBillCollateSaveDto);
    }

    /**
     * 生成“核对反馈”的消息
     *
     * @return 新增是否成功
     */
    public List<MsgInfo> saveMsgCollateFeedback(BelMsgCollateFeedbackSaveDto belMsgBillCollateSaveDto) {
        log.info(LOG_MSG_TEMPLATE, DateUtil.now(), "核对反馈");
        return iBellBizService.saveMsgCollateFeedback(belMsgBillCollateSaveDto);
    }
}

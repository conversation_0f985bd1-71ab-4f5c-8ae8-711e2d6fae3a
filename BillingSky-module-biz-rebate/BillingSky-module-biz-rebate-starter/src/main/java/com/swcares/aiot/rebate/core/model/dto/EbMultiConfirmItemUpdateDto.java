package com.swcares.aiot.rebate.core.model.dto;

import com.swcares.aiot.rebate.core.enums.EnumDataSection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class EbMultiConfirmItemUpdateDto {
    @ApiModelProperty(value = "离港返还账单确认项id")
    @NotNull(message = "确认项id不能为空")
    private Long itemId;

    @ApiModelProperty(value = "序号")
    private Integer serialNo;

    @ApiModelProperty(value = "业务编码")
    private String bizCode;

    @ApiModelProperty(value = "确认内容")
    @Length(min = 1, max = 100, message = "确认内容长度不能超过100字符")
    private String confirmContent;

    @ApiModelProperty(value = "所属角色名称")
    private String roleName;

    @ApiModelProperty(value = "所属角色编码")
    private String roleCode;

    @ApiModelProperty(value = "后续操作名称")
    @NotNull(message = "后续操作名称不能为空")
    private String operateName;

    @ApiModelProperty(value = "后续操作编码")
    @NotNull(message = "后续操作编码不能为空")
    private String operateCode;

    @ApiModelProperty(value = "部门id")
    private Long departmentId;

    @ApiModelProperty(value = "部门code")
    private String departmentCode;

    @ApiModelProperty(value = "归属部门")
    private String departmentName;

    @ApiModelProperty("数据范围")
    private EnumDataSection dataSection;
}

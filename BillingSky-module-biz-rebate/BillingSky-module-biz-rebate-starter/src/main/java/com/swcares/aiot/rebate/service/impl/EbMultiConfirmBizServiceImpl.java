package com.swcares.aiot.rebate.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.core.entity.EstimateBillConfirmItem;
import com.swcares.aiot.core.entity.EstimateBillFileAttachment;
import com.swcares.aiot.core.entity.EstimateBillProve;
import com.swcares.aiot.core.service.IEstimateBillConfirmItemService;
import com.swcares.aiot.core.service.IEstimateBillFileAttachmentService;
import com.swcares.aiot.core.service.IEstimateBillProveService;
import com.swcares.aiot.file.client.AttachmentClient;
import com.swcares.aiot.file.dto.DownloadZipFilesDto;
import com.swcares.aiot.rebate.core.cons.ConsRebate;
import com.swcares.aiot.rebate.core.enums.EnumOperateType;
import com.swcares.aiot.rebate.core.enums.EnumProveType;
import com.swcares.aiot.rebate.core.liteflow.ctx.*;
import com.swcares.aiot.rebate.core.model.dto.*;
import com.swcares.aiot.rebate.core.model.vo.EbMultiConfirmItemGetVo;
import com.swcares.aiot.rebate.core.model.vo.EbMultiConfirmPageVo;
import com.swcares.aiot.rebate.core.model.vo.EbOrgGetVo;
import com.swcares.aiot.rebate.core.utils.FileUtils;
import com.swcares.aiot.rebate.service.IEbMultiConfirmBizService;
import com.swcares.baseframe.common.exception.BusinessException;
import com.worm.hutool.HttpUtils;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class EbMultiConfirmBizServiceImpl implements IEbMultiConfirmBizService {
    @Resource
    private FlowExecutor flowExecutor;

    @Resource
    private IEstimateBillFileAttachmentService iEstimateBillFileAttachmentService;

    @Resource
    private AttachmentClient attachmentClient;
    @Resource
    private IEstimateBillConfirmItemService iEstimateBillConfirmItemService;
    @Resource
    private IEstimateBillProveService iEstimateBillProveService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveEbMultiConfirm(EbMultiConfirmSaveDto ebMultiConfirmSaveDto) throws BusinessException {
        // 执行流程
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("saveEbMultiConfirm", null, ebMultiConfirmSaveDto, new CtxEbMultiConfirm());
        boolean success = liteflowResponse.isSuccess();
        if (!success) {
            throw (BusinessException) liteflowResponse.getCause();
        }
        return true;
    }

    @Override
    public Page<EbMultiConfirmPageVo> pageEbMultiConfirm(EbMultiConfirmPageDto ebMultiConfirmPageDto) {
        CtxEbMultiConfirmPage ctxEbMultiConfirmPage = new CtxEbMultiConfirmPage().setEbMultiConfirmPageDto(ebMultiConfirmPageDto);
        // 执行流程
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("pageEbMultiConfirm", null, ctxEbMultiConfirmPage);
        // 分页对象
        return liteflowResponse.getContextBean(CtxEbMultiConfirmPage.class).getPage();
    }

    @Override
    public void download(EbMultiConfirmDownloadDto ebMultiConfirmDownloadDto, HttpServletResponse response) throws BusinessException {
        ebMultiConfirmDownloadDto.setResponse(response);
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("downloadEbMultiConfirm", null, ebMultiConfirmDownloadDto);
        boolean success = liteflowResponse.isSuccess();
        if (!success) {
            throw (BusinessException) liteflowResponse.getCause();
        }
    }

    @Override
    public boolean downloadCheck(EbMultiConfirmDownloadDto confirmDownloadDto, HttpServletResponse response) {
        // 获取流程入参
        Long estimateBillConfirmId = Long.parseLong(confirmDownloadDto.getEstimateBillConfirmId());
        //获取确认项信息
        List<EstimateBillConfirmItem> estimateBillConfirmItems = iEstimateBillConfirmItemService.lambdaQuery()
                .eq(EstimateBillConfirmItem::getEstimateBillConfirmId, estimateBillConfirmId)
                .list();
        List<DownloadZipFilesDto> fileKeyList = new ArrayList<>();
        for (EstimateBillConfirmItem estimateBillConfirmDetail : estimateBillConfirmItems) {
            // 获取确认项对应过程记录
            EstimateBillProve estimateBillProve = iEstimateBillProveService.lambdaQuery()
                    .eq(EstimateBillProve::getBizId, estimateBillConfirmDetail.getId())
                    .eq(EstimateBillProve::getOperateType, EnumOperateType.EB_MULTI_CONFIRM_UPLOAD.getCode())
                    .orderByDesc(EstimateBillProve::getCreatedBy)
                    .last(" limit 1 ")
                    .one();
            if (estimateBillProve != null) {
                //获取过程记录对应附件信息
                List<EstimateBillFileAttachment> estimateBillFileAttachments = iEstimateBillFileAttachmentService.lambdaQuery()
                        .eq(EstimateBillFileAttachment::getProveBizId, estimateBillProve.getId())
                        .list();
                for (EstimateBillFileAttachment estimateBillFileAttachment : estimateBillFileAttachments) {
                    DownloadZipFilesDto dto = new DownloadZipFilesDto();
                    dto.setFileId(Long.valueOf(estimateBillFileAttachment.getFileKey()));
                    fileKeyList.add(dto);
                }
            }
        }
        if (fileKeyList.isEmpty()) {
            throw new BusinessException(ConsRebate.CONS_ESTIMATE_BILL_CONFIRM_DOWNLOAD_EMPTY);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean upload(EbMultiConfirmUploadDto ebMultiConfirmUploadDto) throws BusinessException {
        CtxEbProve ctxEbProve = new CtxEbProve()
                .setBizId(ebMultiConfirmUploadDto.getId())
                .setType(EnumProveType.EB_MULTI_CONFIRM)
                .setRemarks("上传文件")
                .setOperateType(EnumOperateType.EB_MULTI_CONFIRM_UPLOAD);
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("upload", ebMultiConfirmUploadDto, ctxEbProve);
        boolean success = liteflowResponse.isSuccess();
        if (!success) {
            throw (BusinessException) liteflowResponse.getCause();
        }
        return true;
    }

    @Override
    public List<EbMultiConfirmItemGetVo> getEbMultiConfirmItems(Long id) {
        CtxEbMultiConfirmItemGet ctxEbMultiConfirmItemGet = new CtxEbMultiConfirmItemGet().setId(id);
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("getEbMultiConfirmItems", null, ctxEbMultiConfirmItemGet);
        return liteflowResponse.getContextBean(CtxEbMultiConfirmItemGet.class).getEbMultiConfirmItemGetVos();
    }

    @Override
    public Boolean saveEbMultiConfirmItem(EbMultiConfirmItemSaveDto ebMultiConfirmItemSaveDto) {
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("saveEbMultiConfirmItem", ebMultiConfirmItemSaveDto);
        if (!liteflowResponse.isSuccess()) {
            throw new BusinessException(ConsRebate.CONS_ESTIMATE_BILL_CONFIRM_ITEM_FAIL);
        }
        return true;
    }

    @Override
    public Boolean updateEbMultiConfirmItem(EbMultiConfirmItemUpdateDto ebMultiConfirmItemUpdateDto) {
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("updateEbMultiConfirmItem", ebMultiConfirmItemUpdateDto);
        if (!liteflowResponse.isSuccess()) {
            throw new BusinessException(ConsRebate.CONS_ESTIMATE_BILL_CONFIRM_ITEM_FAIL);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean terminateEbMultiConfirmItem(Long itemId) throws BusinessException {
        CtxEbProve ctxEbProve = new CtxEbProve()
                .setBizId(itemId)
                .setType(EnumProveType.EB_MULTI_CONFIRM)
                .setRemarks(EnumProveType.EB_MULTI_CONFIRM.getDesc())
                .setOperateType(EnumOperateType.EB_MULTI_CONFIRM_TERMINATE);
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("terminateEbMultiConfirmItem", null, ctxEbProve);
        if (!liteflowResponse.isSuccess()) {
            throw (BusinessException) liteflowResponse.getCause();
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmEbMultiConfirmItem(Long itemId) {
        CtxEbProve ctxEbProve = new CtxEbProve()
                .setBizId(itemId)
                .setType(EnumProveType.EB_MULTI_CONFIRM)
                .setRemarks(EnumProveType.EB_MULTI_CONFIRM.getDesc())
                .setOperateType(EnumOperateType.EB_MULTI_CONFIRM_CONFIRM);
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("confirmEbMultiConfirmItem", null, ctxEbProve);
        if (!liteflowResponse.isSuccess()) {
            throw new BusinessException(ConsRebate.CONS_ESTIMATE_BILL_CONFIRM_ITEM_FAIL);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean objectionEbMultiConfirmItem(EbMultiConfirmItemObjectionDto ebMultiConfirmItemObjectionDto) throws BusinessException {
        CtxEbProve ctxEbProve = new CtxEbProve()
                .setBizId(ebMultiConfirmItemObjectionDto.getId())
                .setType(EnumProveType.EB_MULTI_CONFIRM)
                .setRemarks(ebMultiConfirmItemObjectionDto.getRemark())
                .setOperateType(EnumOperateType.EB_MULTI_CONFIRM_OBJECTION);
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("objectionEbMultiConfirmItem", null, ebMultiConfirmItemObjectionDto, ctxEbProve);
        if (!liteflowResponse.isSuccess()) {
            throw (BusinessException) liteflowResponse.getCause();
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateEbMultiConfirm(EbMultiConfirmUpdateDto ebMultiConfirmUpdateDto) throws BusinessException {
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("updateEbMultiConfirm", ebMultiConfirmUpdateDto, CtxEbMultiConfirm.class);
        if (!liteflowResponse.isSuccess()) {
            throw (BusinessException) liteflowResponse.getCause();
        }
        return true;
    }

    @Override
    public List<EbOrgGetVo> getEbOrg() throws BusinessException {
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("getEbOrg", null, CtxEbMultiConfirm.class, CtxEbOrgGet.class);
        if (!liteflowResponse.isSuccess()) {
            throw (BusinessException) liteflowResponse.getCause();
        }
        return liteflowResponse.getContextBean(CtxEbOrgGet.class).getEbOrgGetVos();
    }

    @Override
    public void preview(String fileKey, Long proveBizId, HttpServletRequest request, HttpServletResponse response) {
        //检查key是否存在
        EstimateBillFileAttachment file = iEstimateBillFileAttachmentService.lambdaQuery()
                .eq(EstimateBillFileAttachment::getFileKey, fileKey)
                .eq(Objects.nonNull(proveBizId) && proveBizId != 0, EstimateBillFileAttachment::getProveBizId, proveBizId)
                .one();
        if (Objects.isNull(file)) {
            throw new BusinessException(ConsRebate.FILE_MISSING);
        }
        try (Response download = attachmentClient.download(Long.valueOf(file.getFileKey()), 0L);
             InputStream inputStream = download.body().asInputStream()) {
            String fileName = URLEncoder.encode(file.getOriginalName(), "utf-8");
            response.setHeader("Access-Control-Expose-Headers", HttpUtils.HEADER_CONTENT_DISPOSITION);
            response.setHeader(HttpUtils.HEADER_CONTENT_DISPOSITION, "attachment;filename=" + fileName);
            response.setContentType("application/force-download");
            response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
            FileUtils.writeFile(response, inputStream);
        } catch (IOException e) {
            log.error("文件下载失败!message:{}", e.getMessage(), e);
            throw new BusinessException(ConsRebate.FILE_DOWNLOAD_FAILED);
        }
    }

    @Override
    public Boolean isNotAdmin() {
        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("isNotAdmin", null, CtxEbMultiConfirmNotAdmin.class);
        return liteflowResponse.getContextBean(CtxEbMultiConfirmNotAdmin.class).getIsNotAdmin();
    }
}

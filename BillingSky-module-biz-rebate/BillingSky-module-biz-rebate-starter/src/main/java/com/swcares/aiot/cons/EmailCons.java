package com.swcares.aiot.cons;

public class EmailCons {

    private EmailCons() {
    }

    /**
     * 总部账单分发到分支
     */
    public static final String DISTRIBUTED_FROM_HEADQUARTERS_BRANCHES = "DISTRIBUTED_FROM_HEADQUARTERS_BRANCHES";

    /**
     * 总部提出异议到分支
     */
    public static final String HEADQUARTERS_RAISES_OBJECTIONS_BRANCHES = "HEADQUARTERS_RAISES_OBJECTIONS_BRANCHES";

    /**
     * 分支提交异议到总部
     */
    public static final String BRANCH_SUBMITS_OBJECTIONS_HEADQUARTERS = "BRANCH_SUBMITS_OBJECTIONS_HEADQUARTERS";

    /**
     * 分支确认发票
     */
    public static final String BRANCH_CONFIRMS_INVOICE = "BRANCH_CONFIRMS_INVOICE";

    /**
     * 提交报销失败
     */
    public static final String SUBMISSION_REIMBURSEMENT_FAILED = "SUBMISSION_REIMBURSEMENT_FAILED";

    /**
     * 邮箱字典
     */
    public static final String EMAIL_DICT = "EMAIL_DICT";
}

package com.swcares.aiot.rebate.core.model.vo;

import com.swcares.aiot.core.entity.EstimateBillFileAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title ：EstimateBillProveVO <br>
 * Package ：com.swcares.aiot.core.model.vo <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 * Description : 离港返还账单存证记录明细 <br>
 *
 * <AUTHOR> <br>
 * date 2024年 09月03日 13:28 <br>
 * @version v1.0 <br>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "EstimateBillProveVo", description = "离港返还账单存证记录明细")
public class EstimateBillProveVo implements Serializable {

    private static final long serialVersionUID = -6679691172928670408L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "离岗返还账单表id")
    private Long estimateBillId;

    @ApiModelProperty(value = "文件类型（1=非host-app 2=非host-多主机 3=host 4=账单溯源 5=发票 默认0=其他）")
    private Integer type;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "操作类型(1=上传账单)")
    private Integer operateType;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "操作记录附件")
    private List<EstimateBillFileAttachment> fileList;

    private String businessType;
}

package com.swcares.aiot.rebate.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.validation.ValidationUtil;
import com.swcares.aiot.core.entity.EstimateBillFileAttachment;
import com.swcares.aiot.core.entity.EstimateBillProve;
import com.swcares.aiot.core.service.IEstimateBillFileAttachmentService;
import com.swcares.aiot.core.service.IEstimateBillProveService;
import com.swcares.aiot.rebate.core.enums.EnumOperateType;
import com.swcares.aiot.rebate.core.enums.EnumProveType;
import com.swcares.aiot.rebate.core.mapstruct.MsEbProve;
import com.swcares.aiot.rebate.core.model.dto.EstimateBillProveDto;
import com.swcares.aiot.rebate.core.model.vo.EstimateBillProveVo;
import com.swcares.aiot.rebate.mapper.EstimateBillProveBizMapper;
import com.swcares.aiot.rebate.service.IEbProveBizService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> <br>
 * date 2024年 09月03日 11:50 <br>
 * @version v1.0 <br>
 */
@Service
public class EbProveBizServiceImpl implements IEbProveBizService {
    @Resource
    private IEstimateBillProveService iEstimateBillProveService;
    @Resource
    private EstimateBillProveBizMapper estimateBillProveBizMapper;
    @Resource
    private IEstimateBillFileAttachmentService iEstimateBillFileAttachmentService;
    @Resource
    private MsEbProve msEbProve;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EstimateBillProve addProveRecord(EstimateBillProveDto estimateBillProveDto) {
        //必填校验
        ValidationUtil.validate(estimateBillProveDto);

        EstimateBillProve estimateBillProve = msEbProve.dto2bean(estimateBillProveDto);
        if (Objects.isNull(estimateBillProve.getType())) {
            estimateBillProve.setType(EnumProveType.OTHER.getCode());
        }
        if (ObjectUtil.isNotEmpty(estimateBillProveDto.getType())) {
            estimateBillProve.setType(estimateBillProveDto.getType().getCode());
        }
        if (ObjectUtil.isNotEmpty(estimateBillProveDto.getOperateType())) {
            estimateBillProve.setOperateType(estimateBillProveDto.getOperateType().getCode());
        }

        //保存操作记录
        iEstimateBillProveService.save(estimateBillProve);
        //处理附件
        this.assFileByFileKeyList(estimateBillProveDto.getFileKeyList(), estimateBillProve.getId());
        return estimateBillProve;
    }

    @Override
    public void assFileByFileKeyList(List<String> currentFileKeys, Long proveBizId) {
        if (this.clearEbFileAttachments(currentFileKeys, proveBizId)) {
            return;
        }
        // 为上传文件 添加 业务id
        iEstimateBillFileAttachmentService.lambdaUpdate()
                .in(EstimateBillFileAttachment::getFileKey, currentFileKeys)
                .set(EstimateBillFileAttachment::getDeleted, Boolean.FALSE)
                .set(EstimateBillFileAttachment::getProveBizId, proveBizId)
                .update();
    }

    @Override
    public boolean clearEbFileAttachments(List<String> currentFileKeys, Long proveBizId) {
        //  文件列表 （根据 业务类型 + 业务ID）
        List<EstimateBillFileAttachment> estimateBillFileAttachments = iEstimateBillFileAttachmentService.lambdaQuery()
                .eq(EstimateBillFileAttachment::getDeleted, Boolean.FALSE)
                .eq(EstimateBillFileAttachment::getProveBizId, proveBizId)
                .list();
        // 传入key为空时 将数据库中的文件删除
        if (CollectionUtils.isEmpty(currentFileKeys)) {
            estimateBillFileAttachments.forEach(estimateBillFileAttachment -> estimateBillFileAttachment.setDeleted(Boolean.TRUE));
            iEstimateBillFileAttachmentService.updateBatchById(estimateBillFileAttachments);
            return true;
        }
        return false;
    }

    @Override
    public List<EstimateBillProveVo> listVoByBillId(Long estimateBillId) {
        List<EstimateBillProveVo> estimateBillProveVoList = estimateBillProveBizMapper.retrEstimateBillProveDetail(estimateBillId);
        for (EstimateBillProveVo estimateBillPageVo : estimateBillProveVoList) {
            estimateBillPageVo.setFileList(iEstimateBillFileAttachmentService.lambdaQuery()
                    .eq(EstimateBillFileAttachment::getProveBizId, estimateBillPageVo.getId())
                    .eq(EstimateBillFileAttachment::getDeleted, Boolean.FALSE)
                    .list());
        }
        return estimateBillProveVoList;
    }

    @Override
    public List<EstimateBillFileAttachment> getEstimateBillFileAttachmentByBizId(Long bizId, Integer type) {
        List<EstimateBillFileAttachment> estimateBillFileAttachmentList = new ArrayList<>();
        List<EstimateBillProve> estimateBillProveList = iEstimateBillProveService.lambdaQuery()
                .eq(EstimateBillProve::getBizId, bizId)
                .eq(type != null, EstimateBillProve::getOperateType, type)
                .orderByDesc(EstimateBillProve::getCreatedTime).list();
        if (!estimateBillProveList.isEmpty()) {
            EstimateBillProve estimateBillProve = estimateBillProveList.get(0);
            estimateBillFileAttachmentList = iEstimateBillFileAttachmentService.lambdaQuery()
                    .eq(EstimateBillFileAttachment::getProveBizId, estimateBillProve.getId())
                    .eq(EstimateBillFileAttachment::getDeleted, Boolean.FALSE)
                    .list();
        }
        return estimateBillFileAttachmentList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncProveRecord(List<EstimateBillProveVo> estimateBillProveVos) {
        //查询已有的
        List<EstimateBillProve> dbEstimateBillProves = iEstimateBillProveService.listByIds(estimateBillProveVos.stream()
                .map(EstimateBillProveVo::getId).collect(Collectors.toList()));
        List<Long> existsDbIds = dbEstimateBillProves.stream().map(EstimateBillProve::getId).collect(Collectors.toList());
        //过滤新增的存储记录
        List<EstimateBillProveVo> newProveVos = estimateBillProveVos.stream()
                .filter(item -> !existsDbIds.contains(item.getId())).collect(Collectors.toList());
        //新存储记录落库
        List<EstimateBillProve> estimateBillProves = msEbProve.vo2bean(newProveVos);
        iEstimateBillProveService.saveBatch(estimateBillProves);
        //新存储记录附件保存
        List<EstimateBillFileAttachment> newAttachmentList = newProveVos.stream()
                .flatMap(bill -> Optional.ofNullable(bill.getFileList())
                        .map(Collection::stream)
                        .orElseGet(Stream::empty))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(newAttachmentList)) {
            iEstimateBillFileAttachmentService.saveBatch(newAttachmentList);
        }
    }

    @Override
    public ArrayList<Long> notExitsDetailsBills(List<String> ids) {
        List<EstimateBillProve> proves = iEstimateBillProveService.lambdaQuery().in(EstimateBillProve::getBizId, ids)
                .in(EstimateBillProve::getType, Arrays.asList(EnumProveType.HOST_AVIATION.getCode(), EnumProveType.UN_HOST_APP.getCode(), EnumProveType.UN_HOST_MANY.getCode()))
                .list();
        ArrayList<Long> bills = new ArrayList<>();
        Map<Long, Set<Integer>> map = proves.stream().collect(Collectors.groupingBy(EstimateBillProve::getBizId, Collectors.mapping(EstimateBillProve::getType, Collectors.toSet())));
        ids.forEach(id -> {
            Long i = Long.valueOf(id);
            if (!map.containsKey(i)) {
                bills.add(i);
            }
        });
        return bills;
    }

    @Override
    public Integer headquartersObjectionQuantity(Long estimateBillId) {
        return Math.toIntExact(iEstimateBillProveService.lambdaQuery()
                .eq(EstimateBillProve::getBizId, estimateBillId)
                .eq(EstimateBillProve::getOperateType, EnumOperateType.HEADQUARTER_DISAGREEMENT.getCode())
                .eq(EstimateBillProve::getDeleted, Boolean.FALSE).count());
    }
}

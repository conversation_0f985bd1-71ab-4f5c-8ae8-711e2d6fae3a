package com.swcares.aiot.rebate.core.liteflow.ctx;

import com.swcares.aiot.core.model.dto.ErDailyBillPageDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * date 2025年04月18日10:06
 */
@ApiModel(value = "每日账单下载")
@Accessors(chain = true)
@Data
public class CtxErDailyBillDownload {
    @ApiModelProperty("查询参数")
    private ErDailyBillPageDto erDailyBillPageDto;
    @ApiModelProperty(value = "响应")
    private HttpServletResponse response;
}

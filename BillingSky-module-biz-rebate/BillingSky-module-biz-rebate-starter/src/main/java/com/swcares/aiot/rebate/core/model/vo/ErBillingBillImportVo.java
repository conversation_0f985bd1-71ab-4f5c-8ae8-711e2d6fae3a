package com.swcares.aiot.rebate.core.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ClassName：ErBillingBillImportVo
 * Description：billing账单导入Vo
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2025/5/8 15:06
 * Version v1.0
 */
@Data
@Accessors(chain = true)
public class ErBillingBillImportVo {

    @ApiModelProperty("文件数据总数")
    private Integer dataTotal;

    @ApiModelProperty("billing账单重复数")
    private Integer repeatNumber;
}

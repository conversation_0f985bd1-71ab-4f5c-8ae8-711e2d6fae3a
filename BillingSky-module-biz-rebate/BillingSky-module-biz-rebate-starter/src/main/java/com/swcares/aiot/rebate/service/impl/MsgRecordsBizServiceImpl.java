package com.swcares.aiot.rebate.service.impl;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.swcares.aiot.client.IMessageRecordsBizClient;
import com.swcares.aiot.model.dto.GetUnreadMessageCountDto;
import com.swcares.aiot.model.dto.MessageRecordsPageDto;
import com.swcares.aiot.model.dto.ReadMessageDto;
import com.swcares.aiot.model.dto.SendMessageDto;
import com.swcares.aiot.model.vo.MessageRecordsVo;
import com.swcares.aiot.rebate.core.model.dto.MessageRecordPageDto;
import com.swcares.aiot.rebate.service.IMsgRecordsBizService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MsgRecordsBizServiceImpl implements IMsgRecordsBizService {
    @Resource
    private IMessageRecordsBizClient iMessageRecordsBizClient;

    private static final String MSG_COLUMN = "send_time";

    private static final List<String> MESSAGE_TYPE = Arrays.asList("BELL_REBATE_FAIL","BELL_EXTERNAL_AWARD_FAIL");

    @Override
    public BaseResult<Integer> send(SendMessageDto sendMessageDto) {
        return iMessageRecordsBizClient.send(sendMessageDto);
    }

    @Override
    public PagedResult<List<MessageRecordsVo>> page(MessageRecordPageDto pageDto) {
        OrderItem orderItem = new OrderItem();
        orderItem.setAsc(Boolean.FALSE);
        orderItem.setColumn(MSG_COLUMN);
        MessageRecordsPageDto dto = new MessageRecordsPageDto();
        dto.setMessageTypes(MESSAGE_TYPE);
        dto.setTenantId(TenantHolder.getTenant());
        dto.setReceiver(Objects.nonNull(UserContext.getCurrentUser().getEmployeeId()) ?
                        UserContext.getCurrentUser().getEmployeeId():0);
        dto.setItems(Collections.singletonList(orderItem));
        dto.setPageNumber(pageDto.getCurrent());
        dto.setPageSize(pageDto.getSize());
        log.info("小铃铛消息分页入参{}", dto);
        return iMessageRecordsBizClient.page(dto);
    }

    @Override
    public BaseResult<Object> read(Long msgId) {
        ReadMessageDto dto = new ReadMessageDto()
                .setMessageTypes(MESSAGE_TYPE)
                .setTenantId(TenantHolder.getTenant())
                .setReceiver(Objects.nonNull(UserContext.getCurrentUser().getEmployeeId()) ?
                        UserContext.getCurrentUser().getEmployeeId():0);
        log.info("msgId不传，默认阅读所有的:{}", msgId);
        if (Objects.nonNull(msgId)) {
            dto.setMsgId(msgId);
        }
        log.info("小铃铛消息标记已读入参:{}", dto);
        return iMessageRecordsBizClient.readByCondition(dto);
    }

    @Override
    public BaseResult<Integer> getMessageUnreadCount() {
        GetUnreadMessageCountDto dto = new GetUnreadMessageCountDto()
                .setMessageTypes(MESSAGE_TYPE)
                .setTenantId(TenantHolder.getTenant())
                .setReceiver(Objects.nonNull(UserContext.getCurrentUser().getEmployeeId()) ?
                        UserContext.getCurrentUser().getEmployeeId():0);
        log.info("小铃铛消息未读数量入参{}", dto);
        return iMessageRecordsBizClient.getUnreadCount(dto);
    }

}

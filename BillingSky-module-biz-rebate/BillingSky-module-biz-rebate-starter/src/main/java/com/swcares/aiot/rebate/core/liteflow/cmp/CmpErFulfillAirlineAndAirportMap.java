package com.swcares.aiot.rebate.core.liteflow.cmp;

import cn.hutool.http.HttpStatus;
import com.swcares.aiot.rebate.core.liteflow.ctx.CtxErBillFulFill;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.swcares.components.api.bd.BaseDataCommonRemoteService;
import com.swcares.components.bd.vo.AirlineInfoVO;
import com.swcares.components.bd.vo.AirportInfoComboVO;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aiot.rebate.core.liteflow.cmp.CmpErFulfillDailyBillAirlineAndAirportMap
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/4/14 21:07
 * @version v1.0
 */
@Slf4j
@LiteflowComponent(id = "CmpErFulfillAirlineAndAirportMap", name = "组件-离港返还-履约机场航司信息查询")
public class CmpErFulfillAirlineAndAirportMap extends NodeComponent {
    @Resource
    private BaseDataCommonRemoteService baseDataCommonRemoteService;

    @Override
    public void process() throws Exception {
        this.getContextBean(CtxErBillFulFill.class).setAirlineMap(getAirlineNameMap()).setAirportMap(getAirportMap());
    }

    private Map<Object, String> getAirlineNameMap() {
        Map<Object, String> airlineMap = new HashMap<>();
        BaseResult<List<AirlineInfoVO>> airlineCombo = baseDataCommonRemoteService.getAirlineCombo();
        log.info("远程调用获取航司信息:{}", airlineCombo);
        // 把航司二字码分装成map
        if (airlineCombo.getCode() == HttpStatus.HTTP_OK) {
            List<AirlineInfoVO> data = airlineCombo.getData();
            if (ObjectUtils.isNotEmpty(data)) {
                setAirlineInfo(data, airlineMap);
            }
        } else {
            new HashMap<>();
        }
        return airlineMap;
    }

    private void setAirlineInfo(List<AirlineInfoVO> data, Map<Object, String> airlineMap) {
        data.forEach(e -> {
            if (ObjectUtils.isNotEmpty(e.getAirlineAbbr())) {
                airlineMap.put(e.getAirline2code(), e.getAirlineAbbr());
            } else if (ObjectUtils.isNotEmpty(e.getAirlineName())) {
                airlineMap.put(e.getAirline2code(), e.getAirlineName());
            } else {
                airlineMap.put(e.getAirline2code(), e.getAirlineEn());
            }
        });
    }

    private Map<Object, String> getAirportMap() {
        Map<Object, String> airportMap = new HashMap<>();
        BaseResult<List<AirportInfoComboVO>> combo = baseDataCommonRemoteService.getCombo("");
        log.info("远程调用获取机场信息:{}", combo);
        if (combo.getCode() == HttpStatus.HTTP_OK) {
            List<AirportInfoComboVO> data = combo.getData();
            if (ObjectUtils.isNotEmpty(data)) {
                data.forEach(e -> airportMap.put(e.getCode(), e.getCityName()));
            }
        } else {
            return new HashMap<>();
        }
        return airportMap;
    }
}

package com.swcares.aiot.rebate.core.liteflow.cmp;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.swcares.aiot.client.IConfConfigBizClient;
import com.swcares.aiot.rebate.core.liteflow.ctx.CtxEbMultiConfirm;
import com.swcares.aiot.rebate.core.model.dto.EstimateBillConfirmItemRemoteConfigDto;
import com.swcares.baseframe.common.base.BaseResult;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@LiteflowComponent(value = "cmpEbMultiConfirmItemRemoteConfig", name = "组件-离港返还账期确认配置")
public class CmpEbMultiConfirmItemRemoteConfig extends NodeComponent {
    @Resource
    private IConfConfigBizClient iConfConfigBizClient;

    private static final String ESTIMATE_BILL_CONFIRM_ITEM = "ESTIMATE_BILL_CONFIRM_ITEM";

    @Override
    public void process() {
        // 原创获取配置信息
        BaseResult<JSONObject> br = iConfConfigBizClient.getConfig(ESTIMATE_BILL_CONFIRM_ITEM);
        JSONObject jsonObject = br.getData();
        // 配置转换为bean对象
        EstimateBillConfirmItemRemoteConfigDto estimateBillConfirmItemRemoteConfigDto = JSONUtil.toBean(jsonObject.toJSONString(), EstimateBillConfirmItemRemoteConfigDto.class);
        log.info("获取到的配置：{}", estimateBillConfirmItemRemoteConfigDto);
        // 获取上下文
        CtxEbMultiConfirm ctxEbMultiConfirm = this.getContextBean(CtxEbMultiConfirm.class);
        // 查询到的配置
        ctxEbMultiConfirm.setEstimateBillConfirmItemRemoteConfigDto(estimateBillConfirmItemRemoteConfigDto);
    }
}

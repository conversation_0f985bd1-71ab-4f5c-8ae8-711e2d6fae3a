package com.swcares.aiot.rebate.core.liteflow.cmp;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.swcares.aiot.client.IConfConfigBizClient;
import com.swcares.aiot.rebate.core.liteflow.ctx.CtxErDailyBillCalc;
import com.swcares.aiot.rebate.core.model.dto.ErDailyBillRemoteConfDto;
import com.swcares.baseframe.common.base.BaseResult;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 获取字符百分比·
 *
 * <AUTHOR>
 */
@Slf4j
@LiteflowComponent(value = "CmpErDailyBillCalcRemoteConf", name = "组件-离港返还-每日账单-远程配置")
public class CmpErDailyBillCalcRemoteConf extends NodeComponent {
    @Resource
    private IConfConfigBizClient iConfConfigBizClient;

    private static final String KEY = "PERCENT_PAY";

    @Override
    public void process() {
        log.info("执行组件：{}", this.getNodeId());
        // 原创获取配置信息
        BaseResult<JSONObject> br = iConfConfigBizClient.getConfig(KEY);
        JSONObject jsonObject = br.getData();
        // 配置转换为bean对象
        ErDailyBillRemoteConfDto erDailyBillRemoteConfDto = JSONUtil.toBean(jsonObject.toJSONString(), ErDailyBillRemoteConfDto.class);
        log.info("获取到的配置：{}", erDailyBillRemoteConfDto);
        // 获取上下文
        CtxErDailyBillCalc ctxErDailyBillCalc = this.getContextBean(CtxErDailyBillCalc.class);
        // 查询到的配置
        ctxErDailyBillCalc.setErDailyBillRemoteConfDto(erDailyBillRemoteConfDto);
    }
}

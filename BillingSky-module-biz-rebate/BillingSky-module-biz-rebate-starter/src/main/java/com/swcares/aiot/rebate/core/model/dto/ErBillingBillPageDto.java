package com.swcares.aiot.rebate.core.model.dto;

import com.swcares.aiot.core.model.dto.PageDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * ClassName：ErBillingBillPageDto
 * Description：billing账单分页 dto
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2025/4/16 14:14
 * Version v1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErBillingBillPageDto extends PageDto {

    private static final long serialVersionUID = -833068970029087755L;

    @ApiModelProperty(value = "机场三字码", example = "YBP")
    private String airportCode;

    @ApiModelProperty(value = "开始月份(YYYY-MM-DD)", example = " ")
    private LocalDate startBillMonth;

    @ApiModelProperty(value = "结束月份(YYYY-MM-DD)", example = " ")
    private LocalDate endBillMonth;

    @ApiModelProperty(value = "状态：1已变更、2已支付", example = " ")
    private List<Integer> status;
}

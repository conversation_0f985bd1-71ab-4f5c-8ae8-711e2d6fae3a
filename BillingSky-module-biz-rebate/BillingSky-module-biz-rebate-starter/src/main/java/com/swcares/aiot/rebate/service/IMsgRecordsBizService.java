package com.swcares.aiot.rebate.service;

import com.swcares.aiot.model.dto.SendMessageDto;
import com.swcares.aiot.model.vo.MessageRecordsVo;
import com.swcares.aiot.rebate.core.model.dto.MessageRecordPageDto;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IMsgRecordsBizService {

    BaseResult<Integer> send(SendMessageDto sendMessageDto);

    PagedResult<List<MessageRecordsVo>> page(MessageRecordPageDto pageDto);

    /**
     * Title：read
     * Description：消息标记已读
     * author：李军呈
     * date：2025/2/8 10:22
     * @param msgId : 消息id
     * return: com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    BaseResult<Object> read(Long msgId);

    /**
     * Title：getMessageUnreadCount
     * Description：获取消息未读数量
     * author：李军呈
     * date：2025/2/8 10:10
     * return: com.swcares.baseframe.common.base.BaseResult<java.lang.Integer>
     */
    BaseResult<Integer> getMessageUnreadCount();
}


package com.swcares.aiot.rebate.controller;

import com.swcares.aiot.file.vo.AttachmentVO;
import com.swcares.aiot.rebate.core.cons.ConsRebate;
import com.swcares.aiot.rebate.core.enums.EnumFileBusinessType;
import com.swcares.aiot.rebate.core.utils.FileUtils;
import com.swcares.aiot.rebate.service.IEbFileAttachBizService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Title ：FileAttachmentBizController <br>
 * Package ：com.swcares.aiot.controller <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * date 2024年 09月03日 19:29 <br>
 * @version v1.0 <br>
 */
@ApiVersion(ConsRebate.CONS_TRAVELSKY_REBATE_API_VERSION)
@RestController
@RequestMapping("/ebFileAttachBiz")
@Api(tags = "控制器-附件上传（发票+处理记录）")
public class EbFileAttachBizController {

    @Resource
    private IEbFileAttachBizService iEbFileAttachBizService;

    @PostMapping(value = "/upload", headers = "content-type=" + MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation(value = "文件上传", notes = "返回文件信息", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BaseResult<String> uploadFile(@RequestPart("file") MultipartFile file, @RequestParam EnumFileBusinessType businessType) {
        FileUtils.checkFile(file);
        String fileKey = iEbFileAttachBizService.uploadFile(file, businessType.getFileType());
        return BaseResult.ok(fileKey);
    }


    @PostMapping(value = "/uploadAttachment", headers = "content-type=" + MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation(value = "文件上传", notes = "返回文件信息", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BaseResult<AttachmentVO> uploadAttachment(@RequestPart("file") MultipartFile file, @RequestParam EnumFileBusinessType businessType) {
        FileUtils.checkFile(file);
        AttachmentVO attachmentVo = iEbFileAttachBizService.uploadAttachment(file, businessType.getFileType());
        return BaseResult.ok(attachmentVo);
    }

    @GetMapping("/download/{fileKey}/{proveBizId}")
    @ApiOperation(value = "下载文件", notes = "返回文件")
    public void downloadFile(HttpServletRequest request, HttpServletResponse response,
                             @ApiParam("需要下载的文件的key") @PathVariable("fileKey") String fileKey,
                             @ApiParam("主键id") @PathVariable(value = "proveBizId") Long proveBizId) throws IOException {
        iEbFileAttachBizService.downloadFile(fileKey, proveBizId, request, response);
        response.flushBuffer();
    }

    @GetMapping("/downloadTwo/{fileKey}")
    @ApiOperation(value = "下载文件", notes = "返回文件")
    public void downloadFileTwo(HttpServletRequest request, HttpServletResponse response,
                              @ApiParam("需要下载的文件的key") @PathVariable("fileKey") String fileKey,
                              @ApiParam("主键id") @RequestParam(value = "originalName") String originalName) throws IOException {
        iEbFileAttachBizService.downloadFileTwo(fileKey, originalName, request, response);
        response.flushBuffer();
    }
}

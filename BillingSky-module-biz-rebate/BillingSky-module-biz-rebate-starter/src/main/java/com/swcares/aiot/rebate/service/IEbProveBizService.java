package com.swcares.aiot.rebate.service;


import com.swcares.aiot.core.entity.EstimateBillFileAttachment;
import com.swcares.aiot.core.entity.EstimateBillProve;
import com.swcares.aiot.rebate.core.model.dto.EstimateBillProveDto;
import com.swcares.aiot.rebate.core.model.vo.EstimateBillProveVo;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title ：IEstimateBillProveBizService <br>
 * Package ：com.swcares.aiot.service <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 * Description : 存证记录查询详情 <br>
 *
 * <AUTHOR> <br>
 * date 2024年 09月03日 11:41 <br>
 * @version v1.0 <br>
 */
public interface IEbProveBizService {

    EstimateBillProve addProveRecord(EstimateBillProveDto dto);


    /**
     * 通过离港返还账单id获取存证记录与对应的文件列表
     *
     * @param estimateBillId 账单id
     * @return 存证记录集合
     */
    List<EstimateBillProveVo> listVoByBillId(Long estimateBillId);

    /**
     * Title : syncProveRecord <br>
     * Description : 同步存证记录 <br>
     *
     * @param proveVOList :
     * author zhang_qiang  <br>
     * date 2024/9/4 10:09<br>
     */
    void syncProveRecord(List<EstimateBillProveVo> proveVOList);

    ArrayList<Long> notExitsDetailsBills(List<String> ids);

    /**
     * Title：headquartersObjectionQuantity
     * Description：总部提异议次数
     * author：李军呈
     * date： 2024/12/20 10:33
     * @param estimateBillId 账单id
     * @return Integer
     */
    Integer headquartersObjectionQuantity(Long estimateBillId);

    /**
     * Title：getEstimateBillFileAttachmentByTableId
     * Description：通过文件表的关联id查询文件list
     * author：刘志恒
     * date： 2025/01/08 14:33
     * @param bizId 文件表的关联id
     * @param type 类型
     * @return Integer
     */
    List<EstimateBillFileAttachment> getEstimateBillFileAttachmentByBizId(Long bizId, Integer type);


    void assFileByFileKeyList(List<String> currentFileKeys, Long proveBizId);

    boolean clearEbFileAttachments(List<String> currentFileKeys, Long proveBizId);
}

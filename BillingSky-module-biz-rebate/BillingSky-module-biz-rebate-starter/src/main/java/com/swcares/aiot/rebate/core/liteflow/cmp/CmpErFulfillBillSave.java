package com.swcares.aiot.rebate.core.liteflow.cmp;

import com.swcares.aiot.core.entity.EstimateRebeatDailyBill;
import com.swcares.aiot.core.entity.EstimateRebeatMonthlyBill;
import com.swcares.aiot.core.service.IEstimateRebeatDailyBillService;
import com.swcares.aiot.core.service.IEstimateRebeatMonthlyBillService;
import com.swcares.aiot.enums.DeletedEnum;
import com.swcares.aiot.rebate.core.enums.EnumBillType;
import com.swcares.aiot.rebate.core.liteflow.ctx.CtxErBillFulFill;
import com.swcares.aiot.statemachine.biz.status.EnumErBillStatus;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * ClassName：com.swcares.aiot.rebate.core.liteflow.cmp.CmpErFulfillDailyBillSave
 * Description：保存履约后的账单
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/4/14 21:28
 * @version v1.0
 */
@Slf4j
@LiteflowComponent(id = "CmpErFulfillBillSave", name = "组件-离港返还-保存履约后的账单")
public class CmpErFulfillBillSave extends NodeComponent {
    @Resource
    private IEstimateRebeatDailyBillService estimateRebeatDailyBillService;
    @Resource
    private IEstimateRebeatMonthlyBillService estimateRebeatMonthlyBillService;

    @Override
    public void process() throws Exception {
        CtxErBillFulFill ctxErBillFulFill = this.getContextBean(CtxErBillFulFill.class);
        String billingType = ctxErBillFulFill.getBillingType();
        if (EnumBillType.DAILY_BILL.getCode().equals(billingType)) {
            saveDailyBill();
        } else {
            saveMonthlyBill();
        }
    }

    private void saveMonthlyBill() {
        //保存履约月账单
        EstimateRebeatMonthlyBill estimateRebeatMonthlyBill =this.getCurrLoopObj();
        log.info("履约保存数据：{}", estimateRebeatMonthlyBill);
        //检查当前月账单状态是否变化
        EstimateRebeatMonthlyBill oldMonthlyBill = estimateRebeatMonthlyBillService.lambdaQuery()
                .eq(EstimateRebeatMonthlyBill::getId, estimateRebeatMonthlyBill.getId())
                .eq(EstimateRebeatMonthlyBill::getDeleted, DeletedEnum.NORMAL.getValue()).one();
        if (oldMonthlyBill != null) {
            if (!Objects.equals(oldMonthlyBill.getStatus(), EnumErBillStatus.SUCCESS.getStatus())
                    && !Objects.equals(oldMonthlyBill.getStatus(), EnumErBillStatus.FAIL.getStatus())) {
                estimateRebeatMonthlyBillService.updateById(estimateRebeatMonthlyBill);
            }else{
                estimateRebeatMonthlyBillService.lambdaUpdate()
                        .eq(EstimateRebeatMonthlyBill::getId, estimateRebeatMonthlyBill.getId())
                        .set(EstimateRebeatMonthlyBill::getAdjustmentAmount, estimateRebeatMonthlyBill.getAdjustmentAmount())
                        .set(EstimateRebeatMonthlyBill::getPlanPayAmount, estimateRebeatMonthlyBill.getPlanPayAmount())
                        .set(EstimateRebeatMonthlyBill::getRemainingAdjustmentAmount, estimateRebeatMonthlyBill.getRemainingAdjustmentAmount())
                        .set(EstimateRebeatMonthlyBill::getHaveConfirm, estimateRebeatMonthlyBill.getHaveConfirm())
                        .update();
            }
        }else{
            estimateRebeatMonthlyBillService.save(estimateRebeatMonthlyBill);
        }
    }

    private void saveDailyBill() {
        //保存成功履约日账单
        EstimateRebeatDailyBill estimateRebeatDailyBill =this.getCurrLoopObj();
        log.info("履约保存数据：{}", estimateRebeatDailyBill);
        //检查当前账单状态是否变化
        EstimateRebeatDailyBill rebeatDailyBill = estimateRebeatDailyBillService.lambdaQuery()
                .eq(EstimateRebeatDailyBill::getId, estimateRebeatDailyBill.getId())
                .eq(EstimateRebeatDailyBill::getDeleted, DeletedEnum.NORMAL.getValue()).one();
        if (rebeatDailyBill != null) {
            if (!Objects.equals(rebeatDailyBill.getStatus(), EnumErBillStatus.SUCCESS.getStatus())
                    && !Objects.equals(rebeatDailyBill.getStatus(), EnumErBillStatus.FAIL.getStatus())) {
                estimateRebeatDailyBillService.updateById(estimateRebeatDailyBill);
            }else{
                estimateRebeatDailyBillService.lambdaUpdate()
                        .eq(EstimateRebeatDailyBill::getId, estimateRebeatDailyBill.getId())
                        .set(EstimateRebeatDailyBill::getAdjustmentAmount, estimateRebeatDailyBill.getAdjustmentAmount())
                        .set(EstimateRebeatDailyBill::getPlanPayAmount, estimateRebeatDailyBill.getPlanPayAmount())
                        .set(EstimateRebeatDailyBill::getRemainingAdjustmentAmount, estimateRebeatDailyBill.getRemainingAdjustmentAmount())
                        .update();
            }
        }else{
            estimateRebeatDailyBillService.save(estimateRebeatDailyBill);
        }
    }
}

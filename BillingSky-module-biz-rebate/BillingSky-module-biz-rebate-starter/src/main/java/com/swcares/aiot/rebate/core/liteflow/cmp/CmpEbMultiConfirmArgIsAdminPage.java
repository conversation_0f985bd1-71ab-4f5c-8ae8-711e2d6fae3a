package com.swcares.aiot.rebate.core.liteflow.cmp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.swcares.aiot.client.IConfConfigBizClient;
import com.swcares.aiot.rebate.core.liteflow.ctx.CtxEbMultiConfirmPage;
import com.swcares.aiot.rebate.core.model.dto.EbConfirmItemRoleDto;
import com.swcares.aiot.rebate.core.model.dto.EbConfirmRoleRemoteConfigDto;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.security.UserContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@LiteflowComponent(id = "cmpEbMultiConfirmArgIsAdminPage", name = "组件-补充账期管理员标识")
public class CmpEbMultiConfirmArgIsAdminPage extends NodeComponent {
    @Resource
    private IConfConfigBizClient iConfConfigBizClient;
    private static final String ESTIMATE_BILL_CONFIRM_ROLE = "ESTIMATE_BILL_CONFIRM_ROLE";

    private static final String ROLE_CODE_EB_ADMIN = "ROLE_CODE_EB_ADMIN";

    @Override
    public void process() throws Exception {
        // 上下文
        CtxEbMultiConfirmPage ctxEbMultiConfirmPage = this.getContextBean(CtxEbMultiConfirmPage.class);
        // 原创获取配置信息
        BaseResult<JSONObject> br = iConfConfigBizClient.getConfig(ESTIMATE_BILL_CONFIRM_ROLE);
        JSONObject jsonObject = br.getData();
        // 配置转换为bean对象
        EbConfirmRoleRemoteConfigDto ebConfirmRoleRemoteConfigDto = JSONUtil.toBean(jsonObject.toJSONString(), EbConfirmRoleRemoteConfigDto.class);
        log.info("获取到的配置：{}", ebConfirmRoleRemoteConfigDto);
        List<EbConfirmItemRoleDto> ebConfirmItemRoleDtos = ebConfirmRoleRemoteConfigDto.getEbConfirmItemRoleDtos();
        // 获取账期管理人员的角色id
        EbConfirmItemRoleDto ebConfirmItemRoleDto = ebConfirmItemRoleDtos.stream().filter(ebCirDto -> ebCirDto.getRoleCode().equalsIgnoreCase(ROLE_CODE_EB_ADMIN)).findFirst().orElse(null);
        // 角色列表id集合
        List<Long> roleIds = UserContext.getCurrentUser().getRoleIds();
        // 管理员就什么也不做
        boolean isAdmin = ObjectUtil.isNotNull(ebConfirmItemRoleDto) && CollUtil.isNotEmpty(roleIds) && roleIds.contains(ebConfirmItemRoleDto.getRoleId());
        // 是否账期管理员
        ctxEbMultiConfirmPage.setIsAdmin(isAdmin);
        log.info("是否为账期管理人员:{}", isAdmin);
    }
}

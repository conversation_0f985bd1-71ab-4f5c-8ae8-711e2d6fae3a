package com.swcares.aiot.rebate.core.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
public class EbMultiConfirmSaveDto {
    @ApiModelProperty(value = "账单周期名称", example = "2023")
    @NotNull(message = "【账单周期名称】不能为空")
    @Length(max = 10, message = "账单周期名称】不能超过10个字符")
    private String paymentPeriod;

    @NotNull(message = "【账单覆盖日期开始时间】不能为空")
    @ApiModelProperty(value = "账单覆盖日期开始时间", example = "2024-12-01")
    private LocalDate startTime;

    @NotNull(message = "【账单覆盖日期结束时间】不能为空")
    @ApiModelProperty(value = "账单覆盖日期结束时间", example = "2024-12-31")
    private LocalDate endTime;
}

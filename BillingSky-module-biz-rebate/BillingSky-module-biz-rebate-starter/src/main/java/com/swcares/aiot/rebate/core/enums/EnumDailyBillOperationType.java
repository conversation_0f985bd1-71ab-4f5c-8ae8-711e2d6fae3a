package com.swcares.aiot.rebate.core.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.swcares.baseframe.common.enums.JsonEnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ClassName：com.swcares.aiot.rebate.core.enums.EnumDailyBillOperationType
 * Description：日账单操作枚举
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/3/18 14:32
 * @version v1.0
 */
@AllArgsConstructor
@Getter
public enum EnumDailyBillOperationType {

    CALC_DAILY_BILL("CALC_DAILY_BILL","汇总离港数据"),
    SAVE_DAILY_BILL("SAVE_DAILY_BILL","添加汇总离港数据"),
    FULFILL_DAILY_BILL("FULFILL_DAILY_BILL","发起履约"),
    CONFIRM_DAILY_BILL("CONFIRM_DAILY_BILL","确认履约"),
    PAY_SUCCESS("PAY_SUCCESS","支付成功"),
    PAY_FAIL("PAY_FAIL","支付失败");


    @JsonEnumValue
    @EnumValue
    private final String code;
    private final String desc;
}

package com.swcares.aiot.rebate.core.liteflow.cmp;

import com.swcares.aiot.rebate.core.liteflow.ctx.CtxEbMultiConfirmItemGet;
import com.swcares.aiot.rebate.core.model.vo.EbMultiConfirmItemGetVo;
import com.swcares.aiot.rebate.mapper.EbMultiConfirmBizMapper;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@LiteflowComponent(id = "cmpEbMultiConfirmItemGet", name = "组件-查看审核项列表")
public class CmpEbMultiConfirmItemGet extends NodeComponent {
    @Resource
    private EbMultiConfirmBizMapper ebMultiConfirmBizMapper;

    @Override
    public void process() {
        // 获取流程入参 确认主账单id
        CtxEbMultiConfirmItemGet ctxEbMultiConfirmItemGet = this.getContextBean(CtxEbMultiConfirmItemGet.class);
        // 查询账单列表
        List<EbMultiConfirmItemGetVo> ebMultiConfirmItemGetVos = ebMultiConfirmBizMapper.getEbMultiConfirmItems(ctxEbMultiConfirmItemGet);
        // 返回数据
        ctxEbMultiConfirmItemGet.setEbMultiConfirmItemGetVos(ebMultiConfirmItemGetVos);
    }
}

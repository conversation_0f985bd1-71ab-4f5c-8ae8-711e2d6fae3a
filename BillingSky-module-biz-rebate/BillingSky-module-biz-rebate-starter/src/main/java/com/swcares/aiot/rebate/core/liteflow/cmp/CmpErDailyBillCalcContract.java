package com.swcares.aiot.rebate.core.liteflow.cmp;

import com.swcares.aiot.core.entity.EstimateRebeatContract;
import com.swcares.aiot.core.entity.EstimateRebeatDailyBill;
import com.swcares.aiot.core.service.IEstimateRebeatContractService;
import com.swcares.aiot.core.service.IEstimateRebeatDailyBillService;
import com.swcares.aiot.rebate.core.enums.EnumBillType;
import com.swcares.aiot.rebate.core.enums.EnumContractStatus;
import com.swcares.aiot.rebate.core.liteflow.ctx.CtxErDailyBillCalc;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeIteratorComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@LiteflowComponent(value = "CmpErDailyBillCalcContract", name = "每日账单-获取合约")
public class CmpErDailyBillCalcContract extends NodeIteratorComponent {
    @Resource
    private IEstimateRebeatContractService iEstimateRebeatContractService;

    @Resource
    private IEstimateRebeatDailyBillService iEstimateRebeatDailyBillService;

    @Override
    public Iterator<EstimateRebeatContract> processIterator() {
        // 获取每日账单计算上下文
        CtxErDailyBillCalc ctxErDailyBillCalc = this.getContextBean(CtxErDailyBillCalc.class);
        // 每日账单id
        Long id = ctxErDailyBillCalc.getErDailyBillCalcDto().getId();
        log.info("每日账单id:{}", id);
        Long contractId = this.dailyBillId2ContractId(id);
        log.info("合约id:{}", contractId);
        // 要查询合约的机场列表
        List<String> airports = ctxErDailyBillCalc.getAirports();
        log.info("合约机场:{}", airports);
        // 调用合约服务，获取合约数据
        List<EstimateRebeatContract> estimateRebeatContracts = iEstimateRebeatContractService.lambdaQuery()
                .eq(contractId != null, EstimateRebeatContract::getId, contractId)
                .in(EstimateRebeatContract::getContractAirport, airports)
                .eq(EstimateRebeatContract::getBillingType, EnumBillType.DAILY_BILL.getCode())
                .eq(EstimateRebeatContract::getStatus, EnumContractStatus.AGREE.getValue())
                .list();
        // 返回合约数据的迭代器
        return estimateRebeatContracts.iterator();
    }

    /**
     * 每日账单id转合约id
     * @param id 每日账单id
     * @return 合约id
     */
    private Long dailyBillId2ContractId(Long id) {
        Long contractId = null;
        if (id != null) {
            EstimateRebeatDailyBill estimateRebeatDailyBill = iEstimateRebeatDailyBillService.getById(id);
            if (estimateRebeatDailyBill != null) {
                contractId = estimateRebeatDailyBill.getContractId();
            }
        }
        return contractId;
    }
}

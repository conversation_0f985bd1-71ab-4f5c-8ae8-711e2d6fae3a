package com.swcares.aiot.config;

import com.swcares.aiot.properties.RebateProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties({RebateProperties.class})
@ConditionalOnProperty(prefix = "swcares.rebate", name = "enabled", havingValue = "true", matchIfMissing = true)
public class RebateAutoConfiguration {
}

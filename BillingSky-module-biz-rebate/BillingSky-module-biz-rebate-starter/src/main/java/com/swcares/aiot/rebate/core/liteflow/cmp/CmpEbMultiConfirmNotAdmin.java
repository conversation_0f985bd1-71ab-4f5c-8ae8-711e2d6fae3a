package com.swcares.aiot.rebate.core.liteflow.cmp;

import cn.hutool.core.collection.CollUtil;
import com.swcares.aiot.rebate.core.liteflow.ctx.CtxEbMultiConfirmNotAdmin;
import com.swcares.aiot.rebate.core.model.dto.EbConfirmItemRoleDto;
import com.swcares.aiot.rebate.core.model.dto.EbConfirmRoleRemoteConfigDto;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.util.List;

/**
 * <AUTHOR>
 */
@LiteflowComponent(id = "cmpEbMultiConfirmNotAdmin", name = "组件-判定是否为账期确认人员")
public class CmpEbMultiConfirmNotAdmin extends NodeComponent {
    private static final String ROLE_CODE_EB_VERIFY = "ROLE_CODE_EB_VERIFY";

    @Override
    public void process() throws Exception {
        // 当前登录账号
        LoginUserDetails loginUserDetails = UserContext.getCurrentUser();
        // 当前账户的所有角色id集合
        List<Long> roleIds = loginUserDetails.getRoleIds();
        // 获取角色配置
        CtxEbMultiConfirmNotAdmin ctxEbMultiConfirmNotAdmin = this.getContextBean(CtxEbMultiConfirmNotAdmin.class);
        EbConfirmRoleRemoteConfigDto ebConfirmRoleRemoteConfigDto = ctxEbMultiConfirmNotAdmin.getEbConfirmRoleRemoteConfigDto();
        // 角色列表
        List<EbConfirmItemRoleDto> ebConfirmItemRoleDtos = ebConfirmRoleRemoteConfigDto.getEbConfirmItemRoleDtos();
        // 获取账期确认人员的角色id
        EbConfirmItemRoleDto ebConfirmItemRoleDto = ebConfirmItemRoleDtos.stream().filter(ebCirDto -> ebCirDto.getRoleCode().equalsIgnoreCase(ROLE_CODE_EB_VERIFY)).findFirst().orElse(null);
        // 判定是否为账期管理员
        if (ebConfirmItemRoleDto != null && CollUtil.isNotEmpty(roleIds) && roleIds.contains(ebConfirmItemRoleDto.getRoleId())) {
            ctxEbMultiConfirmNotAdmin.setIsNotAdmin(Boolean.TRUE);
        }
    }
}

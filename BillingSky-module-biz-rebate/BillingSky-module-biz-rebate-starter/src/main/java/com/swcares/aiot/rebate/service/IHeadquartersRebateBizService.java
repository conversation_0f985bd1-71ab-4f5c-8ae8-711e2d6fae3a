package com.swcares.aiot.rebate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.entity.EstimateBill;
import com.swcares.aiot.rebate.core.model.dto.*;
import com.swcares.aiot.rebate.core.model.vo.EstimateBillPageVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IHeadquartersRebateBizService {

    /**
     * 分页查询
     *
     * @param estimateBillPageDto :
     */
    IPage<EstimateBillPageVo> pageEstimateBill(EstimateBillPageDto estimateBillPageDto);

    /**
     * 详情
     *
     * @param id :
     * @return :
     */
    EstimateBill infoEstimateBill(String id);

    /**
     * 检查文件
     *
     * @param file :
     * @return :
     */
    Integer uploadFileCheck(MultipartFile file) throws IOException;

    /**
     * 上传文件数据保存
     *
     * @param file :
     * @return :
     */
    boolean uploadFileSave(MultipartFile file) throws IOException;

    /**
     * 创建excel模板
     */
    void templateDownload(HttpServletResponse response, String fullName) throws IOException;

    /**
     * 账单分发
     * author BrocadeLei
     *
     * @param headquartersRebateBillDistributeDto 请求对象
     * @return java.lang.Boolean
     */
    Boolean distributeHeadquartersRebateBill(HeadquartersRebateBillDistributeDto headquartersRebateBillDistributeDto);

    /***
     * 删除账单
     * author BrocadeLei
     * @param id 账单id
     * @return java.lang.Boolean
     */
    Boolean deleteEstimateBill(String id);

    /***
     * 编辑账单
     * author BrocadeLei
     * @param estimateBillUpdateDto 账单实体
     * @return java.lang.Boolean
     */
    Boolean updateEstimateBill(EstimateBillUpdateDto estimateBillUpdateDto);


    /**
     * 提交报销
     *
     * @param ids ：
     */
    void submitReimbursement(List<String> ids);

    /**
     * Title：uploadHostAviationFileCheck
     * Description：上传-HOST航账单文件校验
     * author：李军呈
     * date： 2024/8/28 10:54
     *
     * @param file :
     * @return java.lang.Integer
     */
    Integer uploadHostAviationFileCheck(MultipartFile file) throws IOException;

    /**
     * Title：uploadNotHostAviationAppFileCheck
     * Description：上传-非HOST航APP账单文件校验
     * author：李军呈
     * date： 2024/8/28 10:54
     *
     * @param file :
     * @return java.lang.Integer
     */
    Integer uploadNotHostAviationAppFileCheck(MultipartFile file, boolean isHostAviationApp) throws IOException;

    /**
     * Title：uploadHostAviationFileSave
     * Description：上传-HOST航账单文件保存
     * author：李军呈
     * date： 2024/8/30 13:08
     *
     * @param fileList :
     * @return boolean
     */
    boolean uploadHostAviationFileSave(List<MultipartFile> fileList) throws IOException;

    /**
     * Title：hostAviationFileDownload
     * Description：HOST航账单文件下载
     * author：李军呈
     * date： 2024/8/30 21:04
     *
     * @param dto      :
     * @param response :
     */
    void hostAviationFileDownload(HostAviationDownloadDto dto, HttpServletResponse response);

    /**
     * Title：hostAviationFileDownload
     * Description：HOST航账单文件预览
     * author：李军呈
     * date： 2024/8/30 21:04
     *
     * @param dto :
     */
    Map<String, Object> hostAviationFilePreview(HostAviationDownloadDto dto);

    void submitAbnormal(AbnormalSubmitDto abnormalSubmitDto);

    /**
     * 获取年度
     *
     * @return 年度列表
     */
    List<Integer> retrievePaymentPeriod();

    /**
     * 获取机场编码
     *
     * @param paymentPeriod 年度
     * @return 机场编码列表
     */
    List<String> retrieveAirportCode(Integer paymentPeriod);

    /**
     * 下载
     *
     * @param rebateDownloadDto 查询参数
     * @param response          响应对象
     */
    void downloadRebate(RebateDownloadDto rebateDownloadDto, HttpServletResponse response) throws IOException;

    /**
     * Title：invoiceDownloadRebate
     * Description：发票打包下载
     * author：李军呈
     * date： 2024/12/20 15:06
     * @param rebateDownloadDto dto
     * @param response 响应
     */
    void invoiceDownloadRebate(RebateDownloadDto rebateDownloadDto, HttpServletResponse response) throws IOException;

    void downloadRebateBizO(RebateDownloadDto rebateDownloadDto, HttpServletResponse response, List<EstimateBill> estimateBills) throws IOException;

    void updateReimbursementCompleted(String id);

    Boolean exitsDetails(List<String> ids);
}

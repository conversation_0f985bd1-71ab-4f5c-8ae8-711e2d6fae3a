package com.swcares.aiot.rebate.mapper;

import com.swcares.aiot.core.model.dto.ErMonthlyBillPageDto;
import com.swcares.aiot.core.model.vo.ErMonthlyBillAccVo;
import com.swcares.aiot.core.model.vo.ErMonthlyBillDownloadVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ErMonthlyBillBizMapper {
    /**
     * 统计月度账单数据
     *
     * @param erMonthlyBillPageDto 参数
     * @return 结果
     */
    ErMonthlyBillAccVo accErMonthlyBill(@Param("erMonthlyBillPageDto") ErMonthlyBillPageDto erMonthlyBillPageDto);

    /**
     * 查询要下载的月账单数据
     *
     * @param erMonthlyBillPageDto 参数
     * @return 结果
     */
    List<ErMonthlyBillDownloadVo> downloadErMonthlyBillData(@Param("erMonthlyBillPageDto") ErMonthlyBillPageDto erMonthlyBillPageDto);
}

package com.swcares.aiot.rebate.core.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * ClassName：com.swcares.aiot.rebate.core.model.vo.ErDailyBillHistoryVo
 * Description：日账单过程记录Vo
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/4/16 13:47
 * @version v1.0
 */
@Data
public class ErDailyBillHistoryVo {

    @ApiModelProperty(value = "操作类型（CALC_DAILY_BILL：汇总离港数据，SAVE_DAILY_BILL：添加汇总离港数据，FULFILL_DAILY_BILL：发起履约，CONFIRM_DAILY_BILL：确认履约，PAY_SUCCESS：支付成功，PAY_FAIL：支付失败）")
    private String operationType;

    @ApiModelProperty(value = "操作人")
    private String operationBy;

    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;


}

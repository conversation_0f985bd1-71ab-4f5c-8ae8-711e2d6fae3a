<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain id="saveEbMultiConfirmItemSub">
        SER(
        // 读取系统配置
        cmpEbMultiConfirmItemRemoteConfig,
        // 生成确认项
        cmpEbMultiConfirmItem);
    </chain>

    <chain id="saveEbMultiConfirm">
        // 主账单保存
        IF(cmpEbMultiConfirm,saveEbMultiConfirmItemSub);
    </chain>

    <chain id="pageEbMultiConfirm">
        SER(cmpEbMultiConfirmArgDepartmentCodePage,cmpEbMultiConfirmArgIsAdminPage,cmpEbMultiConfirmPage);
    </chain>
    <chain id="upload">
        SER(
        // 存证记录
        PRE(cmpEbProveSave),
        // 文件记录
        cmpEbMultiConfirmUpload
        );
    </chain>
    <chain id="downloadEbMultiConfirm">
        SER(cmpEbMultiConfirmDownload);
    </chain>
    <chain id="getEbMultiConfirmItems">
        SER(cmpEbMultiConfirmItemArgDepartmentCodeGet,cmpEbMultiConfirmItemArgIsAdminGet,cmpEbMultiConfirmItemGet);
    </chain>
    <chain id="saveEbMultiConfirmItem">
        SER(cmpEbMultiConfirmItemSave);
    </chain>
    <chain id="updateEbMultiConfirmItem">
        SER(cmpEbMultiConfirmItemUpdate);
    </chain>
    <chain id="terminateEbMultiConfirmItem">
        SER(
        // 更新确认项状态为终止
        cmpEbMultiConfirmItemTerminate,
        // 主账单状态是否变更
        cmpEbMultiConfirmEnd,
        // 记录存证
        FINALLY(cmpEbProveSave)
        );
    </chain>
    <chain id="confirmEbMultiConfirmItem">
        SER(
        // 更新确认项状态为确认
        cmpEbMultiConfirmItemConfirm,
        // 主账单状态是否变更
        cmpEbMultiConfirmEnd,
        // 记录存证
        FINALLY(cmpEbProveSave)
        );
    </chain>
    <chain id="objectionEbMultiConfirmItem">
        SER(
        // 更新确认项状态为异议
        cmpEbMultiConfirmItemObjection,
        // 主账单状态是否变更
        cmpEbMultiConfirmEnd,
        // 记录存证
        FINALLY(cmpEbProveSave)
        );
    </chain>
    <chain id="updateEbMultiConfirm">
        IF(cmpEbMultiConfirmUpdate,SER(cmpEbMultiConfirmItemDelete,saveEbMultiConfirmItemSub));
    </chain>
    <chain id="getEbOrg">
        SER( cmpEbMultiConfirmItemRemoteConfig,cmpEbOrgGet);
    </chain>
    <chain id="isNotAdmin">
        SER(cmpEbMultiConfirmRoleRemoteConfig,cmpEbMultiConfirmNotAdmin);
    </chain>
</flow>
/* --------------- 修改表 --------------- */
-- 修改表：estimate_rebeat_contract[合约]
-- 添加字段：
DROP PROCEDURE IF EXISTS alter_table_travelsky_rebate_column;
DELIMITER $$
CREATE PROCEDURE alter_table_travelsky_rebate_column()
BEGIN
    select DATABASE() into @db_name;

    IF EXISTS(SELECT *
              FROM information_schema.columns
              WHERE table_schema = @db_name
                and table_name = 'estimate_rebeat_contract'
                and column_name = 'contract_trading_limit')
    THEN
        alter table estimate_rebeat_contract
            modify contract_trading_limit decimal(32, 2) not null comment '支付合约履约交易限额（累计）';
    END IF;

    IF EXISTS(SELECT *
              FROM information_schema.columns
              WHERE table_schema = @db_name
                and table_name = 'estimate_rebeat_contract'
                and column_name = 'trading_plan_cnt')
    THEN
        alter table estimate_rebeat_contract
            modify trading_plan_cnt decimal(32, 2) null comment '支付合约计划履约笔数';
    END IF;

    IF EXISTS(SELECT *
              FROM information_schema.columns
              WHERE table_schema = @db_name
                and table_name = 'estimate_rebeat_contract'
                and column_name = 'cpt_limit')
    THEN
        alter table estimate_rebeat_contract
            modify cpt_limit decimal(32, 2) not null comment '支付合约履约交易限额（单笔）;CPT[Contract Performance Transaction]';
    END IF;
END $$
DELIMITER ;

CALL alter_table_travelsky_rebate_column;
DROP PROCEDURE alter_table_travelsky_rebate_column;
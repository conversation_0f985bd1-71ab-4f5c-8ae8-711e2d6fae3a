package com.swcares.aiot.core.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 合约
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("estimate_rebeat_contract")
@ApiModel(value = "EstimateRebeatContract对象", description = "合约")
public class EstimateRebeatContract extends Model<EstimateRebeatContract> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "业务合约id")
    @TableField("biz_contract_id")
    private String bizContractId;

    @ApiModelProperty(value = "应用名称")
    @TableField("app_name")
    private String appName;

    @ApiModelProperty(value = "合约机场")
    @TableField("contract_airport")
    private String contractAirport;

    @ApiModelProperty(value = "业务合约名")
    @TableField("contract_name")
    private String contractName;

    @ApiModelProperty(value = "业务合约描述")
    @TableField("contract_desc")
    private String contractDesc;

    @ApiModelProperty(value = "业务规则计算公式")
    @TableField("contract_formula")
    private String contractFormula;

    @ApiModelProperty(value = "公式说明")
    @TableField("formula_desc")
    private String formulaDesc;

    @ApiModelProperty(value = "支付合约履约交易限额（累计）")
    @TableField("contract_trading_limit")
    private BigDecimal contractTradingLimit;

    @ApiModelProperty(value = "支付合约履约交易限额有效期[开始日期]")
    @TableField("trading_limit_start")
    private LocalDate tradingLimitStart;

    @ApiModelProperty(value = "支付合约履约交易限额有效期[结束日期]")
    @TableField("trading_limit_end")
    private LocalDate tradingLimitEnd;

    @ApiModelProperty(value = "支付合约计划履约笔数")
    @TableField("trading_plan_cnt")
    private BigDecimal tradingPlanCnt;

    @ApiModelProperty(value = "账单类型;账单类型[billing_type_daily(日账单):billing_type_monthly(月账单)]")
    @TableField("billing_type")
    private String billingType;

    @ApiModelProperty(value = "账单汇总方式;账单汇总方式[single_airport(单机场):single_airport_airline(单机场_单航司)]")
    @TableField("billing_sum_method")
    private String billingSumMethod;

    @ApiModelProperty(value = "支付合约履约交易限额（单笔）;CPT[Contract Performance Transaction]")
    @TableField("cpt_limit")
    private BigDecimal cptLimit;

    @ApiModelProperty(value = "合约状态（0 未响应,1 同意,2 不同意）")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "删除标识（1：删除 0：正常）")
    @TableField(value = "deleted", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

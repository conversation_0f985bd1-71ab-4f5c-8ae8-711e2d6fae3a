package com.swcares.aiot.core.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.core.model.dto.ErMonthlyObjectionDto
 * Description：
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2025/4/18 14:55
 * @version v1.0
 */
@Data
public class ErMonthlyObjectionDto {

    @ApiModelProperty(value = "月账单id")
    private Long id;

    @ApiModelProperty(value = "异议备注")
    @Size(max = 256, message = "异议备注长度不能超过256")
    private String objectionRemark;

    @ApiModelProperty(value = "附件")
    @Size(max = 20)
    private List<String> fileKeyList;
}

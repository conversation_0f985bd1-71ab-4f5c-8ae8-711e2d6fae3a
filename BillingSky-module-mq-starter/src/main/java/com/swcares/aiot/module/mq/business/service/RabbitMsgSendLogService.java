package com.swcares.aiot.module.mq.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aiot.module.mq.business.model.dto.RabbitMsgSendLogPagedDTO;
import com.swcares.aiot.module.mq.business.model.entity.RabbitMsgSendLog;
import com.swcares.aiot.module.mq.business.model.vo.RabbitMsgSendLogVO;

import java.util.List;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title ：RabbitMsgSendLogService <br>
 * Package ：com.swcares.aiot.module.mq.service <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 * <p>
 * <AUTHOR> <br>
 * date 2024年 02月29日 10:17 <br>
 *
 * @version v1.0 <br>
 */
public interface RabbitMsgSendLogService extends IService<RabbitMsgSendLog> {
    /**
     * Title : listFailedMessage <br>
     * Description : 查询发送失败的消息 <br>
     * author zhang_qiang  <br>
     * date 2024/2/29 10:23<br>
     *
     * @return java.util.List<com.swcares.aiot.module.mq.model.entity.RabbitMsgSendLog>
     */
    List<RabbitMsgSendLog> listFailedMessage();


    /**
     * Title : page <br>
     * Description : 分页查询mq发送日志 <br>
     * author zhang_qiang  <br>
     * date 2024/3/6 12:04<br>
     *
     * @param dto :
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aiot.module.mq.business.model.vo.RabbitMsgSendLogVO>
     */
    IPage<RabbitMsgSendLogVO> page(RabbitMsgSendLogPagedDTO dto);

    /**
     * Title : addRetryTimesById <br>
     * Description : 重试次数加1 <br>
     * author zhang_qiang  <br>
     * date 2024/3/7 13:46<br>
     *
     * @param id
     */
    void addRetryTimesById(Long id);
}

package com.swcares.aiot.module.mq.vender;

import com.swcares.aiot.module.mq.AbstractMQ;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title ：IMessageSender <br>
 * Package ：com.swcares.aiot.module.mq.vender <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 * Description : MQ 消息发送器 接口定义 <br>
 *
 * <AUTHOR> <br>
 * date 2024年 02月29日 9:39 <br>
 * @version v1.0 <br>
 */
public interface IMessageSender {

    /**
     * Title : send <br>
     * Description : 实时推送MQ消息  <br>
     * <p>
     * author zhang_qiang  <br>
     * date 2024/2/29 9:42<br>
     *
     * @param mqModel :
     */
    void send(AbstractMQ mqModel);

    /**
     * Title : send <br>
     * Description : 推送MQ消息, 延迟接收 <br>
     * author zhang_qiang  <br>
     * date 2024/2/29 9:41<br>
     *
     * @param mqModel :
     * @param delay   :
     */
    void send(AbstractMQ mqModel, int delay);
}

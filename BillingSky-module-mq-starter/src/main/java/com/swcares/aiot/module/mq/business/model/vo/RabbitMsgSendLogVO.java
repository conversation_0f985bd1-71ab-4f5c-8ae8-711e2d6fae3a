package com.swcares.aiot.module.mq.business.model.vo;

import com.swcares.aiot.module.mq.business.model.entity.RabbitMsgSendLog;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">xnky.travelsky.net</a> <br>
 * Title ：RabbitMsgSendLog <br>
 * Package ：com.swcares.aiot.components.mq.model.entity <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 * Description : mq消息记录类 <br>
 *
 * <AUTHOR> <br>
 * date 2024年 02月27日 14:58 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RabbitMsgSendLogVO extends RabbitMsgSendLog {

    private static final long serialVersionUID = 1L;

}

package com.swcares.aiot.module.mq.config.rabbitmq;

import com.rabbitmq.client.Channel;
import com.swcares.aiot.module.mq.business.model.entity.RabbitMsgSendLog;
import com.swcares.aiot.module.mq.business.service.RabbitMsgSendLogService;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import javax.annotation.Nullable;
import java.io.IOException;
import java.util.Objects;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title ：LoggedRabbitTemplate <br>
 * Package ：com.swcares.aiot.module.mq.config.rabbitmq <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * date 2024年 02月29日 10:32 <br>
 * @version v1.0 <br>
 */
@NoArgsConstructor
@AllArgsConstructor
public class LoggedRabbitTemplate extends RabbitTemplate {

    private RabbitMsgSendLogService rabbitMsgSendLogService;

    public LoggedRabbitTemplate(ConnectionFactory connectionFactory, RabbitMsgSendLogService rabbitMsgSendLogService) {
        super(connectionFactory);
        this.rabbitMsgSendLogService = rabbitMsgSendLogService;
    }

    @Override
    public void doSend(@NotNull Channel channel,
                       @NotNull String exchangeArg,
                       @NotNull String routingKeyArg,
                       @NotNull Message message,
                       boolean mandatory,
                       @Nullable CorrelationData correlationData) throws IOException {
        if (Objects.nonNull(correlationData)) {
            String[] split = correlationData.getId().split("/");
            Long id = Long.parseLong(split[0]);
            rabbitMsgSendLogService.addRetryTimesById(id);
            super.doSend(channel, exchangeArg, routingKeyArg, message, mandatory, correlationData);
            return;
        }
        message.getMessageProperties().setReceivedExchange(exchangeArg);
        message.getMessageProperties().setReceivedRoutingKey(routingKeyArg);

        RabbitMsgSendLog rabbitMsgSendLog = new RabbitMsgSendLog();
        rabbitMsgSendLog.setMessageBody(new String(message.getBody()));
        rabbitMsgSendLog.setExchange(exchangeArg);
        rabbitMsgSendLog.setRoutingKey(routingKeyArg);
        rabbitMsgSendLog.setRetryTimes(0);
        //将发送的消息做日志存储到数据库当中
        rabbitMsgSendLogService.save(rabbitMsgSendLog);
        correlationData = new CorrelationData();
        Long tenant = TenantHolder.getTenant();
        correlationData.setId(rabbitMsgSendLog.getId() + "/" + tenant);

        super.doSend(channel, exchangeArg, routingKeyArg, message, mandatory, correlationData);
    }
}

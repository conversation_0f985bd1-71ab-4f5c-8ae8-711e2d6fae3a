package com.swcares.aiot;

import com.swcares.aiot.easyExcel.core.model.ImportResultDto;
import com.swcares.aiot.easyExcel.service.EasyExcelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Slf4j
public class EasyExcelTemplate {

    @Resource
    private EasyExcelService easyExcelService;

    /**
     * 导出Excel，默认
     *
     * @param list 导出的数据
     * @param tClass 带有excel注解的实体类
     * @param response 响应
     * @param fileName 文件名
     * @param sheetName Excel sheet 名
     */
    public <T> void exportExcel(List<T> list, Class<T> tClass, HttpServletResponse response, String fileName, String sheetName){
        easyExcelService.exportExcel(list, tClass, response, fileName, sheetName);
    }

    /**
     * 导出Excel，按照模板导出，这里是填充模板
     *
     * @param response 响应
     * @param template 模板
     * @param fileName 文件名
     * @param dataMap 导出的数据
     */
    public void exportExcelWithTemplate(HttpServletResponse response, String template, String fileName, Map<String, Map<String, Object>> dataMap){
        easyExcelService.exportExcelWithTemplate(response, template, fileName, dataMap);
    }

    /**
     * 按模版生成文件，这里是填充模板
     *
     * @param template 模板
     * @param fileName 文件名
     * @param dataMap 导出的数据
     */
    public void writeExcelWithTemplate( String template, String fileName, Map<String, Map<String, Object>> dataMap){
        easyExcelService.writeExcelWithTemplate(template, fileName, dataMap);
    }

    /**
     * 文件打包压缩下载
     *
     * @param response     响应
     * @param fileNames    文件集合
     * @param zipFileName  压缩包文件名
     */
    public void exportZip(HttpServletResponse response, List<String> fileNames, String zipFileName){
        easyExcelService.exportZip(response, fileNames, zipFileName);
    }

    /**
     * 导入Excel 单 sheet 或多 sheet 单类型
     *
     * @param file 文件
     * @param tClass 带有excel注解的实体类
     */
    public <T> ImportResultDto<T> importExcel(MultipartFile file, Class<T> tClass){
        return easyExcelService.importExcel(file, tClass);
    }

    /**
     * 导入Excel 读单个 sheet
     *
     * @param file 文件
     * @param tClass 带有excel注解的实体类
     */
    public <T> ImportResultDto<T> importExcelComplex(MultipartFile file, Class<T>tClass, Integer sheetNo){
        return easyExcelService.importExcelComplex(file, tClass, sheetNo);
    }

}
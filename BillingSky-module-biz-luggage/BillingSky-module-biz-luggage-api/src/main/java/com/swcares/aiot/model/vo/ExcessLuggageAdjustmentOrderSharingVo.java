package com.swcares.aiot.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ClassName：com.swcares.aiot.model.vo.ExcessLuggageAdjustmentOrderSharingVo<br>
 * Description：逾重行李调账详情业务数据返回参数<br>
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.<br>
 * Company：Aviation Cares Of Southwest Chen Du LTD<br>
 *
 * <AUTHOR>
 * date 2025/2/24 11:19<br>
 * @version v1.0<br>
 */

@Data
@ApiModel(value = "ExcessLuggageAdjustmentOrderSharingVo", description = "逾重行李调账详情业务数据返回参数")
public class ExcessLuggageAdjustmentOrderSharingVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "日账单ID")
    private Long luggageOrderId;

    @ApiModelProperty(value = "调账ID")
    private Long luggageAdjustmentOrderApplyId;

    @ApiModelProperty(value = "分摊客户")
    private String customerCode;

    @ApiModelProperty(value = "调账前金额")
    private BigDecimal adjustmentBeforeAmount;

    @ApiModelProperty(value = "调账后金额")
    private BigDecimal adjustmentAfterAmount;

    @ApiModelProperty(value = "金额补差;调账后金额减调账前金额")
    private BigDecimal supplementAmount;

    @ApiModelProperty(value = "账单分类;airport:机场；airline：航司")
    private String adjustmentBillType;

    @ApiModelProperty(value = "分摊主体名称")
    private String customerName;

    @ApiModelProperty(value = "分摊总金额")
    private BigDecimal sharingTotalAmount;

    @ApiModelProperty(value = "分摊协议内容")
    private String sharingAgreementContent;

}

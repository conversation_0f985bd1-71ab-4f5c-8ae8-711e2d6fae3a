package com.swcares.aiot.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.model.dto.ExcessLuggageOrderPageDto <br>
 * Description：逾重行李分页请求参数 <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2024/11/29 <br>
 * @version v1.0 <br>
 */

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ExcessLuggageOrderPageDto", description = "逾重行李分页请求参数")
public class ExcessLuggageOrderPageDto extends LuggagePageDto {

    @ApiModelProperty(value = "账单开始日期")
    private LocalDate billDateStart;

    @ApiModelProperty(value = "账单结束日期")
    private LocalDate billDateEnd;

    @ApiModelProperty(value = "NewAppEMD订单号-EMD订单号")
    private String newAppEmdOrderNo;

    @ApiModelProperty(value = "EMD票号")
    private String emdTicketNo;

    @ApiModelProperty(value = "旅客类型;TCHB-通程旅客,GYZZ-中转旅客")
    private String passengerType;

    @ApiModelProperty(value = "旅客编号")
    private String passengerId;

    @ApiModelProperty(value = "NewApp支付状态-支付状态")
    private List<String> payStatusList;

    @ApiModelProperty(value = "生成状态（0:已生成、1:调账处理中、2:已调账、3:已分摊、4:未分摊）", hidden = true)
    private Integer status;

    @ApiModelProperty(value = "生成状态（0:已生成、1:调账处理中、2:已调账、3:已分摊、4:未分摊）")
    private List<Integer> statusList;

    @ApiModelProperty(value = "账单ID")
    private String billId;

    @ApiModelProperty(value = "EasyPay交易号")
    private String easyPayTransactionNo;

    @ApiModelProperty(value = "订单ID")
    private String orderId;

    @ApiModelProperty(value = "订单接收日期开始")
    private LocalDate orderReceivedDateStart;

    @ApiModelProperty(value = "订单接收日期结束")
    private LocalDate orderReceivedDateEnd;

    @ApiModelProperty(value = "通程航班号")
    private String throughFlightNo;

    @ApiModelProperty(value = "ET票号")
    private String etTicketNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "承运航班号")
    private String carrierFlightNo;

    @ApiModelProperty(value = "中转编号")
    private String transferNumber;

}

package com.swcares.aiot.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class LuggagePageDto implements Serializable {

    @Min(value = 1, message = "页码错误,不能小于1")
    @ApiModelProperty(value = "页码", example = "1")
    @NotNull(message = "页码不能为空！")
    private Integer pageNo;

    @Min(value = 1, message = "页大小错误,不能小于1")
    @Max(value = 200, message = "单页允许查询的最大数据量为:200")
    @NotNull(message = "页大小不能为空！")
    @ApiModelProperty(value = "页大小", example = "10")
    private Integer pageSize;

    @ApiModelProperty(value = "账单分类(''：航信端；'airport'：机场端；'airline'：航司端)", hidden = true)
    private String billType;

    @ApiModelProperty(value = "机场/航司（账单客户code）")
    private List<String> billCustomerCodes;

    @ApiModelProperty(value = "单个机场/航司（账单客户code）", hidden = true)
    private String billCustomerCode;
}

package com.swcares.aiot.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * ClassName：com.swcares.aiot.model.vo.ExcessLuggageOrderAttachedVO<br>
 * Description：<br>
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.<br>
 * Company：Aviation Cares Of Southwest Chen Du LTD<br>
 *
 * <AUTHOR>
 * date 2025/3/27 16:58<br>
 * @version v1.0<br>
 */
@Data
@ApiModel(value = "ExcessLuggageOrderAttachedVO", description = "逾重行李及附表业务数据")
public class ExcessLuggageOrderAttachedVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "旅客编号")
    private String passengerId;

    @ApiModelProperty(value = "旅客类型")
    private String passengerType;

    @ApiModelProperty(value = "通程航班号")
    private String throughFlightNo;

    @ApiModelProperty(value = "ET票号")
    private String etTicketNo;

    @ApiModelProperty(value = "航段数")
    private Integer flightSegments;

    @ApiModelProperty(value = "航段序号")
    private Integer flightSegmentSeq;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "承运航司")
    private String carrierAirline;

    @ApiModelProperty(value = "承运航班号")
    private String carrierFlightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "NewAppEMD订单号")
    private String newAppEmdOrderNo;

    @ApiModelProperty(value = "EMD票号")
    private String emdTicketNo;

    @ApiModelProperty(value = "本航段逾重托运行李费")
    @TableField("segment_excess_fee")
    private String segmentExcessFee;

    @ApiModelProperty(value = "收费机场三字码")
    private String feeAirportCode;

    @ApiModelProperty(value = "NewApp订单创建日期")
    private String newAppOrderCreateDate;

    @ApiModelProperty(value = "NewApp订单创建时间")
    private String newAppOrderCreateTime;

    @ApiModelProperty(value = "NewApp支付方式类型")
    private String newAppPayType;

    @ApiModelProperty(value = "NewApp支付货币")
    private String newAppPayCurrency;

    @ApiModelProperty(value = "NewApp支付方式总金额")
    private String newAppPayAmount;

    @ApiModelProperty(value = "NewAppEMD退票订单号")
    private String naeRefundOrderNo;

    @ApiModelProperty(value = "退费机场三字码")
    private String resFeeAirportCode;

    @ApiModelProperty(value = "NewAppEMD退票订单创建日期")
    private String naeRefundOrderCreateDate;

    @ApiModelProperty(value = "NewAppEMD退票订单创建时间")
    private String naeRefundOrderCreateTime;

    @ApiModelProperty(value = "NewApp退费支付方式类型")
    private String newAppRefundPayType;

    @ApiModelProperty(value = "NewApp退费支付货币")
    private String newAppRefundPayCurrency;

    @ApiModelProperty(value = "NewApp退费金额")
    private String newAppRefundAmount;

    @ApiModelProperty(value = "NewApp支付日期")
    private String newAppPayDate;

    @ApiModelProperty(value = "NewApp支付时间")
    private String newAppPayTime;

    @ApiModelProperty(value = "NewApp支付状态")
    private String newAppPayStatus;

    @ApiModelProperty(value = "EasyPay支付订单号")
    private String easyPayPayOrderNo;

    @ApiModelProperty(value = "EasyPay交易号")
    private String easyPayTransactionNo;

    @ApiModelProperty(value = "hash值")
    private String hashValue;

    @ApiModelProperty(value = "同步时间")
    private LocalDateTime syncTime;


    @ApiModelProperty(value = "逾重行李账单id")
    private Long excessLuggageOrderId;

    @ApiModelProperty(value = "逾重行李附表id")
    private Long excessLuggageOrderAttachedId;

    @ApiModelProperty(value = "账单ID")
    private String billId;

    @ApiModelProperty(value = "账单日期")
    private LocalDate billDate;

    @ApiModelProperty(value = "支付时间")
    private LocalDateTime payTime;

    @ApiModelProperty(value = "生成状态（0:已生成、1:调账处理中、2:已调账、3:已分摊、4:未分摊）")
    private Integer status;

    @ApiModelProperty(value = "是否是最新数据（1：否 0：是）")
    private Boolean isLatestData;

    @ApiModelProperty(value = "数据是否正常（1：异常 0：正常）")
    private Boolean isNormal;

    @ApiModelProperty(value = "未分摊原因")
    private String unallocatedReason;

    @ApiModelProperty(value = "账单异常原因")
    private String billAbnormalReason;

    @ApiModelProperty(value = "分摊协议表ID")
    private Long luggageProrateAgreement;

    @ApiModelProperty(value = "版本号")
    private Integer version;

    @ApiModelProperty(value = "账单接收日期")
    private LocalDate orderReceivedDate;
}

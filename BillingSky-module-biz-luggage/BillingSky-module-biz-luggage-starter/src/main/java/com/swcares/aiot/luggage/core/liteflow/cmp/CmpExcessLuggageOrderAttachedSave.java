package com.swcares.aiot.luggage.core.liteflow.cmp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.RandomUtil;
import com.swcares.aiot.core.entity.ExcessLuggageOrder;
import com.swcares.aiot.core.entity.ExcessLuggageOrderAttached;
import com.swcares.aiot.core.service.IExcessLuggageOrderAttachedService;
import com.swcares.aiot.luggage.core.cons.ConsLuggage;
import com.swcares.aiot.luggage.core.enums.EnumExcessLuggageOrder;
import com.swcares.aiot.luggage.core.liteflow.ctx.CtxToggleBilling;
import com.swcares.aiot.luggage.core.mapstruct.MsLoHandleBiz;
import com.swcares.aiot.luggage.core.model.dto.ExcessLuggageOrderAttachedDTO;
import com.swcares.aiot.metrics.collector.MetricsFacade;
import com.swcares.aiot.model.vo.ExcessLuggageOrderAttachedVO;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 附表存储数据
 *
 * <AUTHOR>
 */
@Slf4j
@LiteflowComponent(id = "CmpExcessLuggageOrderAttachedSave", name = "附表存储数据")
public class CmpExcessLuggageOrderAttachedSave extends NodeComponent {
    @Resource
    private IExcessLuggageOrderAttachedService iExcessLuggageOrderAttachedService;

    @Resource
    private MetricsFacade metricsFacade;

    @Resource
    private MsLoHandleBiz msLoHandleBiz;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public void process() {
        // 上下文
        CtxToggleBilling ctxToggleBilling = this.getContextBean(CtxToggleBilling.class);
        // 附表历史数据
        List<ExcessLuggageOrderAttachedVO> excessLuggageOrderAttachedHistory = ctxToggleBilling.getExcessLuggageOrderAttachedHistory();
        // 所有主账单数据
        List<ExcessLuggageOrder> excessLuggageOrders = ctxToggleBilling.getExcessLuggageOrders();
        // 附表 最新、正常的数据
        List<ExcessLuggageOrderAttached> excessLuggageOrderAttacheds = this.handleExcessLuggageOrderAttacheds(excessLuggageOrders, excessLuggageOrderAttachedHistory);
        // 附表
        ctxToggleBilling.setExcessLuggageOrderAttacheds(excessLuggageOrderAttacheds);
        // 附表记录
        iExcessLuggageOrderAttachedService.saveOrUpdateBatch(excessLuggageOrderAttacheds);
    }

    /**
     * Description 处理逾重行李订单存在重复数据 <br>
     * author chenshuaijun <br>
     * Date 2025/3/28 11:28
     *
     * @param excessLuggageOrders               ：
     * @param excessLuggageOrderAttachedHistory ：
     * @return List<ExcessLuggageOrderAttached>
     */
    private List<ExcessLuggageOrderAttached> handleExcessLuggageOrderAttacheds(List<ExcessLuggageOrder> excessLuggageOrders, List<ExcessLuggageOrderAttachedVO> excessLuggageOrderAttachedHistory) {
        log.info("处理逾重行李附表存在重复数据");
        List<ExcessLuggageOrderAttached> excessLuggageOrderAttacheds = new ArrayList<>();
        // 调账处理中\已调账
        List<Integer> statusList = Arrays.asList(ConsLuggage.ADJUSTING, ConsLuggage.ADJUSTED);
        // 记录false 是最新、正常的；true:旧数据、异常
        excessLuggageOrders.forEach(excessLuggageOrder -> {
            ExcessLuggageOrderAttachedDTO excessLuggageOrderAttachedDTO = new ExcessLuggageOrderAttachedDTO();
            // 唯一主键
            ExcessLuggageOrderAttachedVO onlyExcessLuggageOrder = getOnlyExcessLuggageOrders(excessLuggageOrder, excessLuggageOrderAttachedHistory);
            if (onlyExcessLuggageOrder != null) {
                // 账单接收日期
                LocalDate billDate = excessLuggageOrder.getSyncTime().toLocalDate();
                // 存在重复的账单日记
                ExcessLuggageOrderAttachedVO excessLuggageOrderAttached = getExistExcessLuggageOrderAttached(onlyExcessLuggageOrder, billDate);
                // 重复逾重行李的账单日期，和接收数据当天是同一天 - 是
                if (excessLuggageOrderAttached != null) {
                    // 重复逾重行李的账单日期，和接收数据当天是同一天 - 是，并且重复订单数据的状态为（调账处理中\已调账）
                    // 不包含（调账处理中\已调账）则正常业务数据，处理旧数据更新成不是最新
                    if (statusList.contains(excessLuggageOrderAttached.getStatus())) {
                        // 重复订单数据的状态为（调账处理中\已调账），更新为异常数据
                        excessLuggageOrderAttachedDTO.setIsLatestData(EnumExcessLuggageOrder.OLD_OR_ABNORMAL.isValue());
                        excessLuggageOrderAttachedDTO.setIsNormal(EnumExcessLuggageOrder.OLD_OR_ABNORMAL.isValue());
                        excessLuggageOrderAttachedDTO.setBillAbnormalReason(ConsLuggage.YZ_YC_002);
                        // 通用事件计数方法
                        metricsFacade.recordCount(ConsLuggage.EXCESS_LUGGAGE_ORDER, "逾重行李异常业务数据统计事件", 1.0, getAdjustmentMapTag());
                    } else {
                        // 不包含（调账处理中\已调账）则正常业务数据，处理旧数据更新成不是最新
                        excessLuggageOrderAttachedDTO.getTransferExcessLuggageOrderAttacheds().add(excessLuggageOrderAttached);
                    }
                } else {
                    // 重复逾重行李的账单日期，和接收数据当天不为同一天 - 否
                    excessLuggageOrderAttachedDTO.setIsLatestData(EnumExcessLuggageOrder.OLD_OR_ABNORMAL.isValue());
                    excessLuggageOrderAttachedDTO.setIsNormal(EnumExcessLuggageOrder.OLD_OR_ABNORMAL.isValue());
                    excessLuggageOrderAttachedDTO.setBillAbnormalReason(ConsLuggage.YZ_YC_001);
                    // 通用事件计数方法
                    metricsFacade.recordCount(ConsLuggage.EXCESS_LUGGAGE_ORDER, "逾重行李异常业务数据统计事件", 1.0, getSplitBillMapTag());
                }
                log.info("处理逾重行李附表存在重复数据，重复数据:{}", excessLuggageOrderAttachedDTO);
            }
            excessLuggageOrderAttacheds.addAll(getExcessLuggageOrderAttacheds(Collections.singletonList(excessLuggageOrder), excessLuggageOrderAttachedDTO));
        });
        return excessLuggageOrderAttacheds;
    }

    /**
     * Description 已分账订单事件统计 <br>
     * author chenshuaijun <br>
     * Date 2025/4/3 10:26
     *
     * @return Map<String, String>
     */
    private Map<String, String> getSplitBillMapTag() {
        Map<String, String> tag = new HashMap<>();
        tag.put(ConsLuggage.SHARE_BILL, "已分账订单事件统计");
        return tag;
    }

    /**
     * Description 调账的订单事件统计 <br>
     * author chenshuaijun <br>
     * Date 2025/4/3 10:25
     *
     * @return Map<String, String>
     */
    private Map<String, String> getAdjustmentMapTag() {
        Map<String, String> tag = new HashMap<>();
        tag.put(ConsLuggage.ADJUSTMENT_BILL, "调账的订单事件统计");
        return tag;
    }

    /**
     * Description 找附表是否存在同一天账单日期数据 <br>
     * author chenshuaijun <br>
     * Date 2025/4/3 14:33
     *
     * @param excessLuggageOrderAttached:
     * @param billDate:
     * @return ExcessLuggageOrderAttachedVO
     */
    private ExcessLuggageOrderAttachedVO getExistExcessLuggageOrderAttached(ExcessLuggageOrderAttachedVO excessLuggageOrderAttached, LocalDate billDate) {
        if (excessLuggageOrderAttached.getOrderReceivedDate().equals(billDate)) {
            return excessLuggageOrderAttached;
        }
        return null;
    }

    /**
     * Description 唯一主键:旅客编号+航段序号+EMD票号+NewApp支付状态 <br>
     * author chenshuaijun <br>
     * Date 2025/3/27 16:22
     *
     * @param excessLuggageOrder     ：
     * @param excessLuggageOrderList ：
     * @return List<ExcessLuggageOrderAttachedVO>
     */
    private ExcessLuggageOrderAttachedVO getOnlyExcessLuggageOrders(ExcessLuggageOrder excessLuggageOrder, List<ExcessLuggageOrderAttachedVO> excessLuggageOrderList) {
        // 唯一主键:旅客编号+航段序号+EMD票号+NewApp支付状态
        List<ExcessLuggageOrderAttachedVO> excessLuggageOrderAttachedVOS = excessLuggageOrderList.stream()
                .filter(order -> order.getPassengerId().equals(excessLuggageOrder.getPassengerId())
                        && order.getFlightSegmentSeq().equals(excessLuggageOrder.getFlightSegmentSeq())
                        && order.getEmdTicketNo().equals(excessLuggageOrder.getEmdTicketNo())
                        && order.getNewAppPayStatus().equals(excessLuggageOrder.getNewAppPayStatus()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(excessLuggageOrderAttachedVOS)) {
            return excessLuggageOrderAttachedVOS.stream().sorted(Comparator.comparing(ExcessLuggageOrderAttachedVO::getVersion).reversed()).collect(Collectors.toList()).get(0);
        }
        return null;
    }

    /**
     * Description 附表数据处理 <br>
     * author chenshuaijun <br>
     * Date 2025/4/3 14:56
     *
     * @param excessLuggageOrders
     * @param excessLuggageOrderAttachedDTO
     * @return List<ExcessLuggageOrderAttached>
     */
    private List<ExcessLuggageOrderAttached> getExcessLuggageOrderAttacheds(List<ExcessLuggageOrder> excessLuggageOrders, ExcessLuggageOrderAttachedDTO excessLuggageOrderAttachedDTO) {
        LocalDate localDate = excessLuggageOrders.get(0).getSyncTime().toLocalDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd");
        // YZ-20250414
        String orderIdKey = ConsLuggage.ORDER_ID_HEAD + localDate.format(formatter);
        // 附表记录 - 用于记录新增或更新数据
        List<ExcessLuggageOrderAttached> excessLuggageOrderAttachedes = new ArrayList<>();
        // 处理历史数据，更新成旧数据
        List<ExcessLuggageOrderAttachedVO> transferExcessLuggageOrderAttacheds = excessLuggageOrderAttachedDTO.getTransferExcessLuggageOrderAttacheds();
        if (CollUtil.isNotEmpty(transferExcessLuggageOrderAttacheds)) {
            excessLuggageOrderAttachedes.addAll(msLoHandleBiz.vos2pos(transferExcessLuggageOrderAttacheds));
        }
        // 新数据
        excessLuggageOrders.forEach(excessLuggageOrder -> excessLuggageOrderAttachedes.add(getExcessLuggageOrderAttached(excessLuggageOrder, orderIdKey, excessLuggageOrderAttachedDTO)));
        return excessLuggageOrderAttachedes;
    }

    /**
     * 获取对象
     */
    private ExcessLuggageOrderAttached getExcessLuggageOrderAttached(ExcessLuggageOrder excessLuggageOrder, String orderIdKey, ExcessLuggageOrderAttachedDTO excessLuggageOrderAttachedDTO) {
        // 主账单记录id
        Long excessLuggageOrderId = excessLuggageOrder.getId();
        ExcessLuggageOrderAttached excessLuggageOrderAttached = new ExcessLuggageOrderAttached();
        excessLuggageOrderAttached.setIsLatestData(excessLuggageOrderAttachedDTO.getIsLatestData());
        excessLuggageOrderAttached.setIsNormal(excessLuggageOrderAttachedDTO.getIsNormal());
        if (CharSequenceUtil.isNotEmpty(excessLuggageOrderAttachedDTO.getBillAbnormalReason())) {
            excessLuggageOrderAttached.setBillAbnormalReason(excessLuggageOrderAttachedDTO.getBillAbnormalReason());
        }
        // 逾重行李账单id
        excessLuggageOrderAttached.setExcessLuggageOrderId(excessLuggageOrderId);
        // 订单ID
        excessLuggageOrderAttached.setOrderId(getOrderId(orderIdKey));
        // 账单接收日期
        excessLuggageOrderAttached.setOrderReceivedDate(excessLuggageOrder.getSyncTime().toLocalDate());
        // 拼接：悦行+订单字段<NewApp支付日期>+<NewApp支付时间>
        excessLuggageOrderAttached.setPayTime(getPayTime(excessLuggageOrder));
        return excessLuggageOrderAttached;
    }

    /**
     * Description 获取账单ID YZDD-年月日+七位顺序数，年月日为接收日期，年只展示后两位，七位数字从0000001依次累加 <br>
     * author chenshuaijun <br>
     * Date 2025/4/16 15:42
     *
     * @param billIdKey :
     * @return String
     */
    private String getOrderId(String billIdKey) {
        // 自增
        Long number = redisTemplate.opsForValue().increment(billIdKey, 1);
        if (null != number && number == 1) {
            // 过期时间
            redisTemplate.expire(billIdKey, 1, TimeUnit.DAYS);
        }
        String randomNum = RandomUtil.randomNumbers(2);
        // 将结果转换回字符串并补零
        // 补零，确保 7 位
        String newNumberStr = String.format("%07d", number);
        // 拼接最终结果
        return billIdKey + newNumberStr + randomNum;
    }


    /**
     * 获取支付时间
     */
    private LocalDateTime getPayTime(ExcessLuggageOrder excessLuggageOrder) {
        String payTimeStr = CharSequenceUtil.format("{} {}", excessLuggageOrder.getNewAppPayDate(), excessLuggageOrder.getNewAppPayTime());
        return DateUtil.parseLocalDateTime(payTimeStr, "yyyyMMdd HH:mm:ss");
    }

}

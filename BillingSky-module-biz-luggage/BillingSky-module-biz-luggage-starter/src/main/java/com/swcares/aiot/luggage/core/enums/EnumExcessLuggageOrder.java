package com.swcares.aiot.luggage.core.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ClassName：com.swcares.aiot.luggage.core.enums.EnumExcessLuggageOrder<br>
 * Description：逾重行李枚举 <br>
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.<br>
 * Company：Aviation Cares Of Southwest Chen Du LTD<br>
 *
 * <AUTHOR>
 * date 2025/4/3 10:22<br>
 * @version v1.0<br>
 */
@AllArgsConstructor
@Getter
public enum EnumExcessLuggageOrder {

    // 记录false 是最新、正常的；true:旧数据、异常
    LATEST_OR_NORMAL(false),
    OLD_OR_ABNORMAL(true);

    @EnumValue
    private final boolean value;
}

package com.swcares.aiot.luggage.core.mapstruct;

import com.swcares.aiot.core.entity.ExcessLuggageOrder;
import com.swcares.aiot.model.vo.ExcessLuggageOrderDataVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface MsExcessLuggageOrder {
    @Mapping(target = "excessLuggageOrderVo", ignore = true)
    List<ExcessLuggageOrder> vo2po(List<ExcessLuggageOrderDataVO> excessLuggageOrderVos);
}

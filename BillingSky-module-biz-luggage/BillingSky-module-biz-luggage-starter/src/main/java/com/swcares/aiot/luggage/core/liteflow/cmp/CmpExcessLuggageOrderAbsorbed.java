package com.swcares.aiot.luggage.core.liteflow.cmp;

import com.swcares.aiot.core.entity.ExcessLuggageOrder;
import com.swcares.aiot.core.entity.ExcessLuggageOrderAttached;
import com.swcares.aiot.luggage.core.liteflow.ctx.CtxToggleBilling;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年04月03日14:46
 */
@LiteflowComponent(id = "CmpExcessLuggageOrderAbsorbed", name = "获取待分摊的订单数据")
public class CmpExcessLuggageOrderAbsorbed extends NodeComponent {
    @Override
    public void process() {
        CtxToggleBilling ctxToggleBilling = this.getContextBean(CtxToggleBilling.class);
        // 从上下文获取对象
        List<ExcessLuggageOrder> excessLuggageOrders = ctxToggleBilling.getExcessLuggageOrders();
        // 获取所有正常且最新数据的数据
        List<ExcessLuggageOrderAttached> excessLuggageOrderAttachedDbs = ctxToggleBilling.getExcessLuggageOrderAttachedDbs();
        Set<Long> excessLuggageOrderIdDbs = excessLuggageOrderAttachedDbs.stream().map(ExcessLuggageOrderAttached::getExcessLuggageOrderId).collect(Collectors.toSet());
        // 需要根据分摊协议计算的账单数据
        List<ExcessLuggageOrder> excessLuggageOrderUpdateList = excessLuggageOrders.stream().filter(k -> excessLuggageOrderIdDbs.contains(k.getId())).collect(Collectors.toList());
        // 保存到上下文
        ctxToggleBilling.setExcessLuggageOrderUpdateList(excessLuggageOrderUpdateList);
    }
}

package com.swcares.aiot.luggage.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.luggage.core.cons.ConsLuggage;
import com.swcares.aiot.luggage.service.IExcessLuggageAdjustmentOrderBizService;
import com.swcares.aiot.model.dto.ApplyAdjustmentDTO;
import com.swcares.aiot.model.dto.ExcessLuggageAdjustmentOrderPageDto;
import com.swcares.aiot.model.vo.ExcessLuggageAdjustmentOrderPageVo;
import com.swcares.aiot.model.vo.ExcessLuggageAdjustmentOrderVO;
import com.swcares.aiot.model.vo.ExcessLuggageOrderAnotherVo;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/excessLuggageAdjustmentOrderBiz")
@ApiVersion(ConsLuggage.CONS_LUGGAGE_API_VERSION)
@Api(tags = "对账通-逾重行李调账-api")
public class ExcessLuggageAdjustmentOrderBizController {

    @Resource
    private IExcessLuggageAdjustmentOrderBizService iExcessLuggageAdjustmentOrderBizService;

    @ApiOperation(value = "根据ID查询申请、审核逾重行李账单")
    @GetMapping("/{luggageOrderId}")
    public BaseResult<ExcessLuggageAdjustmentOrderVO> getExcessLuggageOrderById(@PathVariable("luggageOrderId") Long luggageOrderId,
                                                                                @RequestParam("applyCustomerCode") String applyCustomerCode,
                                                                                @RequestParam(value = "apply", required = false) String apply) {
        return BaseResult.ok(iExcessLuggageAdjustmentOrderBizService.getExcessLuggageOrderById(applyCustomerCode, luggageOrderId, apply));
    }

    @ApiOperation(value = "申请调账")
    @PostMapping("/applyAdjustment")
    public BaseResult<Object> applyAdjustment(@Validated @RequestBody ApplyAdjustmentDTO dto) {
        iExcessLuggageAdjustmentOrderBizService.applyAdjustment(dto);
        return BaseResult.ok();
    }

    @ApiOperation(value = "调账账单分页")
    @PostMapping("/page")
    public PagedResult<List<ExcessLuggageAdjustmentOrderPageVo>> page(@RequestBody ExcessLuggageAdjustmentOrderPageDto dto) {
        Page<ExcessLuggageAdjustmentOrderPageVo> page = iExcessLuggageAdjustmentOrderBizService.page(dto);
        return PagedResult.ok(page.getRecords(), page.getTotal(), page.getPages(), page.getSize(), page.getCurrent());
    }

    @ApiOperation(value = "根据ID查询是否有相同逾重行李账单数据")
    @GetMapping("/getById/{luggageOrderId}")
    public BaseResult<List<ExcessLuggageOrderAnotherVo>> getExcessLuggageOrderVoById(@PathVariable("luggageOrderId") Long luggageOrderId) {
        return BaseResult.ok(iExcessLuggageAdjustmentOrderBizService.getExcessLuggageOrderVoById(luggageOrderId));
    }

}

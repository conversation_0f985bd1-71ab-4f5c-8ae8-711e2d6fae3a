drop table if exists excess_luggage_order_attached;
create table if not exists excess_luggage_order_attached
(
    id                      bigint                               not null comment 'id'
        primary key,
    excess_luggage_order_id bigint                               not null comment '逾重行李账单id',
    bill_id                 varchar(32)                          null comment '账单ID',
    bill_date               date                                 null comment '账单日期',
    pay_time                datetime                             null comment '支付时间',
    status                  tinyint    default 0                 not null comment '生成状态（0:已生成、1:调账处理中、2:已调账）',
    deleted                 tinyint(1) default 0                 not null comment '删除标识（1：删除 0：正常）',
    created_by              varchar(32)                          null comment '创建人',
    created_time            datetime   default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by              varchar(32)                          null comment '更新人',
    updated_time            datetime   default CURRENT_TIMESTAMP not null comment '更新时间'
)
    comment '逾重行李附表' row_format = DYNAMIC;

drop table if exists excess_luggage_order_sharing;
create table if not exists excess_luggage_order_sharing
(
    id                      bigint                               not null comment 'id'
        primary key,
    excess_luggage_order_id bigint                               not null comment '逾重行李账单id',
    bill_type               varchar(16)                          null comment '账单分类',
    bill_customer_code      varchar(8)                           null comment '机场/航司（账单客户code）',
    amount                  decimal(16, 2)                       null comment '分成金额',
    deleted                 tinyint(1) default 0                 not null comment '删除标识（1：删除 0：正常）',
    created_by              varchar(32)                          null comment '创建人',
    created_time            datetime   default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by              varchar(32)                          null comment '更新人',
    updated_time            datetime   default CURRENT_TIMESTAMP not null comment '更新时间'
)
    comment '逾重行李分成';


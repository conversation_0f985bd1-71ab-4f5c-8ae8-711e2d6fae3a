<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="toggleBilling">
        SER(
        // 1、获取数据中心账单数据
        CmpExcessLuggageOrderRemoteData,
        // 2、获取历史主账单以及账单对应的附表数据
        CmpExcessLuggageOrderAttachedHistory,
        // 3、主账单数据保存
        CmpExcessLuggageOrderSave,
        // 4、附表数据保存
        CmpExcessLuggageOrderAttachedSave,
        // 操作历史保存
        CmpExcessLuggageOrderHistorySave,
        // 5、获取有效正常订单数据
        CmpExcessLuggageOrderEffective,
        // 6、获取待分摊的订单数据
        CmpExcessLuggageOrderAbsorbed,
        // 6、分成数据保存
        CmpExcessLuggageOrderSharingSave
        );
    </chain>

    <!--定时分账-->
    <chain name="toggleBillingTime">
        SER(
        // 1、获取订单附表
        CmpExcessLuggageOrderAttached,
        // 1、获取待分摊的订单数据
        CmpExcessLuggageOrderTbAbsorbed,
        // 2、分成数据保存
        CmpExcessLuggageOrderSharingSave
        ) ;
    </chain>
</flow>

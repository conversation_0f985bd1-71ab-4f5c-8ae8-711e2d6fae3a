package com.swcares.aiot.synergy.utils;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title ：ResponseUtils <br>
 * Package ：com.swcares.aiot.ass.common.util <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * date 2024年 01月23日 11:21 <br>
 * @version v1.0 <br>
 */
public class ResponseUtils {
    private ResponseUtils() {
    }

    public static ResponseEntity<String> buildTextResponse(String text) {

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.TEXT_HTML);
        return new ResponseEntity<>(text, httpHeaders, HttpStatus.OK);
    }

    protected ResponseEntity<Object> buildJsonResponse(Object body) {

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        return new ResponseEntity<>(body, httpHeaders, HttpStatus.OK);
    }
}

package com.swcares.aiot.gateway.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * ClassName：com.swcares.aiot.psp.gateway.config.Oauth2Properties <br>
 * Description：权限配置管理<br>
 *
 * <AUTHOR> <br>
 * Date 2022/8/31 16:26 <br>
 * @version v1.0 <br>
 */
@Data
@Component
@ConfigurationProperties(Oauth2Properties.PREFIX)
public class Oauth2Properties {
    public static final String PREFIX = "swcares.oauth2";
    /**
     * 是否启用权限验证
     */
    private boolean enable;
    /**
     * 不用登录就可以访问的地址
     */
    private List<String> freeAccessPath;
    /**
     * 登录就可以访问的地址
     */
    private List<String> loginAccessPath;

}

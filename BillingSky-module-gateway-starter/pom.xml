<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.swcares.aiot</groupId>
        <artifactId>BillingSky-modules</artifactId>
        <version>2.30.0-SNAPSHOT</version>
    </parent>
    <artifactId>BillingSky-module-gateway-starter</artifactId>
    <name>${project.artifactId}</name>
    <packaging>jar</packaging>
    <description>gateway的相关配置</description>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.worm</groupId>
            <artifactId>worm-toolkit-hutool</artifactId>
        </dependency>
    </dependencies>
</project>
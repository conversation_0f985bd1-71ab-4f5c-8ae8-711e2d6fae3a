package com.swcares.aiot.core.model.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * ClassName：com.swcares.aiot.core.model.vo.ReconciliationFlightPagedDTO <br>
 * Description：对账分页查询航班信息 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2024-01-16 <br>
 * @version v1.0 <br>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ReconciliationFlightPagedDTO", description = "对账分页查询航班信息")
public class ReconciliationFlightPagedDTO extends PagedDTO implements Serializable {


    private static final long serialVersionUID = 472388791108376745L;

    @ApiModelProperty(value = "航班日期（开始时间）")
    @NotNull
    private LocalDate startDate;

    @ApiModelProperty(value = "航班日期（结束时间）")
    @NotNull
    private LocalDate endDate;

    @ApiModelProperty(value = "起降标识")
    private String flightFlag;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "机场三字码")
    private String airportCode;


}

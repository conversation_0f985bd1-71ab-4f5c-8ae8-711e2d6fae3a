package com.swcares.aiot.core.model.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.core.model.dto.FlightBillDetailPagedDTO <br>
 * Description： 账单明细查询 传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2024-01-16 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "AirportFlightBillDetailQueryDTO", description = "航班账单明细查询对象")
public class AirportFlightBillDetailQueryDTO implements BaseDTO, Serializable  {

    private static final long serialVersionUID = 8943450721677311400L;

    @ApiModelProperty(value = "开账周期（开始时间）")
    @NotNull
    private LocalDate startDate;

    @ApiModelProperty(value = "开账周期（结束时间）")
    @NotNull
    private LocalDate endDate;

    @ApiModelProperty(value = "机场三字码")
    private String airportCode;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(name = "regNo", value = "机号")
    private String regNo;

    @ApiModelProperty(name = "flightFlag", value = "起降标识")
    private String flightFlag;

    @ApiModelProperty(value = "账单确认状态")
    private Integer status;

    @ApiModelProperty(name = "feeCode", value = "费用代码")
    private List<String> feeCodeList;

    @ApiModelProperty(name = "revocation", value = "撤销状态(1申请撤销 2拒绝撤销 3同意撤销)")
    private Integer revocation;

    @ApiModelProperty(name = "airportSyncStatus", value = "机场同步状态(0:无需同步、1:航司处理中、2:机场申请撤回、3:已撤回机场、4:已反馈机场)")
    private Integer airportSyncStatus;

    @ApiModelProperty(name = "auditWay", value = "审核方式0-系统 1-人工")
    private Integer auditWay;

    @ApiModelProperty(name = "airportFlightBillCode", value = "账单单号(账单ID)")
    private String airportFlightBillCode;
    @ApiModelProperty(value = "自动核对差异代码")
    private String autoCheckDifferenceCode;
}

package com.swcares.aiot.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bus_sign_service")
@ApiModel(value = "BusSignService", description = "")
public class BusSignService extends Model<BusSignService> implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "签单表id")
    @TableField("bill_sign_id")
    private Long billSignId;

    @ApiModelProperty(value = "签单内容项ID")
    @TableField("item_id")
    private Long itemId;

    @ApiModelProperty(value = "数据项名称")
    @TableField("item_name")
    private String itemName;

    @ApiModelProperty(value = "数据值")
    @TableField("item_value")
    private String itemValue;

    @ApiModelProperty(value = "数据格式类型")
    @TableField("data_format")
    private String dataFormat;

    @ApiModelProperty(value = "数据有效 有效:1|无效:0")
    @TableField("invalid")
    private String invalid;

    @ApiModelProperty(value = "创建人")
    @TableField("create_by")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    @TableField("modified_by")
    private String modifiedBy;

    @ApiModelProperty(value = "修改时间")
    @TableField("modified_time")
    private LocalDateTime modifiedTime;

    @ApiModelProperty(value = "机场三字码")
    @TableField("airport_code")
    private String airportCode;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

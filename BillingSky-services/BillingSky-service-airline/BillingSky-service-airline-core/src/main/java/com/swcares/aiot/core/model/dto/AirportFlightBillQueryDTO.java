package com.swcares.aiot.core.model.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.core.model.dto.FlightBillQueryDTO <br>
 * Description： 账单统计导出查询 传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2024-01-16 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "AirportFlightBillQueryDTO", description = "账单统计导出查询对象")
public class AirportFlightBillQueryDTO implements BaseDTO, Serializable  {

    private static final long serialVersionUID = 8653644205903926648L;

    @ApiModelProperty(value = "开账周期（开始时间）")
    @NotNull
    private LocalDate startDate;

    @ApiModelProperty(value = "开账周期（结束时间）")
    @NotNull
    private LocalDate endDate;

    @ApiModelProperty(value = "费用代码")
    private List<String> feeCodeList;

    @ApiModelProperty(value = "机场三字码")
    private String airportCode;

}

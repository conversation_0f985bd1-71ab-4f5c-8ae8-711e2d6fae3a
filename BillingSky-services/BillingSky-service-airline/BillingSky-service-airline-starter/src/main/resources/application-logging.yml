logging:
  config: classpath:logback-spring.xml
  level:
    root: ${LOGGING_LEVEL}
    ch.qos.logback: DEBUG
    com.swcares: ${LOGGING_LEVEL}
    org.springframework: ${LOGGING_LEVEL}
    com.baomidou.mybatisplus: ${LOGGING_LEVEL}
    com.inforun: ${LOGGING_LEVEL}
    com.swcares.aiot.client: ${LOGGING_LEVEL_FEIGN:DEBUG}
    # 配置 Feign 日志级别（全局）
    feign.Logger: ${LOGGING_LEVEL_FEIGN:DEBUG}  # 设为 DEBUG 以激活 Feign 日志
swcares:
  trace:
    add-trace-id-to-header: true
    enabled: true
    trace-id-header-name: X-Trace-Id
#    url-patterns:
#      - /**
#      - /v1/**
    format: ${trace.log.format}
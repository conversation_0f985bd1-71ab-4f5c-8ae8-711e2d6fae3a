package com.swcares.aiot.core.liteflow.cmp;

import com.alibaba.fastjson2.JSON;
import com.swcares.aiot.core.entity.BillAirportFlightBill;
import com.swcares.aiot.core.enums.EnumAutoCheckDiffReason;
import com.swcares.aiot.core.liteflow.ctx.*;
import com.swcares.aiot.model.dto.ActIndexCaluDto;
import com.swcares.aiot.model.vo.ActExpensesCaluVo;
import com.swcares.aiot.statemachine.biz.status.EnumBillStatus;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeIfComponent;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * date 2025年04月11日13:17
 */
@Slf4j
@LiteflowComponent(id = "CmpBmAutoCompareDataFeeEqual", name = "获取航司来源为无的机场费用金额不等于航司金额")
public class CmpBmAutoCompareDataFeeEqual extends NodeIfComponent {
    @Override
    public boolean processIf() throws Exception {
        // 当前循环对象
        BillAirportFlightBill billAirportFlightBill = this.getSubChainReqData();
        // 当前组件特有的上下文
        CtxActExpensesWithFormula ctxActExpensesWithFormula = this.getContextBean(CtxActExpensesWithFormula.class);
        CtxAirlineFlightInfo ctxAirlineFlightInfo = this.getContextBean(CtxAirlineFlightInfo.class);
        CtxBmAutoCompareDataSnapshotVO ctxBmAutoCompareDataSnapshotVO = this.getContextBean(CtxBmAutoCompareDataSnapshotVO.class);
        ctxBmAutoCompareDataSnapshotVO.setBillAirportFlightBill(billAirportFlightBill);
        ctxBmAutoCompareDataSnapshotVO.setAirlineFlightInfo(ctxAirlineFlightInfo.getAirlineFlightInfo());
        // 指标值
        List<ActIndexCaluDto> indexCaluDtos = ctxActExpensesWithFormula.getIndexCaluDtos();
        ActExpensesCaluVo actFormula2Vo = ctxBmAutoCompareDataSnapshotVO.getActFormula2Vo();
        actFormula2Vo.setIndexCaluDtos(indexCaluDtos);
        // 机场费用金额
        BigDecimal chargePrice = billAirportFlightBill.getChargePrice();
        // 航司计算的收费金额
        BigDecimal airlineChargePrice = ctxActExpensesWithFormula.getBillAirportFlightBill().getAirlineChargePrice();
        boolean idDup = chargePrice.compareTo(airlineChargePrice) != BigDecimal.ZERO.intValue();
        CtxBmAutoCompareDataSnapshot ctxBmAutoCompareDataSnapshot = this.getContextBean(CtxBmAutoCompareDataSnapshot.class);
        if (idDup) {
            // 机场费用金额 不等于 航司计算的收费金额
            // 更新快照
            ctxBmAutoCompareDataSnapshot.setEnumAutoCheckDiffReason(EnumAutoCheckDiffReason.BILL_CALC_NOT_EQUALS_661);
            ctxBmAutoCompareDataSnapshot.setAutoCheckDiffReason(EnumAutoCheckDiffReason.BILL_CALC_NOT_EQUALS_661.getReason());
            // 更新账单状态为有差异
            CtxBmAutoCompareBillDataUp ctxBmAutoCompareBillDataUp = this.getContextBean(CtxBmAutoCompareBillDataUp.class);
            ctxBmAutoCompareBillDataUp.setEnumAutoCheckDiffReason(EnumAutoCheckDiffReason.BILL_CALC_NOT_EQUALS_661);
        } else {
            // 已确认
            ctxBmAutoCompareDataSnapshot.setEnumBillStatus(EnumBillStatus.CONFIRMED);
        }
        // 数量
        billAirportFlightBill.setAirlinePricingAmount(ctxActExpensesWithFormula.getBillAirportFlightBill().getAirlinePricingAmount());
        // 单价
        billAirportFlightBill.setAirlineUnitPrice(ctxActExpensesWithFormula.getBillAirportFlightBill().getAirlineUnitPrice());
        // 金额
        billAirportFlightBill.setAirlineChargePrice(airlineChargePrice);
        // 快照数据
        ctxBmAutoCompareDataSnapshot.setSnapshotData(JSON.toJSONString(ctxBmAutoCompareDataSnapshotVO));
        return idDup;
    }
}

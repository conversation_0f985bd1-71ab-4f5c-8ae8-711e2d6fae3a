package com.swcares.aiot.core.liteflow.cmp;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import com.alibaba.fastjson2.JSON;
import com.swcares.aiot.core.entity.AirlineFlightInfo;
import com.swcares.aiot.core.entity.BillAirportFlightBill;
import com.swcares.aiot.core.enums.EnumAutoCheckDiffReason;
import com.swcares.aiot.core.enums.EnumSnapshotBillType;
import com.swcares.aiot.core.liteflow.ctx.*;
import com.swcares.aiot.core.mapstruct.MsBillManageBiz;
import com.swcares.aiot.core.model.dto.BillAirportFlightBillDto;
import com.swcares.aiot.core.model.dto.BizAirlineFlightInfoDTO;
import com.swcares.aiot.core.service.IAirlineFlightInfoService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeIfComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * date 2025年04月10日16:37
 */
@Slf4j
@LiteflowComponent(id = "CmpBmAutoCompareDataFlight", name = "核对账单航班信息")
public class CmpBmAutoCompareDataFlight extends NodeIfComponent {

    @Resource
    private IAirlineFlightInfoService iAirlineFlightInfoService;
    @Resource
    private MsBillManageBiz msBillManageBiz;

    @Override
    public boolean processIf() {
        log.info("核对账单对应的航司航班信息");
        // 获取传入的参数
        BillAirportFlightBill billAirportFlightBill = this.getSubChainReqData();

        LocalDate flightDate = billAirportFlightBill.getFlightDate();
        log.info("航班日期:{}", flightDate);
        String flightNo = billAirportFlightBill.getFlightNo();
        log.info("航班号:{}", flightNo);
        String flightSegment = billAirportFlightBill.getFlightSegment();
        log.info("航段:{}", flightSegment);
        String[] segments = CharSequenceUtil.split(flightSegment, StrPool.DASHED);
        String depAirportCode = segments[0];
        String arrAirportCode = segments[1];
        log.info("查找航司航班数据");
        List<AirlineFlightInfo> airlineFlightInfos = iAirlineFlightInfoService.lambdaQuery()
                .eq(AirlineFlightInfo::getFlightDate, flightDate)
                .eq(AirlineFlightInfo::getFlightNo, flightNo)
                .eq(AirlineFlightInfo::getDepartureAirportCode, depAirportCode)
                .eq(AirlineFlightInfo::getArrivalAirportCode, arrAirportCode)
                .list();
        boolean isOne = airlineFlightInfos.size() != 1;
        // 上下文
        if (isOne) {
            BillAirportFlightBillDto billAirportFlightBillDto = msBillManageBiz.do2dto(billAirportFlightBill);
            billAirportFlightBillDto.setSnapshotBillType(EnumSnapshotBillType.MAIN_BILL);
            // 快照
            CtxBmAutoCompareDataSnapshot ctxBmAutoCompareDataSnapshot = this.getContextBean(CtxBmAutoCompareDataSnapshot.class);
            //航班信息
            CtxAutoCompareBillFlight ctxAutoCompareBillFlight = this.getContextBean(CtxAutoCompareBillFlight.class);
            ctxAutoCompareBillFlight.setBillAirportFlightBill(billAirportFlightBillDto);
            ctxBmAutoCompareDataSnapshot.setSnapshotData(JSON.toJSONString(ctxAutoCompareBillFlight));
            ctxBmAutoCompareDataSnapshot.setEnumAutoCheckDiffReason(EnumAutoCheckDiffReason.BILL_FINDING_FLIGHT_FAILED_621);
            ctxBmAutoCompareDataSnapshot.setAutoCheckDiffReason(EnumAutoCheckDiffReason.BILL_FINDING_FLIGHT_FAILED_621.getReason());


            // 更新账单状态为有差异
            CtxBmAutoCompareBillDataUp ctxBmAutoCompareBillDataUp = this.getContextBean(CtxBmAutoCompareBillDataUp.class);
            ctxBmAutoCompareBillDataUp.setEnumAutoCheckDiffReason(EnumAutoCheckDiffReason.BILL_FINDING_FLIGHT_FAILED_621);
        } else {
            // 机场账单
            BillAirportFlightBillDto billAirportFlightBillDto = msBillManageBiz.do2dto(billAirportFlightBill);
            billAirportFlightBillDto.setSnapshotBillType(EnumSnapshotBillType.MAIN_BILL);
            // 航司账单
            AirlineFlightInfo airlineFlightInfo = airlineFlightInfos.get(0);
            BizAirlineFlightInfoDTO bizAirlineFlightInfoDTO = BeanUtil.copyProperties(airlineFlightInfo, BizAirlineFlightInfoDTO.class);
            bizAirlineFlightInfoDTO.setSnapshotBillType(EnumSnapshotBillType.AIR_BILL);
            log.info("航司航班信息:{}", airlineFlightInfo);
            CtxAirlineFlightInfo ctxAirlineFlightInfo = this.getContextBean(CtxAirlineFlightInfo.class);
            ctxAirlineFlightInfo.setAirlineFlightInfo(airlineFlightInfo);
            CtxCompareDataFeeItem ctxCompareDataFeeItem = this.getContextBean(CtxCompareDataFeeItem.class);
            // 航司账单
            ctxCompareDataFeeItem.setAirlineFlightInfo(bizAirlineFlightInfoDTO);
            // 机场账单
            ctxCompareDataFeeItem.setBillAirportFlightBill(billAirportFlightBillDto);
        }
        return isOne;
    }
}

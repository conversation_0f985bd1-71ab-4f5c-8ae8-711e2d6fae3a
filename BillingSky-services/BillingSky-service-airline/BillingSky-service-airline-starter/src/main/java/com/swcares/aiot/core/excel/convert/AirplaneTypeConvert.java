package com.swcares.aiot.core.excel.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.swcares.aiot.core.model.enums.AirplaneTypeEnum;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title ：AirplaneTypeConvert <br>
 * Package ：com.swcares.aiot.ass.business.base.excel.convert <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * date 2024年 02月23日 11:22 <br>
 * @version v1.0 <br>
 */
public class AirplaneTypeConvert implements Converter<AirplaneTypeEnum> {
    @Override
    public Class<AirplaneTypeEnum> supportJavaTypeKey() {
        return AirplaneTypeEnum.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public AirplaneTypeEnum convertToJavaData(CellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        return AirplaneTypeEnum.getByName(cellData.getStringValue());
    }

    @Override
    public CellData convertToExcelData(AirplaneTypeEnum value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        return null;
    }
}

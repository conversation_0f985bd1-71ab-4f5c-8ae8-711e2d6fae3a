package com.swcares.aiot.core.liteflow.cmp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.core.entity.BillAirportAutoCompareSnapshot;
import com.swcares.aiot.core.liteflow.ctx.CxtBmAirportBillHistory;
import com.swcares.aiot.core.liteflow.ctx.CxtBmBillAirportAutoCompareSnapshotList;
import com.swcares.aiot.core.mapstruct.MsBmHandleAirportBillSnapshot;
import com.swcares.aiot.core.model.enums.EnumBillOperation;
import com.swcares.aiot.core.model.vo.BmHandleAirportBillDetailVo;
import com.swcares.aiot.core.model.vo.BmHandleAirportBillHisVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@LiteflowComponent(id = "CmpBmAirportBillHisAddSnapshot", name = "组件-补充历史记录对应快照详情")
public class CmpBmAirportBillHisAddSnapshot extends NodeComponent {
    @Resource
    private MsBmHandleAirportBillSnapshot msBmHandleAirportBillSnapshot;

    @Override
    public void process() throws Exception {
        log.info("【当前时间】：{},开始调用-组件-补充历史记录对应快照详情", DateTime.now());
        CxtBmAirportBillHistory cxtBmAirportBillHistory = this.getContextBean(CxtBmAirportBillHistory.class);
        //快照数据
        CxtBmBillAirportAutoCompareSnapshotList compareSnapshotList = this.getContextBean(CxtBmBillAirportAutoCompareSnapshotList.class);
        List<BillAirportAutoCompareSnapshot> snapshotList = compareSnapshotList.getBillAirportAutoCompareSnapshotList();
        //账单历史数据
        Page<BmHandleAirportBillHisVo> billHisVo = cxtBmAirportBillHistory.getBmHandleAirportBillHisVo();
        List<BmHandleAirportBillHisVo> hisVoRecords = billHisVo.getRecords();
        log.info("【当前时间】：{},账单历史数据:{}", DateTime.now(), JSONObject.toJSONString(billHisVo));

        if (CollUtil.isNotEmpty(hisVoRecords) && CollUtil.isNotEmpty(snapshotList)) {
            hisVoRecords.forEach(hisVoRecord -> {
                if (EnumBillOperation.AUTO_CHECK.getCode().equals(hisVoRecord.getOperation())) {
                    BillAirportAutoCompareSnapshot matchedSnapshot = snapshotList.stream()
                            .filter(snapshot -> hisVoRecord.getAirportAutoCompareSnapshotId() != null
                                    && Objects.equals(snapshot.getId(), hisVoRecord.getAirportAutoCompareSnapshotId()))
                            .findFirst()
                            .orElse(null);
                    log.info("【当前时间】：{},历史账单记录:{},匹配的快照:{}", DateTime.now(), hisVoRecord.getAirportFlightBillId(), matchedSnapshot);
                    // 如果找到匹配的快照，设置到 hisVoRecord 中
                    if (ObjectUtil.isNotEmpty(matchedSnapshot)) {
                        assert matchedSnapshot != null;
                        if (ObjectUtil.isNotEmpty(matchedSnapshot.getSnapshotData())) {
                            log.info("【当前时间】：{},历史账单记录:{},设置快照数据:{}", DateTime.now(), hisVoRecord.getAirportFlightBillId(), matchedSnapshot.getSnapshotData());
                            BmHandleAirportBillDetailVo bmHandleAirportBillDetailVo = msBmHandleAirportBillSnapshot.snapshot2Vo(matchedSnapshot, hisVoRecord);
                            hisVoRecord.setBmHandleAirportBillDetailVo(bmHandleAirportBillDetailVo);
                        }
                    }
                }
            });
            billHisVo.setRecords(hisVoRecords);
            // 分页数据
            cxtBmAirportBillHistory.setBmHandleAirportBillHisVo(billHisVo);
        }
    }
}


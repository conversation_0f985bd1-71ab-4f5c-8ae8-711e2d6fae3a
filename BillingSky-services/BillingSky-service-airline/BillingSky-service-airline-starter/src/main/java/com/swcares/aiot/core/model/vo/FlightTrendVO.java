package com.swcares.aiot.core.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * ClassName：FlightTrendVO
 * Description：客座率趋势和旅客总数趋势 VO
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2024/7/1 13:38
 * Version v1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode
@ApiModel(value = "FlightTrendVO", description = "客座率趋势和旅客总数趋势VO")
public class FlightTrendVO {

    @ApiModelProperty(name = "flightDate", value = "航班日期")
    private LocalDate flightDate;

    @ApiModelProperty(name = "plfAvg",value = "每日客座率平均值")
    private Double plfAvg;

    @ApiModelProperty(name = "totalNumber",value = "每日旅客总数量")
    private Integer totalNumber;
}

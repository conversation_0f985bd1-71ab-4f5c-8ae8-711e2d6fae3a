package com.swcares.aiot.core.liteflow.ctx;

import com.swcares.aiot.core.model.dto.BmCountAutoCompareDto;
import com.swcares.aiot.core.model.vo.BmCountAutoCompareVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * date 2025年04月09日14:02
 */
@ApiModel(value = "CxtBmCheckAutoCompare", description = "统计上下文")
@Accessors(chain = true)
@Data
public class CxtBmCountAutoCompare {
    @ApiModelProperty(value = "核对参数")
    private BmCountAutoCompareDto bmCountAutoCompareDto;

    @ApiModelProperty(value = "核对结果")
    private BmCountAutoCompareVo bmCountAutoCompareVo;
}

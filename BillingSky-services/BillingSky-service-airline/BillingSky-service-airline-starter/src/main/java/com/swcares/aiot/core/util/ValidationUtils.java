package com.swcares.aiot.core.util;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.HibernateValidator;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class ValidationUtils {

    /**
     * 使用hibernate的注解来进行验证
     */
    private static final Validator VALIDATOR = Validation.byProvider(HibernateValidator.class).configure().failFast(true).buildValidatorFactory().getValidator();

    /**
     * 功能描述: <br>
     * 〈注解验证参数〉
     *
     * @param obj :
     * @see  #valid(Object)
     * @since [产品/模块版本](可选)
     */
    public static <T> String valid(T obj) {
        Set<ConstraintViolation<T>> constraintViolations = VALIDATOR.validate(obj);
        // 抛出检验异常
        if (!constraintViolations.isEmpty()) {
            return constraintViolations.iterator().next().getMessage();
        }
        return null;
    }

}
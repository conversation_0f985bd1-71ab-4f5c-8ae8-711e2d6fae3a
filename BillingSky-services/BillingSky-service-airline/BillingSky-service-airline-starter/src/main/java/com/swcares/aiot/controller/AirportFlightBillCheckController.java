package com.swcares.aiot.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.AirlineServiceStarter;
import com.swcares.aiot.core.constants.SymbolCons;
import com.swcares.aiot.core.model.dto.*;
import com.swcares.aiot.core.model.vo.AirportFlightBillCheckPagedVO;
import com.swcares.aiot.core.model.vo.AirportFlightBillDetailVO;
import com.swcares.aiot.core.model.vo.AirportFlightBillPagedStatisticVO;
import com.swcares.aiot.core.model.vo.FlightBillExcelVO;
import com.swcares.aiot.core.util.EasyExcelUtil;
import com.swcares.aiot.service.IAirportFlightBillManageService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.components.sys.util.ConfigUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.util.List;


/**
 * ClassName：com.swcares.aiot.ass.core.model.controller.InspectionItemController <br>
 * Description： 检查内容相关接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021-08-25 <br>
 * @version v1.0 <br>
 */
@RestController
@ApiVersion(AirlineServiceStarter.SWAGGER_API_VERSION_INFO)
@RequestMapping("/bill/airport/flight/bill/check")
@Api(tags = "机场航班账单数据-账单核对")
public class AirportFlightBillCheckController extends BaseController {

    @Resource
    private IAirportFlightBillManageService airportFlightBillManageService;

    @PostMapping("/page")
    @ApiOperation(value = "账单核对-分页查询")
    public PagedResult<List<AirportFlightBillCheckPagedVO>> pageCheckList(@RequestBody @Validated AirportFlightBillCheckPagedDTO dto) {
        IPage<AirportFlightBillCheckPagedVO> result = airportFlightBillManageService.pageCheckList(dto);
        return ok(result);
    }

    @PostMapping("/statistic")
    @ApiOperation(value = "账单核对-统计求和")
    public BaseResult<AirportFlightBillPagedStatisticVO> checkStatistic(@RequestBody @Validated AirportFlightBillCheckPagedDTO dto) {
        AirportFlightBillPagedStatisticVO result = airportFlightBillManageService.checkStatistic(dto);
        return ok(result);
    }

    @GetMapping("/get/{id}")
    @ApiOperation(value = "账单核对-详情")
    public BaseResult<AirportFlightBillDetailVO> getCheckDetail(@PathVariable @ApiParam(value = "主键id", required = true) Long id) {
        AirportFlightBillDetailVO vo = airportFlightBillManageService.getCheckDetail(id);
        return ok(vo);
    }

    @PostMapping("/auto")
    @ApiOperation(value = "账单核对-自动核对")
    public BaseResult<Object> autoCheckAirportBill(@RequestBody @Validated AirportFlightBillAutoCheckDTO dto) {
        airportFlightBillManageService.autoCheckAirportBill(dto);
        return ok();
    }

    @PostMapping("/next")
    @ApiOperation(value = "账单核对-查询下一个需要核对的数据")
    public BaseResult<AirportFlightBillDetailVO> getNextDataToCheck(@RequestBody @Validated AirportFlightBillCheckQueryDTO dto) {
        AirportFlightBillDetailVO byId = airportFlightBillManageService.getNextDataToCheck(dto);
        return ok(byId);
    }

    @PostMapping("/export")
    @ApiOperation(value = "账单导出查询导出")
    public void export(@RequestBody @Validated AirportFlightBillDetailQueryDTO dto, HttpServletResponse response) {
        List<FlightBillExcelVO> result = airportFlightBillManageService.listExportDetailData(dto);
        String billingCycle = dto.getStartDate().format(DatePattern.NORM_DATE_FORMATTER) + SymbolCons.DASH
                + dto.getEndDate().format(DatePattern.NORM_DATE_FORMATTER);
        String fileName = billingCycle + SymbolCons.DASH
                + ConfigUtil.getString("tenant_settle_code_map", "settle_code", TenantHolder.getTenant())
                + SymbolCons.DASH + LocalDate.now().format(DatePattern.NORM_DATE_FORMATTER);
        EasyExcelUtil.responseExcel(response, fileName, "sheet1", FlightBillExcelVO.class, result, CollUtil.toList(new LongestMatchColumnWidthStyleStrategy()));
    }

    @GetMapping("/billRecord")
    @ApiOperation(value = "账单记录")
    public BaseResult<Object> billRecord(@RequestParam Long airportFlightBillId, @RequestParam @NotBlank String airportCode, @RequestParam Integer page) {
        return ok(airportFlightBillManageService.billRecord(airportFlightBillId, airportCode, page * 10));
    }

    @PostMapping("/operate")
    @ApiOperation(value = "操作账单")
    public BaseResult<Object> operate(@RequestBody @Validated AirlineFlightBillOperateDto dto) {
        airportFlightBillManageService.operate(dto);
        return ok();
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除账单")
    public BaseResult<Object> delete(@PathVariable Long id) {
        airportFlightBillManageService.delete(id);
        return ok();
    }
}

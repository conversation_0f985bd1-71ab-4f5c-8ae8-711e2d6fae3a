package com.swcares.aiot.core.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * <AUTHOR>
 */
@Configuration
public class LiteflowThreadPoolConfig {
    @Bean(name = "liteflowThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(10);
        // 最大线程数
        executor.setMaxPoolSize(50);
        // 队列容量
        executor.setQueueCapacity(1024);
        // 线程池中的线程名前缀
        executor.setThreadNamePrefix("liteflow自动核对线程池-");
        // 当线程池关闭时，是否等待所有任务完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待时间（当线程池关闭时，等待任务完成的最大时间）
        executor.setAwaitTerminationSeconds(60);
        // 拒绝策略
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        // 初始化线程池
        executor.initialize();
        return executor;
    }
}

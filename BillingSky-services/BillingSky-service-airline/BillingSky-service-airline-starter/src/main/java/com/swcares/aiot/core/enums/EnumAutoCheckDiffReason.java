package com.swcares.aiot.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * date 2025年04月15日17:17
 */
@Getter
@AllArgsConstructor
public enum EnumAutoCheckDiffReason {
    DUPLICATE_BILL_601(601, "账单重复", "重复账单，账单ID：{}"),
    BILL_FINDING_FLIGHT_FAILED_621(621, "账单寻找航班失败", "账单寻找航班失败，未匹配到航班数据"),
    BILL_FINDING_FLIGHT_FAILED_622(622, "账单寻找航班失败", "账单寻找航班失败，账单起降时间与航司航班起降时间差异过大"),
    AIRPORT_NOT_CONF_FEE_641(641, "机场未配置此费用项", "{}未配置此费用项"),
    FEE_SCOPE_NOT_MATCH_642(642, "费用项适用范围不符", "账单对应航班数据与此费用项适用范围不符，不产生此费用项"),
    AIRLINE_AMOUNT_CALC_ERROR_643(643, "航司计算金额出现问题", "航司计算金额出现问题"),
    FEE_FORMULA_NOT_EXIST_644(644, "费用项未配置公式", "账单费用项未配置公式"),
    FEE_FORMULA_NOT_MATCH_645(645, "费用项无匹配的费用公式", "费用项无匹配的费用公式"),
    BILL_CALC_NOT_EQUALS_661(661, "账单金额不一致", "账单金额与航司金额不相等"),
    BILL_CALC_NOT_MATCH_662(662, "账单金额超出合理范围", "账单金额超出合理范围");

    private final Integer code;
    private final String desc;
    private final String reason;

    public static EnumAutoCheckDiffReason getEnumByCode(Integer code) {
        return Arrays.stream(EnumAutoCheckDiffReason.values()).filter(enumAutoCheckDiffReason -> enumAutoCheckDiffReason.getCode().equals(code)).findAny().orElse(null);
    }
}

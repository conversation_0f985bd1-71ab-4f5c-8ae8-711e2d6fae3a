package com.swcares.aiot.core.model.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * ClassName：FlightReportFormDTO
 * Description：航班客座率报表 分页数据传输对象
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * date 2024/6/25 13:59
 * Version v1.0
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FlightReportFormPageDTO", description = "航班客座率报表分页查询对象")
public class FlightReportFormPageDTO extends PagedDTO implements Serializable {

    @NotNull(message = "航班日期不能为空")
    @ApiModelProperty(value = "航班日期（开始时间）", required = true)
    private LocalDate startFlightDate;

    @NotNull(message = "航班日期不能为空")
    @ApiModelProperty(value = "航班日期（结束时间）", required = true)
    private LocalDate endFlightDate;

    @ApiModelProperty(name = "flightNo",value = "航班号")
    private String flightNo;

}

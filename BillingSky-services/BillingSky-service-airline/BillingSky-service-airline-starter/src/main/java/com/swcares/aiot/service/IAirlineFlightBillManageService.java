package com.swcares.aiot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.model.dto.AirlineFlightBillPagedDTO;
import com.swcares.aiot.core.model.dto.AirportFlightBillAutoCheckDTO;
import com.swcares.aiot.core.model.vo.AirlineFlightBillPagedVO;
import com.swcares.aiot.core.model.vo.AirlineFlightBillStatisticVO;
import com.swcares.aiot.core.model.vo.FeeCodeVO;
import com.swcares.aiot.core.model.vo.FlightBillAutoCheckVO;

import java.util.List;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title ：IAirlineFlightBillManageService <br>
 * Package ：com.swcares.aiot.ass.business.bill.service <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * date 2024年 04月18日 9:53 <br>
 * @version v1.0 <br>
 */
public interface IAirlineFlightBillManageService {

    IPage<AirlineFlightBillPagedVO> page(AirlineFlightBillPagedDTO dto);

    AirlineFlightBillStatisticVO pageStatistic(AirlineFlightBillPagedDTO dto);

    List<FlightBillAutoCheckVO> listAutoCheckDataList(AirportFlightBillAutoCheckDTO dto);

    List<FeeCodeVO> listFeeCode();
}

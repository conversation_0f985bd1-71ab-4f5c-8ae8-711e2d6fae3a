package com.swcares.aiot.core.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "AirportFlightBillSyncDto", description = "机场账单接收参数")
public class AirportFlightBillSyncDto {
    @JSONField(name = "id")
    private String flightBillId;
    private String airportCode;
    private String airlineShortName;
    private BigDecimal chargePrice;
    private String feeCode;
    private String feeName;
    private String flightFlag;
    private String flightLine;
    private String flightNo;
    private String flightSegment;
    private LocalDateTime flightTime;
    private String fromAirportCode;
    private BigDecimal pricingAmount;
    private String regNo;
    private LocalDate settleMonth;
    private String toAirportCode;
    private BigDecimal unitPrice;
    private LocalDate flightDate;
    private Long flightId;
    private String flightLineType;
    private String flightModel;
    private String flightSegmentType;
    private String settleCode;
    private String taskFlag;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer status;
    private String comment;
    private String pdfUrl;
    private String createdBy;
    private LocalDateTime createdTime;
    private String updatedBy;
    private LocalDateTime updatedTime;
    private BigDecimal airlineChargePrice;
    private Integer airlineOrigin;
    private String indicatorCode;
    private String indicatorName;
    private String indicatorValue;
    private String indicatorDefaultValue;
    private Integer revocation;
    private String dataOrigin;
    private String primitiveId;
    private String actualOperationAirport;
    private String payAirline;
    private String actualOperationAirline;
    private String occurAddress;
    private LocalDateTime feeStartTime;
    private LocalDateTime feeEndTime;
    private String taskType;
    private String flightStatus;
    private String billingAccount;
    private String billingOrder;
    private String submit;
    private String feedback;
    private String proveFile;
}

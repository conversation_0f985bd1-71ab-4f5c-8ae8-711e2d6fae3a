package com.swcares.aiot.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.core.model.dto.AirlineFlightInfoPagedDto;
import com.swcares.aiot.core.model.vo.AirlineFlightInfoCalculateVo;
import com.swcares.aiot.core.model.vo.AirlineFlightInfoVo;
import com.swcares.aiot.core.model.dto.AirlineFlightInfoQueryDTO;
import com.swcares.aiot.core.entity.AirlineFlightInfo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.ass.business.base.mapper.AirlineFlightInfoMapper <br>
 * Description：航司航班信息表 Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2024-03-12 <br>
 * @version v1.0 <br>
 */
public interface AirlineFlightInfoManageMapper {

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：code-generator <br>
     * date：2024-03-12 <br>
     *
     * @param dto  :
     * @param page <br>
     * @return <br>
     */
    IPage<AirlineFlightInfoVo> page(@Param("airlineFlightInfoPagedDto") AirlineFlightInfoPagedDto dto, Page<AirlineFlightInfoVo> page);

    /**
     * Title : list<br>
     * Author : code-generator<br>
     * Description :  航司航班信息表_列表查询 <br>
     *
     * @param dto 查询对象
     *            date : 2024-03-12 <br>
     *            return: List<AirlineFlightInfoVO>
     */
    List<AirlineFlightInfoVo> list(@Param("dto") AirlineFlightInfoQueryDTO dto);

    AirlineFlightInfo getDepBeforeAirlineFlightInfo(@Param("depFlightTime") LocalDateTime depFlightTime, @Param("regNo") String regNo, @Param("id") Long id);

    AirlineFlightInfo getArrAfterAirlineFlightInfo(@Param("arrivalDateTime") LocalDateTime arrivalDateTime, @Param("regNo") String regNo, @Param("id") Long id);

    List<AirlineFlightInfoCalculateVo> listFlightInfoByTimeAndNo(@Param("flightNos") List<String> flightNos, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime);

    List<AirlineFlightInfo> getExistAirlineFlightInfo(@Param("keys") List<String> keys);
}

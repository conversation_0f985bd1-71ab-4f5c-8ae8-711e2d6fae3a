package com.swcares.aiot.service.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.swcares.aiot.service.AirportCodeService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.util.RedisUtil;
import com.swcares.components.api.bd.BaseDataCommonRemoteService;
import com.swcares.components.bd.vo.AirportInfoComboVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * ClassName：com.swcares.aiot.ass.business.bill.service.impl.AirportCodeServiceImpl
 * Description：
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/8/2 09:42
 * @version v1.0
 */
@Service
public class AirportCodeServiceImpl implements AirportCodeService {
    @Resource
    private BaseDataCommonRemoteService baseDataCommonRemoteService;
    @Resource
    private RedisUtil redisUtil;

    @Cached(name = "AirportCode", cacheType = CacheType.BOTH, expire = 1800)
    @Override
    public String getNameByCode(String airportCode) {
        String airportName=redisUtil.get(airportCode);
        if(airportName==null) {
            BaseResult<List<AirportInfoComboVO>> combos = baseDataCommonRemoteService.getCombo(null);
            Optional<AirportInfoComboVO> airp = combos.getData().stream().filter(v -> v.getCode().equalsIgnoreCase(airportCode)).findAny();
            if (airp.isPresent()) {
                airportName = airp.get().getAirportName();
                redisUtil.set(airportCode, airportName, 1800);
            }
        }
        return airportName;
    }
}

package com.swcares.aiot.core.model.dto;

import cn.hutool.core.annotation.Alias;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AirportBillCollateDto {
    @Alias("错误信息")
    @ApiModelProperty(value = "错误信息")
    private String errMsg;

    @Alias("机场原始ID号")
    @ApiModelProperty(value = "机场原始ID号")
    private String primitiveId;

    @Alias("开账机场名称")
    @ApiModelProperty(value = "机场三字码")
    private String airportCode;

    @Alias("实际运营机场名称")
    @ApiModelProperty(value = "实际运营机场名称")
    private String actualOperationAirport;

    @ApiModelProperty(value = "付款航空公司")
    @Alias("付款航空公司")
    private String payAirline;

    @ApiModelProperty(value = "实际运营航空公司")
    @Alias("实际运营航空公司")
    private String actualOperationAirline;

    @ApiModelProperty(value = "航班日期")
    @Alias("航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航班号")
    @Alias("航班号")
    private String flightNo;

    @ApiModelProperty(value = "飞机注册号")
    @Alias("机号")
    private String regNo;

    @ApiModelProperty(value = "机型")
    @Alias("机型")
    private String flightModel;

    @ApiModelProperty(value = "航线")
    @Alias("航线")
    private String flightLine;

    @ApiModelProperty(value = "航线性质 国际:I|国内:D")
    @Alias("航线性质")
    private String flightLineType;

    @ApiModelProperty(value = "航段")
    @Alias("航段")
    private String flightSegment;

    @ApiModelProperty(value = "航段性质 国际:I|国内:D")
    @Alias("航段性质")
    private String flightSegmentType;

    @ApiModelProperty(value = "发生地")
    @Alias("发生地")
    private String occurAddress;

    @ApiModelProperty(value = "费用开始时间")
    @Alias("费用开始时间")
    private LocalDateTime feeStartTime;

    @ApiModelProperty(value = "费用结束时间")
    @Alias("费用结束时间")
    private LocalDateTime feeEndTime;

    @ApiModelProperty(value = "起降时间")
    @Alias("起降时间")
    private LocalDateTime flightTime;

    @ApiModelProperty(value = "起降标识")
    @Alias("起降标识")
    private String flightFlag;

    @ApiModelProperty(value = "任务性质")
    @Alias("任务性质")
    private String taskType;

    @ApiModelProperty(value = "航班状态")
    @Alias("航班状态")
    private String flightStatus;

    @ApiModelProperty(value = "费用代码")
    @Alias("费用代码")
    private String feeCode;

    @ApiModelProperty(value = "计价量 计价量条件|=0 计价量指标项目的数量|=1 和归集方式相关")
    @Alias("数量")
    private String pricingAmount;

    @ApiModelProperty(value = "收费单价")
    @Alias("单价")
    private String unitPrice;

    @ApiModelProperty(value = "收费金额")
    @Alias("金额")
    private String chargePrice;

    @ApiModelProperty(value = "费用名称")
    @Alias("备注")
    private String feeName;

    @ApiModelProperty(value = "开账序号")
    @Alias("开账序号")
    private String billingAccount;

    @ApiModelProperty(value = "开账账单号")
    @Alias("开账账单号")
    private String billingOrder;
}

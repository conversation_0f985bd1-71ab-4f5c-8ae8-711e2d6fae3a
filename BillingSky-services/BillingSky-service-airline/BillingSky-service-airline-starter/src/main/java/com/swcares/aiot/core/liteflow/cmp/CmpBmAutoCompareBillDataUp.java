package com.swcares.aiot.core.liteflow.cmp;

import com.swcares.aiot.core.entity.BillAirportFlightBill;
import com.swcares.aiot.core.enums.EnumAuditWay;
import com.swcares.aiot.core.enums.EnumAutoCheckDiffReason;
import com.swcares.aiot.core.liteflow.ctx.CtxBmAutoCompareBillDataUp;
import com.swcares.aiot.core.liteflow.ctx.CtxBmAutoCompareDataSnapshot;
import com.swcares.aiot.core.liteflow.ctx.CxtBmHandleAutoCompare;
import com.swcares.aiot.core.service.IBillAirportFlightBillService;
import com.swcares.aiot.statemachine.biz.status.EnumBillStatus;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * date 2025年04月11日13:10
 */
@Slf4j
@LiteflowComponent(id = "CmpBmAutoCompareBillDataUp", name = "账单数据更新为有差异")
public class CmpBmAutoCompareBillDataUp extends NodeComponent {
    @Resource
    private IBillAirportFlightBillService iBillAirportFlightBillService;

    @Override
    public void process() {
        // 获取传入的参数-循环对象
        BillAirportFlightBill billAirportFlightBill = this.getSubChainReqData();
        //
        CtxBmAutoCompareBillDataUp ctxBmAutoCompareBillDataUp = this.getContextBean(CtxBmAutoCompareBillDataUp.class);
        // 具体的错误
        EnumAutoCheckDiffReason enumAutoCheckDiffReason = ctxBmAutoCompareBillDataUp.getEnumAutoCheckDiffReason();
        // 更新账单状态为有差异
        billAirportFlightBill.setStatus(EnumBillStatus.DISCREPANCY.getStatus());
        billAirportFlightBill.setAutoCheckDifferenceCode(enumAutoCheckDiffReason.getCode());
        billAirportFlightBill.setAutoCheckDifferenceReason(enumAutoCheckDiffReason.getDesc());
        billAirportFlightBill.setAuditWay(EnumAuditWay.SYSTEM_COMPARISON.getCode());
        iBillAirportFlightBillService.updateById(billAirportFlightBill);
        log.info("更新账单状态为有差异");
    }
}

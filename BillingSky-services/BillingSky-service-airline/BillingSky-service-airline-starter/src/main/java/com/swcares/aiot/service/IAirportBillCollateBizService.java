package com.swcares.aiot.service;

import com.swcares.aiot.core.entity.BillAirportFlightBill;
import com.swcares.aiot.core.model.dto.AirlineCodeOrderDto;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface IAirportBillCollateBizService {
    void templateDownload(HttpServletResponse response, String fullName) throws IOException;

    Integer uploadFileCheck(MultipartFile file) throws Throwable;

    Boolean uploadFileSave(MultipartFile file) throws Throwable;

    List<BillAirportFlightBill> getBillAirportFlightBills(List<BillAirportFlightBill> billAirportFlightBills, List<AirlineCodeOrderDto> airlineCodeOrderConfig, Set<String> lastEightNums);
}

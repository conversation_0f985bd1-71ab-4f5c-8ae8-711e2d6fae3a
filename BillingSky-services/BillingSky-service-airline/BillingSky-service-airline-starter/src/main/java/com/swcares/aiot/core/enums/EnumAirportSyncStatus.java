package com.swcares.aiot.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR> by yaodan
 **/
@Getter
@AllArgsConstructor
public enum EnumAirportSyncStatus {
    NOT_SYNC(0, "无需同步"),
    AIRLINE_HANDLING(1, "航司处理中"),
    AIRPORT_APPLY_REVOCATION(2, "机场申请撤回"),
    FEEDBACK_AIRPORT(3, "已反馈机场"),
    REVOCATION_AIRPORT(4, "已撤回机场");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 编码值
     * @return 枚举对象
     */
    public static EnumAirportSyncStatus ofCode(Integer code) {
        return Arrays.stream(EnumAirportSyncStatus.values()).filter(val -> val.code.equals(code)).findAny().orElse(null);
    }
}

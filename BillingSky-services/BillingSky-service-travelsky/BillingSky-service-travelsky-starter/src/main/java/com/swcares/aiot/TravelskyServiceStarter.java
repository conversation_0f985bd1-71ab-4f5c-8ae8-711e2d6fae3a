package com.swcares.aiot;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * ClassName：com.swcares.aiot.pes.PesServiceImplApplication <br>
 * Description：启动器-Travelsky <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2024年3月8日 下午4:34:49 <br>
 * @version v1.0 <br>
 */
@Slf4j
@SpringBootApplication(scanBasePackages = {TravelskyServiceStarter.DEFAULT_PACKAGE})
@MapperScan("com.swcares.aiot.**.mapper")
@EnableFeignClients(basePackages = TravelskyServiceStarter.DEFAULT_PACKAGE)
@EnableTransactionManagement
public class TravelskyServiceStarter {
    public static final String DEFAULT_PACKAGE = "com.swcares";

    public static void main(String[] args) {
        SpringApplication.run(TravelskyServiceStarter.class, args);
    }
}

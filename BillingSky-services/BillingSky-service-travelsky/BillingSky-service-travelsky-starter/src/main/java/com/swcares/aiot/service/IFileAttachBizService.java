package com.swcares.aiot.service;

import com.swcares.aiot.core.entity.BillFileAttachment;
import com.swcares.aiot.core.enums.EnumFileBusinessType;
import com.swcares.aiot.file.vo.AttachmentVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title ：IFileAttachmentBizService <br>
 * Package ：com.swcares.aiot.service <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 * Description : 附件处理service <br>
 *
 * <AUTHOR> <br>
 * date 2024年 09月03日 20:03 <br>
 * @version v1.0 <br>
 */
public interface IFileAttachBizService {

    String uploadFile(MultipartFile file, Integer businessType);

    String uploadPdfFile(ByteArrayInputStream file, String fileName, Integer businessType, String contentType);

    void assFileByFileKeyList(List<String> fileKeyList, EnumFileBusinessType businessType, Long tableId);

    void assFileByFileKeyList(List<String> currentFileKeyList, EnumFileBusinessType businessType, Long tableId, Long tableId2);

    List<BillFileAttachment> queryFileList(EnumFileBusinessType businessType, Long tableId);

    List<BillFileAttachment> queryFileList(Integer fileType, List<Long> tableIdList);

    void downloadFile(String fileKey, Long tableId, HttpServletRequest request, HttpServletResponse response) throws UnsupportedEncodingException;

    AttachmentVO uploadAttachment(MultipartFile file, Integer fileType);
}

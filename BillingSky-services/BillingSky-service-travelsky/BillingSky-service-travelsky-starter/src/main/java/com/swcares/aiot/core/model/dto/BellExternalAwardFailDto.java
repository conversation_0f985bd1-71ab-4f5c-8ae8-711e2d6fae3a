package com.swcares.aiot.core.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@ApiModel("小铃铛消息-外航奖励")
public class BellExternalAwardFailDto {
    @ApiModelProperty("消息标题")
    private String title;
    @ApiModelProperty("消息日期时间")
    private LocalDateTime msgDateTime;
    @ApiModelProperty("账期")
    private String paymentPeriod;
    @ApiModelProperty("机场")
    private String airportName;
    @ApiModelProperty("分支机构")
    private String branch;
    @ApiModelProperty("客户名称")
    private String customerName;
    @ApiModelProperty("公司名称")
    private String corporationName;
}

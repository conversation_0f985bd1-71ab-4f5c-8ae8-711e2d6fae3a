package com.swcares.aiot.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.cons.ConsTravelsky;
import com.swcares.aiot.core.model.dto.*;
import com.swcares.aiot.core.model.vo.DocumentHistoryRetrieveVo;
import com.swcares.aiot.core.model.vo.ExternalAirlineAwardProveVO;
import com.swcares.aiot.core.model.vo.ExternalAwardInfoVo;
import com.swcares.aiot.service.IHeadquartersExternalAwardBizService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@ApiVersion(ConsTravelsky.CONS_TRAVELSKY_API_VERSION)
@RestController
@RequestMapping("/headquartersExternalAwardBiz")
@Api(tags = "控制器-外航奖励账单（总部）")
public class HeadquartersExternalAwardBizController {

    @Resource
    private IHeadquartersExternalAwardBizService iHeadquartersExternalAwardBizService;

    @GetMapping("/templateDownload")
    @ApiOperation(value = "下载-模板文件下载")
    public void templateDownload(HttpServletResponse response) throws IOException {
        iHeadquartersExternalAwardBizService.templateDownload(response, "templates/excel/外航奖励客户财务信息明细表-模板.xlsx");
    }

    @PostMapping(value = "/uploadFileCheck")
    @ApiOperation(value = "上传-外航奖励账单上传文件校验", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BaseResult<Integer> uploadFileCheck(@RequestPart("file") MultipartFile file) throws IOException {
        return BaseResult.ok(iHeadquartersExternalAwardBizService.uploadFileCheck(file));
    }

    @PostMapping("/uploadFileSave")
    @ApiOperation(value = "上传-账单上传文件保存", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BaseResult<Boolean> uploadFileSave(@RequestPart("file") MultipartFile file) throws IOException {
        return BaseResult.ok(iHeadquartersExternalAwardBizService.uploadFileSave(file));
    }

    @PostMapping("/pageExternalAward")
    @ApiOperation(value = "查询-分页查询")
    public BaseResult<IPage<ExternalAirlineAwardDTO>> pageExternalAward(@RequestBody @Validated ExternalAwardPageDto estimateBillPageDto) {
        IPage<ExternalAirlineAwardDTO> page = iHeadquartersExternalAwardBizService.pageExternalAward(estimateBillPageDto);
        return BaseResult.ok(page);
    }

    @PutMapping("/distributeExternalAward")
    @ApiOperation(value = "更新-分发外航奖励账单")
    public BaseResult<Boolean> distributeExternalAward(@Validated @RequestBody @NotNull List<String> ids) {
        return BaseResult.ok(iHeadquartersExternalAwardBizService.distributeExternalAward(ids));
    }

    @GetMapping("/infoExternalAward/{id}")
    @ApiOperation(value = "查询-外航奖励账单详情")
    public BaseResult<ExternalAwardInfoVo> infoExternalAward(@PathVariable("id") String id) {
        return BaseResult.ok(iHeadquartersExternalAwardBizService.infoExternalAward(id));
    }

    @PostMapping("/updateExternalAward")
    @ApiOperation(value = "更新-账单编辑")
    public BaseResult<Boolean> updateExternalAward(@Valid @RequestBody ExternalAwardUpdateDto externalAwardUpdateDto) {
        Boolean rt = iHeadquartersExternalAwardBizService.updateExternalAward(externalAwardUpdateDto);
        return BaseResult.ok(rt);
    }

    @PostMapping("/objection")
    @ApiOperation(value = "异议")
    public BaseResult<Boolean> objection(@Validated @RequestBody ObjectionBillDTO dto) {
        return BaseResult.ok(iHeadquartersExternalAwardBizService.objection(dto));
    }

    @PostMapping("/submitReimbursement")
    @ApiOperation(value = "更新-提交报销")
    public BaseResult<Boolean> submitReimbursement(@Validated @RequestBody @NotNull List<String> ids) {
        iHeadquartersExternalAwardBizService.submitReimbursement(ids);
        return BaseResult.ok();
    }

    @DeleteMapping("/deleteExternalAward/{id}")
    @ApiOperation(value = "通过id删除账单")
    @ApiParam(value = "主键id", required = true)
    public BaseResult<Boolean> deleteExternalAward(@PathVariable("id") String id) {
        return BaseResult.ok(iHeadquartersExternalAwardBizService.deleteExternalAward(id));
    }

    @PostMapping(value = "/batchEvidenceImport")
    @ApiOperation(value = "上传-批量存证导入")
    public BaseResult<Boolean> batchEvidenceImport(@RequestBody @Validated BatchCertificateImportDto dto) {
        Boolean batched = iHeadquartersExternalAwardBizService.batchEvidenceImport(dto);
        return BaseResult.ok(batched);
    }

    @PostMapping(value = "/billEvidenceDetails/{id}")
    @ApiOperation(value = "账单存证详情")
    public BaseResult<List<ExternalAirlineAwardProveVO>> billEvidenceDetails(@PathVariable("id") Long id) {
        List<ExternalAirlineAwardProveVO> externalAirlineAwardProveVos = iHeadquartersExternalAwardBizService.billDepositDetails(id);
        return BaseResult.ok(externalAirlineAwardProveVos);
    }

    @PostMapping("/exportExternalAward")
    @ApiOperation(value = "查询-账单下载")
    public void exportExternalAward(@RequestBody @Validated ExternalAwardExportDto externalAwardExportDto, HttpServletResponse response) throws IOException {
        iHeadquartersExternalAwardBizService.exportExternalAward(externalAwardExportDto, response);
    }

    @ApiOperation(value = "查询-获取存证历史")
    @GetMapping("/retrieveDocumentHistories")
    public BaseResult<List<DocumentHistoryRetrieveVo>> retrieveDocumentHistories() {
        List<DocumentHistoryRetrieveVo> documentHistoryRetrieveVos = iHeadquartersExternalAwardBizService.retrieveDocumentHistories();
        return BaseResult.ok(documentHistoryRetrieveVos);
    }

    @PostMapping("/downloadZip")
    @ApiOperation(value = "总部批量下载发票和附件")
    public void downloadZip(@RequestBody @Valid ExternalAwardDownloadZipDto downloadZipDto, HttpServletResponse response) {
        iHeadquartersExternalAwardBizService.downloadZip(downloadZipDto, response);
    }

    @GetMapping("/getPaymentPeriod")
    @ApiOperation(value = "获取账期及Office")
    public BaseResult<Map<String, List<String>>> getPaymentPeriod() {
        return BaseResult.ok(iHeadquartersExternalAwardBizService.getPaymentPeriod());
    }
}

/*
 Navicat Premium Data Transfer

 Source Server         : [对账通][test]*************
 Source Server Type    : MySQL
 Source Server Version : 80032
 Source Host           : *************:3306
 Source Schema         : bls_travelsky

 Target Server Type    : MySQL
 Target Server Version : 80032
 File Encoding         : 65001

 Date: 24/07/2024 16:36:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for estimate_bill
-- ----------------------------
DROP TABLE IF EXISTS `estimate_bill`;
CREATE TABLE `estimate_bill`
(
    `id`                     bigint                                                        NOT NULL COMMENT 'id',
    `payment_period`         varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NOT NULL COMMENT '账期',
    `organization_id`        bigint                                                        NULL     DEFAULT NULL COMMENT '分支机构id',
    `organization_name`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '分支机构',
    `airport_name`           varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '机场名称',
    `airport_code`           varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NULL     DEFAULT NULL COMMENT '三字码',
    `customer_name`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '客户名称',
    `area_name`              varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '地区',
    `open_account_name`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '开户名称',
    `open_account_bank`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '开户行名',
    `open_account`           varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '开户账号',
    `agreement_num`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '协议编号',
    `system_guarantee`       varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '系统保障',
    `amount_sg`              decimal(16, 2)                                                NULL     DEFAULT NULL COMMENT '上年度系统保障服务费付款金额',
    `amount_pid`             decimal(16, 2)                                                NULL     DEFAULT NULL COMMENT '上年度离港PID维护费收款金额',
    `estimate_bill_time`     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '本次预估账单时间',
    `amount_invoice`         decimal(16, 2)                                                NULL     DEFAULT NULL COMMENT '本次应付系统保障服务费金额(开发票金额)',
    `amount_maintenance`     decimal(16, 2)                                                NULL     DEFAULT NULL COMMENT '本次应收离港PID维护费金额',
    `status`                 tinyint                                                       NOT NULL DEFAULT 1 COMMENT '账单状态（1：已导入 2：待分支确认 3：有异议 4：待机场确认 5：已确认）',
    `headquarters_send_time` datetime                                                      NULL     DEFAULT NULL COMMENT '总部账单分发时间',
    `branch_send_time`       datetime                                                      NULL     DEFAULT NULL COMMENT '分支账单分发时间',
    `remarks`                varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注',
    `pdf`                    text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         NULL COMMENT '数据溯源',
    `deleted`                tinyint(1)                                                    NOT NULL COMMENT '删除标识（1：删除 0：正常）',
    `created_by`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '创建人',
    `created_time`           datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '更新人',
    `updated_time`           datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '离港返还账单表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for organization_email
-- ----------------------------
DROP TABLE IF EXISTS `organization_email`;
CREATE TABLE `organization_email`
(
    `id`              int                                                          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `organization_id` bigint                                                       NULL     DEFAULT NULL COMMENT '机构id',
    `email`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '邮箱',
    `created_by`      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '创建者',
    `updated_by`      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '更新者',
    `created_date`    timestamp                                                    NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_date`    timestamp                                                    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 12
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '机构邮箱表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of organization_email
-- ----------------------------

-- ----------------------------
-- Table structure for sys_config_info
-- ----------------------------
DROP TABLE IF EXISTS `sys_config_info`;
CREATE TABLE `sys_config_info`
(
    `id`           bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_name`  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
    `config_key`   varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '参数键',
    `status`       tinyint(1)                                                    NULL     DEFAULT 1 COMMENT '状态（1-启用、0-停用）',
    `is_sys`       char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL COMMENT '系统内置（1是 0否）',
    `is_single`    tinyint(1)                                                    NOT NULL DEFAULT 1 COMMENT '是否单数值（1-是，表示数据值可以多个；0-不是）',
    `remark`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '备注信息',
    `created_by`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '创建者',
    `created_time` datetime                                                      NOT NULL COMMENT '创建时间',
    `updated_by`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '更新者',
    `updated_time` datetime                                                      NOT NULL COMMENT '更新时间',
    `deleted`      tinyint(1)                                                    NULL     DEFAULT 0,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 5
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '参数配置表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_config_info
-- ----------------------------
INSERT INTO `sys_config_info`
VALUES (1, '父机构id', 'parent_id', 1, '1', 0, NULL, '1', '2022-04-06 14:07:19', '1', '2022-04-06 14:07:22', 0);
INSERT INTO `sys_config_info`
VALUES (2, '第三方签名设置', 'signature_config', 1, '1', 0, NULL, '1', '2024-07-17 14:13:16', '1',
        '2024-07-17 14:13:16', 0);
INSERT INTO `sys_config_info`
VALUES (3, '租户映射关系临时存储方案', 'tenant_config', 1, '1', 0, NULL, '1', '2024-07-17 14:13:16', '1',
        '2024-07-17 14:13:16', 0);
INSERT INTO `sys_config_info`
VALUES (4, '租户id结算code映射关系临时存储方案', 'tenant_settle_code_map', 1, '1', 0, NULL, '1', '2024-07-17 14:13:16',
        '1', '2024-07-17 14:13:16', 0);

-- ----------------------------
-- Table structure for sys_config_value
-- ----------------------------
DROP TABLE IF EXISTS `sys_config_value`;
CREATE TABLE `sys_config_value`
(
    `id`           bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_id`    bigint                                                        NOT NULL COMMENT '配置主键',
    `corp_code`    bigint                                                        NULL     DEFAULT 0 COMMENT '租户ID',
    `value_key`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '数值键',
    `config_value` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '参数值',
    `status`       tinyint(1)                                                    NULL     DEFAULT 1 COMMENT '状态(1-启用、0-停用)',
    `created_by`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '数据创建者的用户名',
    `created_time` timestamp                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据的创建时间',
    `updated_by`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '数据最后修改者的用户名',
    `updated_time` timestamp                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据最后修改的时间',
    `deleted`      tinyint(1)                                                    NOT NULL DEFAULT 0 COMMENT '删除标识（1-删除、0-未删除）',
    `remark`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 27
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '配置参数值管理'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_config_value
-- ----------------------------
INSERT INTO `sys_config_value`
VALUES (1, 1, 0, 'parent_id', '1755120045843664896', 1, '1', '2022-04-06 22:07:19', '1', '2022-04-06 22:07:19', 0,
        NULL);
INSERT INTO `sys_config_value`
VALUES (23, 2, 0, 'enable', 'true', 1, '1', '2024-07-17 14:30:03', '1', '2024-07-17 14:30:03', 0, NULL);
INSERT INTO `sys_config_value`
VALUES (24, 2, 1752164509045407744, 'ciphertext', '37289bbb41e6e0737057cc7fce5d8c60', 1, '1', '2024-07-17 14:30:03',
        '1', '2024-07-17 14:30:03', 0, NULL);
INSERT INTO `sys_config_value`
VALUES (25, 3, 0, 'TRAVELSKY', '1752164509045407744', 1, '1', '2024-07-17 14:30:03', '1', '2024-07-17 14:30:03', 0,
        NULL);
INSERT INTO `sys_config_value`
VALUES (26, 4, 1752164509045407744, 'settle_code', 'TRAVELSKY', 1, '1', '2024-07-17 14:30:03', '1',
        '2024-07-17 14:30:03', 0, NULL);

-- ----------------------------
-- Table structure for sys_dictionary_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dictionary_data`;
CREATE TABLE `sys_dictionary_data`
(
    `id`           bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '字典数据ID',
    `dict_type`    varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典类型',
    `data_key`     varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '数据KEY',
    `dict_label`   varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典标签',
    `dict_value`   varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典键值',
    `sort_num`     decimal(10, 0)                                                NOT NULL COMMENT '排序号（升序）',
    `is_sys`       char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL COMMENT '系统内置（1是 0否）',
    `is_default`   char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NULL     DEFAULT NULL COMMENT '是否默认',
    `status`       char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NULL     DEFAULT NULL COMMENT '状态（1正常 0暂停）',
    `description`  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '字典描述',
    `remark`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '备注信息',
    `corp_code`    bigint                                                        NOT NULL DEFAULT 0 COMMENT '租户代码',
    `created_by`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '创建者',
    `created_time` datetime                                                      NOT NULL COMMENT '创建时间',
    `updated_by`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '更新者',
    `updated_time` datetime                                                      NOT NULL COMMENT '更新时间',
    `deleted`      tinyint(1)                                                    NOT NULL DEFAULT 0 COMMENT '删除状态(0-未删除、1-已删除)',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2976
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '字典数据表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dictionary_data
-- ----------------------------

-- ----------------------------
-- Table structure for sys_dictionary_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dictionary_type`;
CREATE TABLE `sys_dictionary_type`
(
    `id`           bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '字典类型ID',
    `dict_name`    varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典名称',
    `dict_type`    varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典类型',
    `is_sys`       char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL COMMENT '是否系统字典',
    `status`       char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '1' COMMENT '状态（1正常 0停用）',
    `created_by`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '创建者',
    `created_time` datetime                                                      NOT NULL COMMENT '创建时间',
    `updated_by`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '更新者',
    `updated_time` datetime                                                      NOT NULL COMMENT '更新时间',
    `remark`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '备注信息',
    `deleted`      tinyint(1)                                                    NOT NULL DEFAULT 0 COMMENT '删除状态(0-未删除、1-已删除)',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 82
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '字典类型表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dictionary_type
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;

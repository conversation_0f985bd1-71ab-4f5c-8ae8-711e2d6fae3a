package com.swcares.aiot.core.service.impl;

import com.swcares.aiot.core.entity.AgreementCallback;
import com.swcares.aiot.core.mapper.AgreementCallbackMapper;
import com.swcares.aiot.core.service.IAgreementCallbackService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 协议回调地址列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Service
public class AgreementCallbackServiceImpl extends ServiceImpl<AgreementCallbackMapper, AgreementCallback> implements IAgreementCallbackService {

}

package com.swcares.aiot.core.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class CustomersWithLabelGetVo {
    @ApiModelProperty("客户ID")
    private Long customerId;

    @ApiModelProperty(value = "甲方名称", example = "四川航空")
    private String customerName;

    @ApiModelProperty(value = "类型编码", example = "AIRLINE")
    private String classCode;

    @ApiModelProperty(value = "客户编码", example = "3U001")
    private String customerIata;

    @ApiModelProperty(value = "客户列表")
    private List<AgreementOwnerGetDetailVo> agreementOwnerGetDetailVos;
}

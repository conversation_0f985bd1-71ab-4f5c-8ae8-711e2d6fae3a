package com.swcares.aiot.core.partition;

import cn.hutool.core.util.RandomUtil;
import org.springframework.cloud.stream.binder.PartitionSelectorStrategy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class EnginePartitionSelector implements PartitionSelectorStrategy {
    @Override
    public int selectPartition(Object key, int partitionCount) {
        return RandomUtil.randomInt(partitionCount);
    }
}

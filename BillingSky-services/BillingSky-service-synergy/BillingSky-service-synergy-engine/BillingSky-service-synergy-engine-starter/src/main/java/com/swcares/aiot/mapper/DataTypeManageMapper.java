package com.swcares.aiot.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.dto.AgreementDataTypeDto;
import com.swcares.aiot.core.dto.DataTypePageDto;
import com.swcares.aiot.core.vo.DataTypePageVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DataTypeManageMapper {
    /**
     * @param id 数据类型id
     * @return 当前数据类型的所有协议数据
     */
    List<AgreementDataTypeDto> getAgreementDataType(@Param("id") Long id);

    /**
     * 分页查询数据类型
     *
     * @param page            分页对象
     * @param dataTypePageDto 查询对象
     */
    IPage<DataTypePageVo> pageDataType(IPage<DataTypePageVo> page,@Param("dataTypePageDto") DataTypePageDto dataTypePageDto);
}

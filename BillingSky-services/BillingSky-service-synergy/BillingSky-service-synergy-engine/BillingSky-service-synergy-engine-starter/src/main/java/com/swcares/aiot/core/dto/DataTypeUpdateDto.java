package com.swcares.aiot.core.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * Dto-数据类型-更新
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataTypeUpdateDto extends DataTypeSaveDto {
    @NotNull(message = "id不能为空")
    @ApiModelProperty(value = "主键ID")
    private Long id;
}

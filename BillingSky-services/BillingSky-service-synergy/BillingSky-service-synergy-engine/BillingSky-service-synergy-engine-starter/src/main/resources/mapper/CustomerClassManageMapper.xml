<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.swcares.aiot.mapper.CustomerClassManageMapper">
    <resultMap id="customerClassPageVoResultMap" type="com.swcares.aiot.core.vo.CustomerClassPageVo">
        <id property="id" column="id"/>
        <result property="className" column="class_name"/>
        <result property="classCode" column="class_code"/>
    </resultMap>

    <select id="pageCustomerClass" resultMap="customerClassPageVoResultMap">
        SELECT cc.id,
               cc.class_name,
               cc.class_code
        FROM customer_class cc
        <where>
            cc.deleted = 0
            <if test="customerClassPageDto.condition != null and customerClassPageDto.condition != ''">
                and (cc.class_code LIKE concat('%', #{customerClassPageDto.condition}, '%') OR
                     cc.class_name LIKE concat('%', #{customerClassPageDto.condition}, '%'))
            </if>
        </where>
    </select>
</mapper>
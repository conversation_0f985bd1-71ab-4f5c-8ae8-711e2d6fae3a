management:
  endpoints:
    web:
      exposure:
        include: "prometheus,health"
      base-path: /actuator
  metrics:
    tags:
      application: ${spring.application.name}
#spring:
#  boot:
#    admin:
#      client:
#        url: http://localhost:17900/monitor
info:
  author: 对账通项目组
  app_name: @project.artifactId@
  version: @project.version@
  company: 西南凯亚

# 配置有路径的访问方式
spring:
  cloud:
    nacos:
      discovery:
        metadata:
          management.context-path: /${spring.application.name}/actuator
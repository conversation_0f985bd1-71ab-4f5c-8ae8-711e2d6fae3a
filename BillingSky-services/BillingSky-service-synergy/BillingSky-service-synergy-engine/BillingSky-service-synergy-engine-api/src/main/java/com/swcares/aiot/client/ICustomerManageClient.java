package com.swcares.aiot.client;

import com.swcares.aiot.fallback.CustomerManageFallback;
import com.swcares.aiot.model.vo.CustomerAgreementDataCodeVo;
import com.swcares.baseframe.common.base.BaseResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(value = "sess",
        contextId = "iCustomerManageClient",
        path = "/sess/customerManage",
        fallbackFactory = CustomerManageFallback.class)
public interface ICustomerManageClient {
    /**
     * 获取客户能发送的数据类型是否合法
     *
     * @param sendCustCode 发送客户的编码
     * @return 所有数据类型
     */
    @GetMapping("/protocolDataTypes/{sendCustCode}")
    BaseResult<List<CustomerAgreementDataCodeVo>> protocolDataTypes(@PathVariable("sendCustCode") String sendCustCode);

    /**
     * 获取客户的签名密钥
     *
     * @param customer 客户
     * @return 密钥
     */
    @GetMapping("/retrieveCustomerSignCiphertext/{customer}")
    BaseResult<?> retrieveCustomerSignCiphertext(@PathVariable("customer") String customer);
}

#======================log配置===============================#
logging:
  # 设置分组
  group:
    # worm组为自己开发程序的日志
    worm:
      - com.swcares
    # 系统组的日志
    system:
      - org.springframework
  # 对分组设置日志
  level:
    # 定义根日志的级别
    root: info
    # worm组的日志级别
    worm: debug
    # 未再次定义system系统组的日志级别，则使用根的日志级别
    #system: debug
  # 设置日志的模板格式
  #pattern:
    #    %m输出的信息,%p日志级别,%t线程名,%d日期,%c类的全名,%i索引【从数字0开始递增】,,,
    #    格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度 %msg：日志消息，%n是换行符,%logger{50}:是class的全名,后面的数字代表限制最长的字符
    #    %d == %d{yyyy-MM-dd HH:mm:ss.SSS}
    #    %p == %-5level 字符个数有差别
    #    %m == %msg
    #    %c == %logger{50} %c不简写，%logger{50}有简写
    #    有简写情况：org.springframework.boot.web.embedded.tomcat.TomcatWebServer 被简写为：o.s.boot.web.embedded.tomcat.TomcatWebServer
    #    %t == %thread
    #    %i 索引【从数字0开始递增】
    # 控制台的日志模板格式(使用默认的格式，先不复写)
    #console: "%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-50.50logger{50}){cyan} %clr(:){faint} %clr(\\(%file:%line\\)){blue} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"
    # 文件中的日志格式(使用默认的格式，先不复写)
    #file: ${logging.pattern.console}
  file:
    # 归档文件的删除是在日志输出过程中执行的，这个属性可以指定应用启动时是否执行一次归档文档（因日志时间或大小）的删除，true 为执行，false 为不执行。
    clean-history-on-start: true
    # 归档日志文件保留天数，归档时间超出保留天数范围的归档文件会被删除。
    max-history: 7
    # 日志文件（日志文件 + 归档文件）总大小，使用 KB/MB/GB/... 为单位进行指定；日志文件总大小超过设置的值，会删除老（旧）的归档文件，直到日志文件总大小小于设置的值。
    total-size-cap: 1GB
    # 日志名
    name: logs/logback/logback.slf4j/${spring.application.name}.log
    # 日志文件大小最大值，使用 KB/MB/GB/... 为单位进行指定；日志文件大小超过设置的最大值，会进行日志归档（按日志大小归档）。
    max-size: 50MB

#使用文件创建镜像
FROM 192.168.17.80/library/jdk:n1.8
#作者信息
MAINTAINER "姚文兵 <EMAIL>"
ENV PROJECT_PATH=/usr/local/project
ENV PATH=$PATH:$PROJECT_PATH
ENV TZ=Asia/Shanghai
ENV LANG en_US.utf8
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
#设定工作目录
WORKDIR $PROJECT_PATH
#拷贝工程
COPY target/BillingSky-service-synergy-sensor-starter.jar ./BillingSky-service-synergy-sensor-starter.jar
#容器暴露端口（可以暴露的，容器启动时确定具体暴露的端口）
EXPOSE 8003
#执行的命令
ENTRYPOINT ["java","-jar","BillingSky-service-synergy-sensor-starter.jar"]
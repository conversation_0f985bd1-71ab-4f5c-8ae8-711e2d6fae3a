version: '3.0'
services:
  BillingSky-service-synergy-sensor-starter:
    # 镜像地址(这个是开发环境的构建镜像)
    #build:
    #context: .
    #dockerfile: ./Dockerfile
    # 这个是正式的环境
    image: harbor.kaiya.com:30443/psp/BillingSky-service-synergy-sensor-starter:${version}
    # 容器名称
    container_name: BillingSky-service-synergy-sensor-starter
    # 服务挂掉的情况下自动重启（目前先不启用，稳定后启用）
    restart: on-failure
    # 网络策略使用共享主机网络策略
    network_mode:
      bridge
    # 暴露端口
    ports:
      - "8003:8003"
    env_file:
      - project_${profile}.env
    # 设置BillingSky-service-synergy-sensor-starter的数据映射
    volumes:
      - /opt/logs/BillingSky-service-synergy-sensor-starter:/opt/logs/BillingSky-service-synergy-sensor-starter
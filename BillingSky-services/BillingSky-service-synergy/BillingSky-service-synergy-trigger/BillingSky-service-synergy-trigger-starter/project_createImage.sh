moduleName="bls"
projectName="BillingSky-service-synergy-sensor-starter"
currentDateTime=$(date "+%Y%m%d%H%M%S")
echo "${currentDateTime}删除BillingSky-service-synergy-sensor-starter镜像"
docker rmi -f $(docker images "*/*/${projectName}" -aq)
echo "${currentDateTime}使用本地环境打包镜像"
docker build -f ./Dockerfile -t harbor.kaiya.com:30443/${moduleName}/${projectName}:latest .
docker build -f ./Dockerfile -t harbor.kaiya.com:30443/${moduleName}/${projectName}:$1 .
echo "${currentDateTime}构建进行完成，版本：$1"
echo "${currentDateTime}登录harbor服务器"
docker login --username=admin --password=kaiya@harbor harbor.kaiya.com:30443
echo "${currentDateTime}推送镜像到远程的harbor"
docker push harbor.kaiya.com:30443/${moduleName}/${projectName}:latest
docker push harbor.kaiya.com:30443/${moduleName}/${projectName}:$1
echo "${currentDateTime}推送镜像到远程的harbor完成，版本：$1"
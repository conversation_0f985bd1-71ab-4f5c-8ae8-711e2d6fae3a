package com.swcares.aiot.core.scs.sink;

import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.service.ITriggerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class SinkMessage {
    @Resource
    private ITriggerService<Object> iTriggerService;

    @Bean
    public Consumer<?> sink() {
        return message -> {
            log.info(CharSequenceUtil.format("{}======消费端消息:{}", LocalDateTime.now(), message));
            iTriggerService.dispatchMessage(message);
        };
    }
}

package com.swcares.aiot.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alicp.jetcache.support.StatInfo;
import com.swcares.aiot.ComplexMessage;
import com.swcares.aiot.client.ICustomerManageClient;
import com.swcares.aiot.core.cons.TriggerConstant;
import com.swcares.aiot.core.entity.MessageHistoryDispatchData;
import com.swcares.aiot.core.entity.MessageHistoryDispatchSource;
import com.swcares.aiot.core.service.IMessageHistoryDispatchSourceService;
import com.swcares.aiot.core.statemachine.constants.StateMachineConstant;
import com.swcares.aiot.core.statemachine.events.EnumDataPushStatusChangeEvent;
import com.swcares.aiot.core.statemachine.template.StateMachineTemplate;
import com.swcares.aiot.header.MessageHeader;
import com.swcares.aiot.message.MessageTemplate;
import com.swcares.aiot.model.enums.EnumDataPushStatus;
import com.swcares.aiot.service.ITriggerService;
import com.swcares.aiot.utils.LocalDateTimeUtils;
import com.swcares.baseframe.common.base.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TriggerServiceImpl<T> implements ITriggerService<T> {
    @Resource
    private IMessageHistoryDispatchSourceService iMessageHistoryDispatchSourceService;

    @Resource
    private StateMachineTemplate stateMachineTemplate;

    @Resource
    private ICustomerManageClient iCustomerManageClient;

    @Resource
    private MessageTemplate messageTemplate;

    @Resource
    private RestTemplate restTemplate;

    @Override
    public void dispatchMessage(T msg) {
        // 状态信息我们不处理
        if (msg.getClass() != StatInfo.class) {
            log.info(CharSequenceUtil.format("消费类型：{},具体的消息:{}", msg.getClass().getName(), JSONUtil.toJsonStr(msg)));
            ComplexMessage message = JSONUtil.toBean(JSONUtil.toJsonStr(msg), ComplexMessage.class);
            // 记录接收到的具体消息
            this.recordSourceDispatchMessage().accept(message);
            // 构造最终要发送的所有数据
            log.info("构造所有要发送的数据");
            List<ComplexMessage> waitingSendMessages = this.rebuildDispatchMessageCollections().apply(message);
            // 每个消息转换为具体得实例对象
            List<MessageHistoryDispatchData> messageHistoryDispatchDatas = this.buildMessageHistoryDispatchDatas().apply(waitingSendMessages);
            // 启用多线程进行数据的分发
            log.info("开始分发消息");
            this.dispatchMessageHistoryDispatchData().accept(messageHistoryDispatchDatas);
        }
    }

    /**
     * 通过复杂消息列表转换为具体得对象列表
     *
     * @return 消息对应得对象列表
     */
    private Function<List<ComplexMessage>, List<MessageHistoryDispatchData>> buildMessageHistoryDispatchDatas() {
        return complexMessages -> complexMessages.stream().map(complexMessage -> this.buildMessageHistoryDispatchData().apply(complexMessage)).collect(Collectors.toList());
    }

    /**
     * 构建所有要发送的数据
     */
    private Function<ComplexMessage, List<ComplexMessage>> rebuildDispatchMessageCollections() {
        return message -> {
            // 所有的目标地址
            List<String> targetCustCodes = message.getHeader().getTargetCustCodes();
            // 所有的回调地址
            Map<String, List<String>> callbackUrls = message.getCallbackUrls();
            // 扁平化处理
            return targetCustCodes.stream()
                    .flatMap((Function<String, Stream<ComplexMessage>>) targetCustCode -> {
                        List<String> cbs = callbackUrls.get(targetCustCode);
                        return cbs.stream().map(callbackUrl -> {
                            ComplexMessage complexMessage = ObjectUtil.clone(message);
                            // 目标地址-单个
                            complexMessage.getHeader().setTargetCustCodes(CollUtil.toList(targetCustCode));
                            // 回调地址-单个
                            Map<String, List<String>> callbackUrlOnly = new HashMap<>();
                            callbackUrlOnly.put(targetCustCode, CollUtil.toList(callbackUrl));
                            complexMessage.setCallbackUrls(callbackUrlOnly);
                            return complexMessage;
                        });
                    }).collect(Collectors.toList());
        };
    }

    /**
     * 记录接收到的具体消息
     *
     * @return 操作对象
     */
    private Consumer<ComplexMessage> recordSourceDispatchMessage() {
        return message -> {
            log.info(CharSequenceUtil.format("{}=======数据库记录原始接收到的消息:{}", LocalDateTimeUtil.now(), JSONUtil.toJsonStr(message)));
            MessageHistoryDispatchSource messageHistoryDispatchSource = new MessageHistoryDispatchSource();
            // 头信息
            MessageHeader messageHeader = message.getHeader();
            BeanUtil.copyProperties(messageHeader, messageHistoryDispatchSource);
            // 回调信息
            String callbacks = JSONUtil.toJsonStr(message.getCallbackUrls());
            messageHistoryDispatchSource.setCallbackUrls(callbacks);
            // 目的地址
            messageHistoryDispatchSource.setTargetCustCode(String.join(StrPool.COMMA, CollUtil.emptyIfNull(messageHeader.getTargetCustCodes())));
            // 原始数据
            messageHistoryDispatchSource.setSourceData(JSONUtil.toJsonStr(message));
            // 用户信息
            messageHistoryDispatchSource.setCreatedBy(message.getUsername());
            messageHistoryDispatchSource.setUpdatedBy(message.getUsername());
            iMessageHistoryDispatchSourceService.save(messageHistoryDispatchSource);
        };
    }

    /**
     * 具体发送目标消息，有2步需要完成
     * 第一步：需要修改原始目标为单个地址
     * 第二步：发送简单消息到各个目标
     * 注意：以上操作都要记录数据库
     */
    @Override
    public Consumer<List<MessageHistoryDispatchData>> dispatchMessageHistoryDispatchData() {
        return messageHistoryDispatchDatas -> messageHistoryDispatchDatas.forEach(messageHistoryDispatchData -> {
            Future<String> future = ThreadUtil.execAsync(() -> {
                // 目标
                String callbackUrls = messageHistoryDispatchData.getCallbackUrls();
                List<?> addresses = JSONUtil.parseObj(callbackUrls).get(messageHistoryDispatchData.getTargetCustCode(), List.class);
                String address = String.valueOf(addresses.stream().findFirst().orElse(null));
                // 推送
                if (CharSequenceUtil.isNotBlank(address)) {
                    log.info("{}======开始推送：推送地址为====={}", LocalDateTimeUtil.now(), address);
                    log.info("推送的具体数据为:{}", JSONUtil.toJsonStr(messageHistoryDispatchData));
                    assert address != null;
                    // 重试次数+1
                    messageHistoryDispatchData.setRetryNum(messageHistoryDispatchData.getRetryNum() + 1);
                    // 更新时间变更为最新
                    messageHistoryDispatchData.setUpdatedTime(LocalDateTimeUtils.ofCtt());
                    // 获得当前客户的密钥
                    try {
                        // 将对象构建为消息
                        ComplexMessage complexMessage = JSONUtil.toBean(messageHistoryDispatchData.getSourceData(), ComplexMessage.class);
                        ResponseEntity<String> responseEntity = restTemplate.postForEntity(address, complexMessage, String.class);
                        String rt = responseEntity.getBody();
                        log.info("{}======推送得结果：======{}", LocalDateTimeUtils.ofCtt(), rt);
                        return rt;
                    } catch (Exception e) {
                        log.error("{}======基础框架发生异常，我不管呢！", LocalDateTimeUtils.ofCtt(), e);
                        return e.getMessage();
                    }
                }
                return null;
            });

            // 我只关心发送是否成功了没(有响应就是成功了，这个地方要看一下结果),推送成功
            if (ObjectUtil.isNotNull(future)) {
                try {
                    String rt = future.get();
                    log.info("{}推送结果消息：{}", LocalDateTimeUtils.ofCtt(), rt);
                    if (CharSequenceUtil.isNotBlank(rt)) {
                        // 不管成功与否，都记录返回结果
                        messageHistoryDispatchData.setResponseContent(rt);
                        // 判断返回结果是否为json
                        boolean rtJson = JSONUtil.isJson(rt);
                        // 如果是json就解析结果
                        if (rtJson) {
                            // json的状态码是规定的字符串
                            Boolean ok = this.getStatusInfo(rt);
                            if (Boolean.TRUE.equals(ok)) {
                                String key = this.obligationKey().apply(messageHistoryDispatchData, StateMachineConstant.PUSH_OK_TRANSITION);
                                // 推送成功事件
                                stateMachineTemplate.sendEvent(EnumDataPushStatusChangeEvent.PUSH_OK, messageHistoryDispatchData, key);
                            } else {
                                String key = this.obligationKey().apply(messageHistoryDispatchData, StateMachineConstant.PUSH_FAIL_TRANSITION);
                                // 推送失败事件
                                stateMachineTemplate.sendEvent(EnumDataPushStatusChangeEvent.PUSH_FAIL, messageHistoryDispatchData, key);
                            }
                        }
                        // 如果不是json，就看字符串是否为success
                        else {
                            // 如果是规定的有效字符
                            if (TriggerConstant.EFFECTIVE_RESULT.contains(rt.toUpperCase())) {
                                String key = this.obligationKey().apply(messageHistoryDispatchData, StateMachineConstant.PUSH_OK_TRANSITION);
                                // 推送成功事件
                                stateMachineTemplate.sendEvent(EnumDataPushStatusChangeEvent.PUSH_OK, messageHistoryDispatchData, key);
                            }
                            // 其他为推送失败事件
                            else {
                                String key = this.obligationKey().apply(messageHistoryDispatchData, StateMachineConstant.PUSH_FAIL_TRANSITION);
                                // 推送失败事件
                                stateMachineTemplate.sendEvent(EnumDataPushStatusChangeEvent.PUSH_FAIL, messageHistoryDispatchData, key);
                            }
                        }
                    } else {
                        messageHistoryDispatchData.setResponseContent(CharSequenceUtil.EMPTY);
                        String key = this.obligationKey().apply(messageHistoryDispatchData, StateMachineConstant.PUSH_FAIL_TRANSITION);
                        // 推送失败事件
                        stateMachineTemplate.sendEvent(EnumDataPushStatusChangeEvent.PUSH_FAIL, messageHistoryDispatchData, key);
                    }
                } catch (InterruptedException | ExecutionException e) {
                    log.error(CharSequenceUtil.format("发生异常：{}", e));
                    Thread.currentThread().interrupt();
                    messageHistoryDispatchData.setResponseContent(CharSequenceUtil.EMPTY);
                    String key = this.obligationKey().apply(messageHistoryDispatchData, StateMachineConstant.PUSH_FAIL_TRANSITION);
                    // 推送失败事件
                    stateMachineTemplate.sendEvent(EnumDataPushStatusChangeEvent.PUSH_FAIL, messageHistoryDispatchData, key);
                }
            }
            // 推送失败
            else {
                messageHistoryDispatchData.setResponseContent(CharSequenceUtil.EMPTY);
                String key = this.obligationKey().apply(messageHistoryDispatchData, StateMachineConstant.PUSH_FAIL_TRANSITION);
                // 推送失败事件
                stateMachineTemplate.sendEvent(EnumDataPushStatusChangeEvent.PUSH_FAIL, messageHistoryDispatchData, key);
            }
        });
    }

    /**
     * 生成业务id
     */
    @Override
    public BiFunction<MessageHistoryDispatchData, String, String> obligationKey() {
        return (messageHistoryDispatchData, key) -> {
            String newKey = CharSequenceUtil.format("{}:{}", key, messageHistoryDispatchData.hashCode());
            log.info("{}======实际在使用的Key：{}", LocalDateTimeUtils.ofCtt(), newKey);
            return newKey;
        };
    }

    /**
     * 从头部信息获取成功标志
     *
     * @param rt 返回的记过
     * @return 是否有规定的成功标志
     */
    private Boolean getStatusInfo(String rt) {
        return TriggerConstant.HEADER_FIELD_NAME.stream().filter(fieldName -> {
            String isOk = JSONUtil.parseObj(rt).getStr(fieldName);
            return CharSequenceUtil.isNotBlank(isOk) && TriggerConstant.EFFECTIVE_RESULT.contains(isOk.toUpperCase());
        }).count() > BigDecimal.ZERO.intValue();
    }


    /**
     * 构建初始对象
     *
     * @return 初始对象
     */
    private Function<ComplexMessage, MessageHistoryDispatchData> buildMessageHistoryDispatchData() {
        return message -> {
            MessageHistoryDispatchData messageHistoryDispatchData = new MessageHistoryDispatchData();
            String targetCustCode = message.getHeader().getTargetCustCodes().stream().findFirst().orElse(CharSequenceUtil.EMPTY);
            // 获取密钥
            if (CharSequenceUtil.isNotBlank(targetCustCode)) {
                try {
                    BaseResult<?> baseResult = iCustomerManageClient.retrieveCustomerSignCiphertext(targetCustCode);
                    String signCiphertext = baseResult.getData().toString();
                    if (CharSequenceUtil.isNotBlank(signCiphertext)) {
                        message = (ComplexMessage) messageTemplate.generateSign(signCiphertext, message);
                        // 消息签名
                        messageHistoryDispatchData.setSign(message.getHeader().getSign());
                    }
                } catch (Exception e) {
                    log.error("当前的异常先手动处理了!", e);
                }
            }
            // 基础信息
            BeanUtil.copyProperties(message.getHeader(), messageHistoryDispatchData);
            // 消息创建者
            messageHistoryDispatchData.setCreatedBy(message.getUsername());
            // 消息更新者
            messageHistoryDispatchData.setUpdatedBy(message.getUsername());
            // 原始数据
            messageHistoryDispatchData.setSourceData(JSONUtil.toJsonStr(message));
            // 目标地址
            messageHistoryDispatchData.setTargetCustCode(String.join(StrPool.COMMA, message.getHeader().getTargetCustCodes()));
            // 回调地址
            messageHistoryDispatchData.setCallbackUrls(JSONUtil.toJsonStr(message.getCallbackUrls()));
            // 保存状态(待推送)
            messageHistoryDispatchData.setDataStatus(EnumDataPushStatus.PUSH_WAITING.getStatus());
            messageHistoryDispatchData.setStatusDesc(EnumDataPushStatus.PUSH_WAITING.getDesc());
            // 重试次数（默认为0）
            messageHistoryDispatchData.setRetryNum(BigDecimal.ZERO.intValue());
            return messageHistoryDispatchData;
        };
    }
}

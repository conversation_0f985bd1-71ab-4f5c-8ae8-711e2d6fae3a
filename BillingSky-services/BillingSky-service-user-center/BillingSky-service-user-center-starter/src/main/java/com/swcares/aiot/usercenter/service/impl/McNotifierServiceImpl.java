package com.swcares.aiot.usercenter.service.impl;

import com.google.common.collect.Sets;
import com.swcares.aiot.usercenter.dto.GetMcNotifierDTO;
import com.swcares.aiot.usercenter.mapper.McNotifierMapper;
import com.swcares.aiot.usercenter.service.McNotifierService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * ClassName：com.swcares.aiot.usercenter.service.impl.McNotifierServiceImpl <br>
 * Description：接口实现 <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024/11/26 16:21 <br>
 * @version v1.0 <br>
 */

@Service
public class McNotifierServiceImpl implements McNotifierService {
    @Resource
    private McNotifierMapper mcNotifierMapper;

    @Override
    public Set<Long> getEmpIdList(GetMcNotifierDTO dto) {
        Set<Long> empIds = Sets.newHashSet();

        // 获取机构下的人员id
        if (CollectionUtils.isNotEmpty(dto.getOrgIdList())) {
            List<Long> orgEmpIds = mcNotifierMapper.selectOrgEmpIdList(dto.getOrgIdList());
            if (CollectionUtils.isNotEmpty(orgEmpIds)) {
                empIds.addAll(orgEmpIds);
            }
        }

        // 获取角色下的人员id
        if (CollectionUtils.isNotEmpty(dto.getRoleIdList())) {
            List<Long> roleEmpIds = mcNotifierMapper.selectRoleEmpIdList(dto.getRoleIdList());
            if (CollectionUtils.isNotEmpty(roleEmpIds)) {
                empIds.addAll(roleEmpIds);
            }
        }

        return empIds;
    }
}

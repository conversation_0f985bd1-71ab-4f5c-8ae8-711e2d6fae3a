package com.swcares.aiot.usercenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.usercenter.dto.GetMcNotifierDTO <br>
 * Description：获取机构、角色人员请求参数 <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024/11/26 15:09 <br>
 * @version v1.0 <br>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@ApiModel(value="GetMcNotifierDTO", description="获取机构、角色人员请求参数")
public class GetMcNotifierDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机构id列表")
    private List<Long> orgIdList;

    @ApiModelProperty(value = "角色id列表")
    private List<Long> roleIdList;
}

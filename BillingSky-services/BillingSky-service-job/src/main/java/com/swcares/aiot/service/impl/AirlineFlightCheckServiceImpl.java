package com.swcares.aiot.service.impl;

import com.swcares.aiot.client.IFlightDataCheckClient;
import com.swcares.aiot.service.AirlineFlightCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AirlineFlightCheckServiceImpl implements AirlineFlightCheckService {

    @Resource
    private IFlightDataCheckClient flightDataCheckClient;

    @Override
    public void flightCheck() {
        flightDataCheckClient.flightCheck();
    }
}

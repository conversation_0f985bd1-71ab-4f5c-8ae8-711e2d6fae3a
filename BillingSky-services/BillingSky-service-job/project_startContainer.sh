projectName="job-service"
basePath=$(
  cd $(dirname $0)
  pwd
)
echo "当前目录路径:$basePath"
echo "重新部署${projectName}系统容器"
# ========================================================================
echo "删除${projectName}系统容器"
containerId=$(docker ps -a | grep -i ${projectName} | awk '{print $12}')
if [ ! "${containerId}" ]; then
  echo "${projectName} container is null"
else
  docker rm -f ${projectName}
  echo "${projectName}系统容器删除成功"
fi
# ========================================================================
echo "删除本地${projectName}镜像"
imageId=$(docker images | grep -i ${projectName} | awk '{print $3}')
if [ ! "${imageId}" ]; then
  echo "${projectName} image is null"
else
  docker rmi -f "${imageId}"
  echo "${projectName}镜像删除成功"
fi
# ========================================================================
echo "上传到服务器的镜像压缩包重新加载为镜像"
docker load </root/deploy/services/$projectName.tar
# echo "因为镜像为私有的，所以必须要登录：登录镜像仓库"
#docker login --username=admin --password=kaiya@harbor harbor.kaiya.com:30443
# ========================================================================
echo "重新构建容器所有容器对象"
# -d 后端运行容器对象，并且打印容器名
# -f 指定编排的文件
version=$1 profile=pro docker-compose -f $basePath/project_docker-compose.yml up -d

package com.swcares.aiot.service.impl;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * ClassName：com.swcares.aiot.service.impl.AdeFlightInfoServiceImplTest <br>
 * Description： <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> 扬 <br>
 * date 2024/5/7 16:51 <br>
 * @since V <br>
 */
@SpringBootTest
class AdeFlightInfoServiceImplTest {
    @Resource
    private AdeFlightInfoBizServiceImpl adeFlightInfoBizServiceImpl;

    @Test
    void pullAdeFlightInfo() {
        adeFlightInfoBizServiceImpl.pullAdeFlightInfo();
        Assertions.assertTrue(true);
    }
}
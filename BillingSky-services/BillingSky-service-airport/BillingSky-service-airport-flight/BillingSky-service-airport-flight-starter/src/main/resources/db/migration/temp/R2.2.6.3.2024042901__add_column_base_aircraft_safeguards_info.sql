DROP  PROCEDURE IF EXISTS add_column_procedure;
DE<PERSON><PERSON><PERSON>ER $$
CREATE  PROCEDURE add_column_procedure()
BEGIN
select DATABASE() into @db_name;
IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE  table_schema=@db_name and  table_name='base_aircraft_safeguards_info' AND column_name= 'arr_task')
	THEN
ALTER TABLE base_aircraft_safeguards_info ADD `arr_task` varchar(32) DEFAULT NULL COMMENT '降落航班任务表示';
END IF;

IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE  table_schema=@db_name and  table_name='base_aircraft_safeguards_info' AND column_name= 'dep_task')
	THEN
ALTER TABLE base_aircraft_safeguards_info ADD `dep_task` varchar(32) DEFAULT NULL COMMENT '起飞航班任务表示';
END IF;

END $$
DELIMITER ;

CALL add_column_procedure;

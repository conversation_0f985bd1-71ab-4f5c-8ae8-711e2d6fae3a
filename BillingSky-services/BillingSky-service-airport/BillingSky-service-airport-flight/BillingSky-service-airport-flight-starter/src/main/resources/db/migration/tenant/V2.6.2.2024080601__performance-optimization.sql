DROP PROCEDURE IF EXISTS `add_index_proc`;
/* 添加索引 */
DELIMITER $$
CREATE PROCEDURE add_index_proc()
BEGIN
    select DATABASE() into @db_name;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND table_name='base_aircraft_safeguards_info' AND index_name='idx_basi_arrival_status')
    THEN
        create index idx_basi_arrival_status on base_aircraft_safeguards_info(arrival_status) ;
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND table_name='base_flight_info' AND index_name='idx_bfi_flight_date')
    THEN
        create index idx_bfi_flight_date on base_flight_info(flight_date DESC);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND table_name='base_flight_cargo' AND index_name='idx_bfc_base_flight_id')
    THEN
        create index idx_bfc_base_flight_id on base_flight_cargo(base_flight_id);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND table_name='base_aircraft_safeguards_info' AND  column_name= 'landing_datetime')
    THEN
        ALTER TABLE base_aircraft_safeguards_info ADD `landing_datetime` datetime DEFAULT NULL COMMENT '到达时间（计算结果）';
        UPDATE base_aircraft_safeguards_info SET landing_datetime = COALESCE(real_landing_datetime, predict_landing_datetime, plan_landing_datetime);
        create index idx_basi_landing_datetime on base_aircraft_safeguards_info(landing_datetime);
    END IF;
    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND table_name='base_aircraft_safeguards_info' AND  column_name= 'take_off_datetime')
    THEN
        ALTER TABLE base_aircraft_safeguards_info ADD `take_off_datetime` datetime DEFAULT NULL COMMENT '起飞时间（计算结果）';
        UPDATE base_aircraft_safeguards_info SET take_off_datetime = coalesce(cobt,real_take_off_datetime,predict_take_off_datetime,plan_take_off_datetime);
        create index idx_basi_take_off_datetime on base_aircraft_safeguards_info(take_off_datetime);
    END IF;
    IF NOT EXISTS(SELECT * FROM information_schema.statistics WHERE table_schema=@db_name AND table_name='base_aircraft_safeguards_info' AND index_name= 'idx_basi_landing_takeoff_datetime')
    THEN
        CREATE INDEX idx_basi_landing_takeoff_datetime ON base_aircraft_safeguards_info (deleted,
                                                                                         plan_landing_datetime,
                                                                                         real_landing_datetime,
                                                                                         plan_take_off_datetime,
                                                                                         real_take_off_datetime
            );
    END IF;
END $$
DELIMITER ;

CALL add_index_proc;
DROP PROCEDURE IF EXISTS `add_index_proc`;
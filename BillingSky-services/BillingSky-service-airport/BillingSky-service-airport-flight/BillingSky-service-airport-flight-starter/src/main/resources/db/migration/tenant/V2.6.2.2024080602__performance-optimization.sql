DROP PROCEDURE IF EXISTS `add_index_proc`;
/* 添加索引 */
DELIMITER $$
CREATE PROCEDURE add_index_proc()
BEGIN
    select DATABASE() into @db_name;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='asa_flight_parking_record' AND
        index_name='idx_fpr_flight_date_time')
    THEN
        create index idx_fpr_flight_date_time on asa_flight_parking_record(flight_date_time);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='flight_data_check' AND
        index_name='idx_fdc_flight_date')
    THEN
        create index idx_fdc_flight_date on flight_data_check(flight_date);
    END IF;

    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_flight_info' AND
        index_name='idx_base_flight_info_flight_plan_landing_datetime')
    THEN
        create index idx_base_flight_info_flight_plan_landing_datetime on base_flight_info(plan_landing_datetime);
    END IF;

    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_flight_info' AND
        index_name='idx_base_flight_info_flight_plan_take_off_datetime')
    THEN
        create index idx_base_flight_info_flight_plan_take_off_datetime on base_flight_info(plan_take_off_datetime);
    END IF;
END $$
DELIMITER ;

CALL add_index_proc;
DROP PROCEDURE IF EXISTS `add_index_proc`;
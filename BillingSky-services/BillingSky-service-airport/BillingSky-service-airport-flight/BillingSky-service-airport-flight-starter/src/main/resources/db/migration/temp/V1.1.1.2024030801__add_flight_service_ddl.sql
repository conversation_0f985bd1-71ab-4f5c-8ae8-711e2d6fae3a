create index idx_arrival_status on base_aircraft_safeguards_info(arrival_status);
create index idx_departure_status on base_aircraft_safeguards_info(departure_status);
create index idx_plan_landing_datetime on base_aircraft_safeguards_info(plan_landing_datetime);
create index idx_plan_take_off_datetime on base_aircraft_safeguards_info(plan_take_off_datetime);
create index idx_deleted on base_aircraft_safeguards_info(deleted);
create index idx_dict_type on sys_dictionary_data(dict_type);
create index idx_dict_value on sys_dictionary_data(dict_value);
create index idx_deleted on sys_dictionary_data(deleted);

create index idx_is_arrv on base_flight_info(is_arrv);

create index idx_deleted on base_flight_info(deleted);

create index idx_plan_landing_datetime on base_flight_info(plan_landing_datetime);

create index idx_plan_take_off_datetime on base_flight_info(plan_take_off_datetime);

create index idx_safeguard_type on base_aircraft_safeguards_info(safeguard_type);

create index idx_status on bill_bus_data(status);

create index idx_deleted on bill_bus_data(deleted);

create index idx_sign_type on bill_bus_data(sign_type);

create index idx_flight_date on bill_bus_data(flight_date);



create index idx_bill_data_id on bill_bus_data_item(bill_data_id);

create index idx_deleted on bill_bus_data_item(deleted);

create index idx_data_format on bill_bus_data_item(data_format);

create index idx_times on bill_bus_data_item(times);

create index idx_have_or_not on bill_bus_data_item(have_or_not);

create index idx_start_time on bill_bus_data_item(start_time);

create index idx_end_time on bill_bus_data_item(end_time);

create index idx_bill_data_id on bill_bus_data_item(bill_data_id);

create index idx_take_off_flight_id_basi on base_aircraft_safeguards_info(take_off_flight_id);

create index idx_arrive_flight_id_basi on base_aircraft_safeguards_info(arrive_flight_id);

create index idx_safeguard_type_basi on base_aircraft_safeguards_info(safeguard_type);

create index idx_flight_date_time_basi on base_aircraft_safeguards_info(flight_date_time);

create index idx_guarantee_start_time on bill_bus_data(guarantee_start_time);

create index idx_guarantee_end_time on bill_bus_data(guarantee_end_time);

create index idx_safeguards_type on bill_bus_data(safeguards_type);

create index idx_airline_code on t_aircraft_info(airline_code);

create index idx_invalid on t_aircraft_info(invalid);

create index idx_create_time on t_aircraft_info(create_time);

create index idx_airport_code on t_flight_info(airport_code);

create index idx_invalid on t_flight_info(invalid);

create index idx_invalid on t_service_record(invalid);

create index idx_flight_id on t_service_record(flight_id);

create index idx_flight_time on t_flight_info(flight_time);

create index idx_airline_code on t_flight_info(airline_code);

create index idx_flight_no on t_flight_info(flight_no);

create index idx_flight_flag on t_flight_info(flight_flag);

create index idx_data_status on t_flight_info(data_status);

create index idx_data_modified on t_flight_info(data_modified);

create index idx_task_flag on t_flight_info(task_flag);

create index idx_psg_number on t_flight_info(psg_number);

create index idx_variable_status on t_flight_info(variable_status);

create index idx_invalid_tfi on t_flight_info(invalid);

create index idx_airport_code_tfi on t_flight_info(airport_code);

create index idx_stay_start_time on t_flight_info(stay_start_time);

create index idx_stay_end_time on t_flight_info(stay_end_time);

create index idx_reg_no on t_flight_info(reg_no);

create index idx_flight_id on t_flight_bill(flight_id);

create index idx_fee_code on t_flight_bill(fee_code);

create index idx_airport_code on t_flight_bill(airport_code);

create index idx_settle_month on t_flight_bill(settle_month);

create index idx_invalid on t_flight_bill(invalid);

create index idx_fee_name on t_flight_bill(fee_name);

create index idx_airport_code on base_flight_info(airport_code);

create index idx_flight_no on base_flight_info(flight_no);

DROP INDEX idx_flight_date_and_id ON base_flight_traveler;

CREATE  INDEX  idx_flight_date_and_id
      ON base_flight_traveler (flight_date,flight_no,land_flag,confirm,id,base_flight_id) ;
CREATE  INDEX  idx_base_flight_id
      ON base_flight_traveler (base_flight_id) ;

CREATE  INDEX  idx_base_flight_id
      ON base_flight_traveler (base_flight_id) ;

create index idx_deleted on base_flight_cargo(deleted);

create index idx_flight_date on base_flight_cargo(flight_date);

-- 航班服务系统库
-- 租户添加数据采集相关 开关配置
-- pic
INSERT IGNORE INTO `sys_config_info` (`id`, `config_name`, `config_key`, `status`, `is_sys`, `is_single`, `remark`, `created_by`, `created_time`, `updated_by`, `updated_time`, `deleted`) VALUES (111, 'pic系统数据采集配置', 'pic_system_config', 1, '1', 0, 'pic系统采集配置', 'system', '2023-08-11 14:18:11', 'system', '2023-08-11 14:18:16', 0);
INSERT IGNORE INTO `sys_config_value` (`id`, `config_id`, `corp_code`, `value_key`, `config_value`, `status`, `created_by`, `created_time`, `updated_by`, `updated_time`, `deleted`, `remark`) VALUES (1772528039569702923, 111, 1503987820729229312, 'pic_pull_flight_data_enable', 'true', 1, 'system', '2024-05-07 22:22:36', 'system', '2024-05-07 22:22:41', 0, 'pic数据获取开关-ybp');
INSERT IGNORE INTO `sys_config_value` (`id`, `config_id`, `corp_code`, `value_key`, `config_value`, `status`, `created_by`, `created_time`, `updated_by`, `updated_time`, `deleted`, `remark`) VALUES (1772528039569702924, 111, 1503987820729229312, 'pic_protocol_ip_port', 'https://*************:8082', 1, 'system', '2024-05-07 23:56:03', 'system', '2024-05-07 23:56:03', 0, 'pic-pic服务地址-ybp');
INSERT IGNORE INTO `sys_config_value` (`id`, `config_id`, `corp_code`, `value_key`, `config_value`, `status`, `created_by`, `created_time`, `updated_by`, `updated_time`, `deleted`, `remark`) VALUES (1772528039569702925, 111, 1503987820729229312, 'pic_service_username', 'YBPdzt', 1, 'system', '2024-05-07 23:56:03', 'system', '2024-05-07 23:56:03', 0, 'pic-服务用户名-ybp');
INSERT IGNORE INTO `sys_config_value` (`id`, `config_id`, `corp_code`, `value_key`, `config_value`, `status`, `created_by`, `created_time`, `updated_by`, `updated_time`, `deleted`, `remark`) VALUES (1772528039569702926, 111, 1503987820729229312, 'pic_service_password', 'YBPdzt@2024', 1, 'system', '2024-05-07 23:56:03', 'system', '2024-05-07 23:56:03', 0, 'pic-服务密码-ybp');
INSERT IGNORE INTO `sys_config_value` (`id`, `config_id`, `corp_code`, `value_key`, `config_value`, `status`, `created_by`, `created_time`, `updated_by`, `updated_time`, `deleted`, `remark`) VALUES (1772528039569702931, 111, 1503987820729229312, 'pic_sync_collect_table_to_business_table_enable', 'true', 1, 'system', '2024-05-07 23:56:03', 'system', '2024-05-07 23:56:03', 0, '同步pic采集表的数据到业务表-ybp');
INSERT IGNORE INTO `sys_config_value` (`id`, `config_id`, `corp_code`, `value_key`, `config_value`, `status`, `created_by`, `created_time`, `updated_by`, `updated_time`, `deleted`, `remark`) VALUES (1772528039569702933, 111, 1503987820729229312, 'pic_push_base_flight_to_cargo_and_traveler_enable', 'true', 1, 'system', '2024-05-07 23:56:03', 'system', '2024-05-07 23:56:03', 0, '推送航班数据到客货邮行生成为0的数据 任务开关-ybp');


-- ume
INSERT IGNORE INTO `sys_config_info` (`id`, `config_name`, `config_key`, `status`, `is_sys`, `is_single`, `remark`, `created_by`, `created_time`, `updated_by`, `updated_time`, `deleted`) VALUES (112, 'ume系统数据采集配置', 'ume_system_config', 1, '1', 0, '	航旅纵横系统采集配置', 'system', '2023-08-11 14:18:11', 'system', '2023-08-11 14:18:16', 0);
INSERT IGNORE INTO `sys_config_value` (`id`, `config_id`, `corp_code`, `value_key`, `config_value`, `status`, `created_by`, `created_time`, `updated_by`, `updated_time`, `deleted`, `remark`) VALUES (1772528039569702935, 112, 1503987820729229312, 'ume_pull_flight_data_enable', 'true', 1, 'system', '2024-05-07 22:22:36', 'system', '2024-05-07 22:22:41', 0, 'ume拉取航班数据获取开关-ybp');
INSERT IGNORE INTO `sys_config_value` (`id`, `config_id`, `corp_code`, `value_key`, `config_value`, `status`, `created_by`, `created_time`, `updated_by`, `updated_time`, `deleted`, `remark`) VALUES (1772528039569702937, 112, 1503987820729229312, 'ume_sync_flt_data_collect_table_to_business_enable', 'true', 1, 'system', '2024-05-07 22:22:36', 'system', '2024-05-07 22:22:41', 0, 'ume同步航班数据从采集表到业务表开关-YBP');

-- 生成保障列表和推送数据到mq的开关
INSERT IGNORE INTO `sys_config_value` (`id`, `config_id`, `corp_code`, `value_key`, `config_value`, `status`, `created_by`, `created_time`, `updated_by`, `updated_time`, `deleted`, `remark`) VALUES (1772528039569702917, 1, 1503987820729229312, 'airport_code', 'YBP', 1, 'system', '2024-05-07 22:22:36', 'system', '2024-05-07 22:22:41', 0, '机场三字码-YBP');
INSERT IGNORE INTO `sys_config_value` (`id`, `config_id`, `corp_code`, `value_key`, `config_value`, `status`, `created_by`, `created_time`, `updated_by`, `updated_time`, `deleted`, `remark`) VALUES (1772528039569702919, 1, 1503987820729229312, 'generate_aircraft_safeguard_list_enable', 'true', 1, 'system', '2024-05-07 22:22:36', 'system', '2024-05-07 22:22:41', 0, '生成飞机保障列表开关-YBP');
INSERT IGNORE INTO `sys_config_value` (`id`, `config_id`, `corp_code`, `value_key`, `config_value`, `status`, `created_by`, `created_time`, `updated_by`, `updated_time`, `deleted`, `remark`) VALUES (1772528039569702921, 1, 1503987820729229312, 'push_flight_data_to_mq_enable', 'true', 1, 'system', '2024-05-07 22:22:36', 'system', '2024-05-07 22:22:41', 0, '推送航班数据mq开关-YBP');


DROP PROCEDURE IF EXISTS `add_index_proc`;
/* 添加索引 */
DELIMITER $$
CREATE PROCEDURE add_index_proc()
BEGIN
    select DATABASE() into @db_name;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_aircraft_safeguards_info' AND
        index_name='idx_basi_arrival_status')
    THEN
        CREATE INDEX idx_basi_arrival_status ON base_aircraft_safeguards_info (arrival_status);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_aircraft_safeguards_info' AND
        index_name='idx_basi_departure_status')
    THEN
        CREATE INDEX idx_basi_departure_status ON base_aircraft_safeguards_info (departure_status);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_aircraft_safeguards_info' AND
        index_name='idx_basi_take_off_flight_id')
    THEN
        CREATE INDEX idx_basi_take_off_flight_id ON base_aircraft_safeguards_info (take_off_flight_id);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_aircraft_safeguards_info' AND
        index_name='idx_basi_arrive_flight_id')
    THEN
        CREATE INDEX idx_basi_arrive_flight_id ON base_aircraft_safeguards_info (arrive_flight_id);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_aircraft_safeguards_info' AND
        index_name='idx_basi_safeguard_type')
    THEN
        CREATE INDEX idx_basi_safeguard_type ON base_aircraft_safeguards_info (safeguard_type);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_aircraft_safeguards_info' AND
        index_name='idx_basi_arrive_flight_id')
    THEN
        CREATE INDEX idx_basi_arrive_flight_id ON base_aircraft_safeguards_info (arrive_flight_id);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_aircraft_safeguards_info' AND
        index_name='idx_basi_aircraft_parking')
    THEN
        CREATE INDEX idx_basi_aircraft_parking ON base_aircraft_safeguards_info (aircraft_parking);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_aircraft_safeguards_info' AND
        index_name='idx_basi_plan_landing_datetime')
    THEN
        CREATE INDEX idx_basi_plan_landing_datetime ON base_aircraft_safeguards_info (plan_landing_datetime);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_aircraft_safeguards_info' AND
        index_name='idx_basi_plan_take_off_datetime')
    THEN
        CREATE INDEX idx_basi_plan_take_off_datetime ON base_aircraft_safeguards_info (plan_take_off_datetime);
    END IF;


    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_flight_cargo' AND
        index_name='idx_bfc_flight_date')
    THEN
        CREATE INDEX idx_bfc_flight_date ON base_flight_cargo (flight_date);
    END IF;

    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_flight_cargo' AND
        index_name='idx_bfc_flight_date')
    THEN
        CREATE INDEX idx_bfc_flight_date ON base_flight_cargo (flight_date);
    END IF;

    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_flight_info' AND
        index_name='idx_bfi_is_arrv')
    THEN
        CREATE INDEX idx_bfi_is_arrv ON base_flight_info (is_arrv);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_flight_info' AND
        index_name='idx_bfi_aircraft_no_real_landing_datetime_real_take_off_datetime')
    THEN
        CREATE INDEX idx_bfi_aircraft_no_real_landing_datetime_real_take_off_datetime ON base_flight_info (`aircraft_no`, `real_landing_datetime`, `real_take_off_datetime`);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_flight_info' AND
        index_name='idx_bfi_airline')
    THEN
        CREATE INDEX idx_bfi_airline ON base_flight_info (airline);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_flight_info' AND
        index_name='idx_bfi_flight_date_time')
    THEN
        CREATE INDEX idx_bfi_flight_date_time ON base_flight_info (flight_date_time);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
        table_name='base_flight_info' AND
        index_name='idx_bfi_task_deleted')
    THEN
        CREATE INDEX idx_bfi_task_deleted ON base_flight_info (task,deleted);
    END IF;
END $$
DELIMITER ;

CALL add_index_proc;
DROP PROCEDURE IF EXISTS `add_index_proc`;


CREATE TABLE IF NOT EXISTS `collect_flight_info_ume_bak` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `data_source_interface_code` char(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据来源的接口编码  COM:通用，ALT：备降;',
    `data_ownership_airport_code` char(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据归属机场三字码',
    `depart_or_arrive` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '进出港标识 -1：进港  1：出港',
    `flight_no` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航班号',
    `query_date` char(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '查询日期',
    `dept_flight_date` char(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '航班起飞日期',
    `dept_city_code` char(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '起飞机场三字码',
    `dest_city_code` char(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '到达机场三字码',
    `dest_flight_date` char(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班到达日期 ',
    `actual_dept_date` char(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '实际起飞日期',
    `actual_dest_date` char(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '实际到达时期',
    `estimate_dept_date` char(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '预计起飞日期',
    `estimate_dest_date` char(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '预计到达日期',
    `std` char(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '计划起飞时间（hh:mm）',
    `sta` char(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '计划到达时间（hh::mm）',
    `etd` char(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '预计起飞时间（hh:mm）',
    `eta` char(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '预计到达时间（hh:mm）',
    `atd` char(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '实际起飞时间（hh:mm）',
    `ata` char(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '实际到达时间（hh:mm）',
    `airline_code` char(3) DEFAULT NULL COMMENT '航空公司二字码',
    `plane_type` varchar(60) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '飞机型号',
    `plane_reg_no` varchar(60) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '飞机设备注册号',
    `fltmission` varchar(70) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班任务性质',
    `shared_nos` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '共享航班号',
    `air_rout` varchar(60) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线',
    `air_rout_code` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航线（三字码）',
    `is_civil` char(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '是否国内航班  true：是  false : 不是',
    `pre_flight_no` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '前序航班',
    `flight_status` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航班状态',
    `host_flight_no` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '承运方航班号',
    `alternate_airport` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备降机场',
    `created_time` timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '当前记录的创建时间',
    `last_operate_time` timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '当前记录的更新时间',
    `deleted` char(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '0' COMMENT '删除字段',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_query_date_interface_code` (`query_date`,`data_source_interface_code`) USING BTREE,
    KEY `idx_dept_flight_date` (`dept_flight_date`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3 COMMENT='（航旅纵横）航班数据';



DROP PROCEDURE IF EXISTS `add_index_proc`;
/* 添加索引 */
DELIMITER $$
CREATE PROCEDURE add_index_proc()
BEGIN
    select DATABASE() into @db_name;
    IF EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
            table_name='base_flight_info' AND
            index_name='idx_bfi_task_deleted')
    THEN
        DROP INDEX idx_bfi_task_deleted ON base_flight_info;
    END IF;


    IF EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
            table_name='collect_flight_info_ume' AND
            index_name='idx_dept_flight_date')
    THEN
        DROP INDEX  idx_dept_flight_date ON collect_flight_info_ume;
    END IF;



    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
            table_name='base_flight_info' AND
            index_name='idx_base_flight_info_task_deleted')
    THEN
        create index idx_base_flight_info_task_deleted on base_flight_info(task,deleted);
    END IF;
    IF NOT EXISTS(SELECT * FROM  information_schema.statistics WHERE table_schema=@db_name AND
            table_name='collect_flight_info_ume' AND
            index_name='idx_collect_flight_info_ume_dept_flight_date')
    THEN
        create index idx_collect_flight_info_ume_dept_flight_date on collect_flight_info_ume(dept_flight_date);
    END IF;




END $$
DELIMITER ;

CALL add_index_proc;
DROP PROCEDURE IF EXISTS `add_index_proc`;
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.mapper.BaseFlightCargoSegmentMapper">
    <sql id = "base_column_list">
        id,flight_date,flight_no,land_flag,flight_segment,base_flight_id,cargo,mail,bag,bag_num,transit_cargo,
        transit_mail,transit_bag,transit_bag_num,cmb_throughput,load_factor,cmb_total_kg,cargo_flag,mail_flag,
        bag_flag,bag_num_flag,transit_cargo_flag,transit_mail_flag,transit_bag_flag,transit_bag_num_flag,airport_code
    </sql>

    <select id="listByFlightId" resultType="com.swcares.aiot.core.vo.BaseFlightCargoSegmentVO">
        select
            <include refid="base_column_list"/>,
            (CASE WHEN updated_by is null
                    OR updated_by = 'null'
                    OR updated_by = 'DCS_AUTO_SYNC_PROG'
                THEN '自动采集'
                ELSE '手动添加' END )                                             AS dataSources,
            (CASE WHEN updated_by is null
                    OR updated_by = 'null'
                    OR updated_by = 'DCS_AUTO_SYNC_PROG'
                THEN 'PIC'
                ELSE updated_by END )                                            AS updatedBy
        from
            base_flight_cargo_segment
        <where>
            and deleted = 0 and base_flight_id=#{flightId}
        </where>
        order by flight_date desc,id desc
    </select>

    <select id="getDataToLastUpdateTime" resultType="com.swcares.aiot.core.entity.BaseFlightCargoSegment">
        SELECT *
        FROM base_flight_cargo_segment
        WHERE updated_time >= #{date}
    </select>

    <update id="deleteByBaseFlightID">
        update base_flight_cargo_segment
        set deleted ='1'
        where base_flight_id = #{flightId}
    </update>

    <update id="confirm">
        update base_flight_cargo_segment
        set confirm=1 where base_flight_id in (select DISTINCT base_flight_id
                                               from base_flight_cargo where id in
        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        )
          and (confirm = 0 or confirm = 2 or confirm is null)
    </update>

</mapper>

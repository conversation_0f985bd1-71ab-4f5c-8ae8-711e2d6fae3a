<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.mapper.AircraftInformationMapper">
    <insert id="bulkSave">
        insert into sys_aircraft_information
        (aircraft_num,aircraft_type,aircraft_type_name,airline,max_seat,optional_seat,max_industry_load,optional_industry_load,max_takeoff_weight,
        aircraft_property,wide_or_narrow,nation,domestic_and_abroad,effectiveness,effective_date,expiry_date,usage_unit,deleted,created_time,updated_time)
        values
        <if test="list.size() > 0">
            <foreach collection="list" separator="," item="e">
                (#{e.aircraftNum},#{e.aircraftType},#{e.aircraftTypeName},#{e.airline},#{e.maxSeat},#{e.optionalSeat},
                #{e.maxIndustryLoad},#{e.optionalIndustryLoad},#{e.maxTakeoffWeight},#{e.aircraftProperty},#{e.wideOrNarrow},
                #{e.nation},#{e.domesticAndAbroad},#{e.effectiveness},#{e.effectiveDate},#{e.expiryDate},#{e.usageUnit},
                0,NOW(),NOW())
            </foreach>
        </if>
    </insert>
    <update id="bulkUpdate">
        update sys_aircraft_information
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="wide_or_narrow=case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.wideOrNarrow != null and i.wideOrNarrow != ''">
                        when aircraft_num = #{i.aircraftNum} then #{i.wideOrNarrow}
                    </if>
                </foreach>
            </trim>
            <trim prefix="aircraft_property=case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.aircraftProperty != null and i.aircraftProperty !=''">
                        when aircraft_num = #{i.aircraftNum} then #{i.aircraftProperty}
                    </if>
                </foreach>
            </trim>
        </trim>
        where aircraft_num in
        <foreach collection="list" separator="," item="i" index="index" open="(" close=")">
            #{i.aircraftNum}
        </foreach>
    </update>
    <update id="updateByAircraftNum">
        update sys_aircraft_information set aircraft_property = #{a.aircraftProperty},wide_or_narrow = #{a.wideOrNarrow}
        where aircraft_num = #{a.aircraftNum}
          and effective_date = #{a.effectiveDate} and expiry_date = #{a.expiryDate}
    </update>

    <select id="page" resultType="com.swcares.aiot.core.vo.AircraftInformationVO">
        select * from sys_aircraft_information
        <where>
            and deleted = 0
            <if test="dto.aircraftNum != null and dto.aircraftNum != ''">
                and aircraft_num like concat('%',#{dto.aircraftNum},'%')
            </if>
            <if test="dto.airline != null and dto.airline != ''">
                and airline = #{dto.airline}
            </if>
            <if test="dto.aircraftType != null and dto.aircraftType != ''">
                and aircraft_type like concat('%',#{dto.aircraftType},'%')
            </if>
            <if test="dto.effectiveness != null">
                and effectiveness = #{dto.effectiveness}
            </if>
        </where>
        order by created_time desc
    </select>

    <select id="list" resultType="com.swcares.aiot.core.vo.AircraftInformationVO">
        select * from sys_aircraft_information
        <where>
            and deleted = 0
        </where>
    </select>
    <select id="selectNum" resultType="com.swcares.aiot.core.vo.AircraftInformationNumVO">
        select id,aircraft_num as aircraft_no from sys_aircraft_information where
            aircraft_num like concat('%',#{no},'%');
    </select>
    <select id="selectOptionalSeat" resultType="com.swcares.aiot.core.entity.AircraftInformation">
        select optional_seat,optional_industry_load from sys_aircraft_information where aircraft_num = #{no} and effectiveness = '1'
    </select>


</mapper>

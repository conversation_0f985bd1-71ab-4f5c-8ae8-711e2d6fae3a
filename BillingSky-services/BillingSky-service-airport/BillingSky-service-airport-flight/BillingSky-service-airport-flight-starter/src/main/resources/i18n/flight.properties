## 业务错误
910001=调用某服务错误请联系管理员
910002=航班状态异常
910003=同appid下版本号不能重复
910004=航班已存在
910005=请选择需要删除的数据
910006=文件导入失败，请重试
910007=查询结束日期不可超过查询其实日期
910008=允许查询日期最长间隔为31天
910009=请传入一条数据
910010=航班数据不存在
910011=请先上传机号文件
910012=航段格式不正确
910013=旅客数据不存在
910014=货邮数据不存在
910015=开始时间<结束时间
910016=开始时间大于当日时间+1
910017=同一机场生效时间不能重叠
910018=系统正在启用该数据源，删除失败
910019=航段不允许修改
910020=请输入推送时间
910021=该航班未获取到机号
910022=进出港机号不一致
910023=进出港航司不一致
910024=计划起飞时间应小于计划降落时间
910025=实际起飞时间应小于实际降落时间
910026=计划起飞时间与出港航班日期不一致
910027=计划到达时间与进港航班日期不一致
910028=修改保障状态不能和当前保障状态一致
910029=保障记录不存在
910030=修改失败
910031=imputation不能为空
911023=excel表格生成失败

## 机位分配
604002001=文件{0}下载失败
604002002=上传文件内容不正确
604002003=上传文件解析失败
604002004=导出{0}数据时出错
604002005=日期格式有误
604002006=数据重复，请核对数据后再添加
604002007=远程调用失败，请稍后重试
604002008=预算科目数据被使用，请核对数据后再操作
604100000=时间已过,无法修改
604100001=此记录不存在
604100003=参数错误，请重试
604100004=当前航班与左侧航班冲突
604100005=当前航班与右侧航班冲突
604100006=当前航班与左侧停用时间冲突
604100007=当前航班与右侧停用时间冲突
604100008=新增失败
604100009=删除失败
604100010=修改失败
604100011=查询失败
604100027=当前航班起飞降落时间包含了航班
604100028=当前航班起飞降落时间存在了航班
604100029=当前航班起飞降落时间包含了禁用时间
604100030=当前航班起飞降落时间被包含禁用时间
604100031=机位号与当前机位记录冲突
604100032=机位经纬度与当前机位记录冲突
604100033=机位纬度与当前记录冲突
604100034=当前机位已分配航班，无法编辑
604100035=当前机位已分配航班，无法删除
604100036=当前设置的停用时间\n{0}至{1}\n机位已分配航班,无法停用
604100037=该条停用计划已过当前时间，无法编辑
604100038=停用管理时间段不能与已有时间段有重合
604100039=停用开始时间不能等于或者大于停用结束时间
604100040=该条停用计划已过当前时间，无法新建
604100041=机位号不能修改
604100042=导出数据异常
604100043=航线中机场重复
605100001=数据库中PIC相关配置错误
605100002=调用PIC登陆接口错误
## 生产子系统数据导入模块
5102030=导入校验出现错误，请重新上传
5102001=文件io错误
5102002=文件模版错误，请按照模板格式导入
5102003=文件格式错误，系统支持格式：xlsx、xls
5102004=单次上传数据条数不超过{0}条，请分批导入
5102005=租户id未找到编码
5102006=文件无信息，请重新上传
5102007=航班数据导入确认失败
5102008=excel解析出错
5102009=缺少（导入限制最大数量）配置，请在配置中心配置
##航班数据相关错误
5101001=停场时间计算错误，没有查询到日期为:{0}的{1}的前序进港的航班
5101002=计算停场时间航班的机号为空，或是起降时间为空




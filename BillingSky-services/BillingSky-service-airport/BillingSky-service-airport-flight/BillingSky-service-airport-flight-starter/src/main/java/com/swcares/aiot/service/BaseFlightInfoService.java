package com.swcares.aiot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aiot.core.dto.BaseAircraftSafeguardsInfoDTO;
import com.swcares.aiot.core.dto.BaseFlightInfoByIsArrvDTO;
import com.swcares.aiot.core.dto.BaseFlightInfoDTO;
import com.swcares.aiot.core.dto.BaseFlightInfoManualPagedDTO;
import com.swcares.aiot.core.dto.DataSettingDTO;
import com.swcares.aiot.core.entity.BaseFlightInfo;
import com.swcares.aiot.core.vo.BaseFlightlistInfoVO;
import com.swcares.aiot.dto.BaseAircraftSafeguardsInfoQueryDTO;
import com.swcares.aiot.dto.BaseFlightDetalsDTO;
import com.swcares.aiot.vo.BaseAircraftSafeguardsInfoVO;
import com.swcares.aiot.vo.BaseFlightInfoVO;
import com.swcares.aiot.vo.FsBusAircraftSafeguardsInfoVO;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public interface BaseFlightInfoService extends IService<BaseFlightInfo> {

    List<BaseAircraftSafeguardsInfoVO> selectByNo(BaseAircraftSafeguardsInfoQueryDTO dto);


    /**
     * Title：listInfo <br>
     * Description：航班动态全部展示 <br>
     *
     * @param dto <br>
     * @return <br>
     * date 2022-03-29 <br>
     */
    List<BaseFlightlistInfoVO> listInfo(BaseFlightInfoDTO dto);

    /**
     * Title：listInfocount <br>
     * Description：航班动态获取总条数 <br>
     *
     * @param dto <br>
     * @return <br>
     * date 2022-04-07 <br>
     */
    Map<String, String> listInfocount(BaseFlightInfoDTO dto);

    /**
     * Title：selectDetails <br>
     * Description：详情查看 <br>
     *
     * @param id <br>
     * @return <br>
     * author ：朱程宇 <br>
     * date：2022-03-29 <br>
     */
    BaseFlightDetalsDTO selectDetails(Long id);


    /**
     * Title：selectFlightType <br>
     * Description：航班进出港、进港、出港、取消、异常、过夜查询<br>
     *
     * @param dto <br>
     * @return <br>
     * author 陈宇峰 <br>
     * date 2022-04-11 <br>
     */
    List<BaseAircraftSafeguardsInfoVO> baseFlightInfo(BaseAircraftSafeguardsInfoQueryDTO dto);

    /**
     * Title：createOvernight <br>
     * Description：添加过夜航班<br>
     * author ：陈宇峰 <br>
     * date：2022-04-11 <br>
     *
     * @param dto <br>
     */
    void createOvernight(BaseAircraftSafeguardsInfoDTO dto);

    /**
     * Title：typeCount <br>
     * Description： <br>
     * author ：陈宇峰 <br>
     * date：2022/4/11 10:43 <br>
     *
     * @param dto :
     * @return : BaseAircraftSafeguardsInfoVO
     */
    Map<String, Integer> typeCount(BaseAircraftSafeguardsInfoQueryDTO dto);

    /**
     * Title：deleteById <br>
     * Description：移除过夜航班信息 <br>
     * author ：陈宇峰 <br>
     * date：2022-03-29 <br>
     *
     * @param dto <br>
     */
    void deleteOvernight(BaseAircraftSafeguardsInfoDTO dto);

    /**
     * Title：page <br>
     * Description：分页 <br>
     * author ：陈宇峰 <br>
     * date：2022/4/18 10:43 <br>
     *
     * @param dto :
     * @return : BaseAircraftSafeguardsInfoVO
     */
    IPage<BaseAircraftSafeguardsInfoVO> page(BaseFlightInfoManualPagedDTO dto);

    IPage<FsBusAircraftSafeguardsInfoVO> page1(BaseFlightInfoManualPagedDTO dto);

    /**
     * Title: getBaseFlightInfoList <br>
     * Description: 根据航班日期及进出港查询航班日期 <br>
     * author wupengfei  <br>
     * date 2022/12/5 10:14<br>
     *
     * @param dto :
     * @return null
     */
    List<BaseFlightInfoVO> getBaseFlightInfoList(BaseFlightInfoByIsArrvDTO dto);

    /**
     * Title：getByFightNoAndFlightDate <br>
     * Description：根据航班号、航班日期、等条件查询航班数据 <br>
     * author：李军呈 <br>
     * date：2024年1月28日 下午1:37:10 <br>
     *
     * @param flightNo      航班号
     * @param flightDate    航班日期
     * @param airportCode   机场三字码
     * @param flightSegment 航段
     * @return com.swcares.aiot.core.entity.BaseFlightInfo 航班列表<br>
     */
    BaseFlightInfo getByFightNoAndFlightDate(String flightNo, LocalDate flightDate, String airportCode, String flightSegment);

    /**
     * Title：getByFightNoAndFlightDate <br>
     * Description：查询大于updateTime的航班数据 <br>
     * author：李军呈 <br>
     * date：2024年1月28日 下午1:37:10 <br>
     *
     * @param updatedTime  更新时间
     * @return 航班列表 <br>
     */
    List<BaseFlightInfo> getByGtUpdatedTime(LocalDateTime updatedTime);
    
    /**
     * Title：selectFlightInfoList <br>
     * Description: 查询航班列表 <br>
     *
     * @param flightDate 航班日期
     * @return java.util.List<com.swcares.aiot.core.entity.BaseFlightInfo>
     * author 周扬 <br>
     * date 2024/10/16 <br>
     */
    List<BaseFlightInfo> selectFlightInfoListByFlightDate(LocalDate flightDate);
    /**
     * Title：deleteFlightInfoByTaskCodeList <br>
     * Description: 删除生效日期 之后 需要过滤的航班 <br>
     * author 周扬 <br>
     * date 2024/5/29 <br>
     *
     * @param dataSettingDto  ui设置的过滤条件<br>
     *  <br>
     */
    void deleteFlightInfoByTaskCodeList(DataSettingDTO dataSettingDto);

    /**
     * Title：cleaningFlightData <br>
     * Description: 清理 航班数据 <br>
     *
     * @param minDate 开始航班日期
     * @param maxDate 结束航班日期
     * <p>
     * author 周扬 <br>
     * date 2024/10/17 <br>
     */
    void cleaningFlightData(LocalDate minDate, LocalDate maxDate);

    /**
     * Title：disposeAcdmDeleteRetainFlightList <br>
     * Description: 处理 acdm删除， acdm删除自动保留的航班数据 <br>
     * 
     * @param acdmDeleteRetainFlightList 删除航班列表
     * </br>
     * author 周扬 <br>
     * date 2024/10/20 <br>
     */
    void disposeAcdmDeleteRetainFlightList(List<BaseFlightInfo> acdmDeleteRetainFlightList);

    /**
     * Title：getBaseFlightInfoList <br>
     * Description: 获取航班列表 <br>
     *
     * @param flightDateList 日期列表
     * @return java.util.List<com.swcares.aiot.core.entity.BaseFlightInfo>
     * author 周扬 <br>
     * date 2024/11/12 <br>
     */
    List<BaseFlightInfo> getBaseFlightInfoList(List<LocalDate> flightDateList);
}

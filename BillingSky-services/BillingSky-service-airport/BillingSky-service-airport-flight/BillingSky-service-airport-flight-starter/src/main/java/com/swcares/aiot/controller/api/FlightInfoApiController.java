package com.swcares.aiot.controller.api;

import com.swcares.aiot.client.IFlightInfoClient;
import com.swcares.aiot.core.common.cons.AppConstants;
import com.swcares.aiot.dto.FlightInfoApiPageDto;
import com.swcares.aiot.service.FlightManagerService;
import com.swcares.aiot.vo.FlightInfoVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：FlightInfoApiController
 * Description：航班信息外部调用 Controller
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2024/11/15 15:35
 * Version v1.0
 */
@RestController
@RequestMapping("/api/flightInfo")
@Api(tags = "航班信息外部调用API")
@ApiVersion(AppConstants.SWAGGER_API_VERSION_INFO)
public class FlightInfoApiController extends BaseController implements IFlightInfoClient {
    @Resource
    private FlightManagerService flightManagerService;

    @PostMapping("/flightPage")
    @ApiOperation(value = "航班分页接口(包含客货邮行信息)")
    @Override
    public PagedResult<List<FlightInfoVO>> flightPage(@RequestBody @Validated FlightInfoApiPageDto dto) {
        return ok(flightManagerService.flightPage(dto));
    }
}

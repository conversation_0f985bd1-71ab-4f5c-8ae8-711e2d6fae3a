package com.swcares.aiot.flight.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.swcares.aiot.core.common.cons.FlightCons;
import com.swcares.aiot.core.entity.BaseConfigInfo;
import com.swcares.aiot.core.entity.BaseFlightInfoExt;
import com.swcares.aiot.core.enums.EnumFlightArrvied;
import com.swcares.aiot.core.service.IBaseFlightInfoExtService;
import com.swcares.aiot.flight.service.FlightDataTaskBizService;
import com.swcares.aiot.mapper.BaseFlightInfoMapper;
import com.swcares.aiot.service.BaseConfigInfoService;
import com.swcares.aiot.vo.FlightInfoVO;
import com.swcares.baseframe.common.core.tenant.TenantDataSourceParam;
import com.swcares.baseframe.common.core.tenant.TenantDataSourceParamFetcher;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.lang.DateUtils;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * ClassName：FlightDataTaskBizServiceImpl
 * Description：航班相关定时任务 实现类
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2024/12/18 16:28
 * Version v1.0
 */
@Service
@Slf4j
public class FlightDataTaskBizServiceImpl implements FlightDataTaskBizService {

    @Resource
    private TenantDataSourceParamFetcher tenantDataSourceParamFetcher;

    @Resource
    private FlightDataTaskBizService flightDataTaskBizService;

    @Resource
    private BaseConfigInfoService baseConfigInfoService;

    @Resource
    private BaseFlightInfoMapper baseFlightInfoMapper;

    @Resource
    private IBaseFlightInfoExtService baseFlightInfoExtService;

    @Override
    public void calculateDownTimeTask(String flightDate) {
        log.info("开始计算停场时间，dateTime= {} ", getNowLocalDateTime());
        // 数据源中的连接信息
        List<TenantDataSourceParam> allTenantDataSourceParams = tenantDataSourceParamFetcher.getAllTenantDataSourceParams();
        Set<Long> ucTenantIdList = allTenantDataSourceParams.stream().map(TenantDataSourceParam::getTenantId).collect(Collectors.toSet());
        log.info(FlightCons.MSG_TEMPLATE, ucTenantIdList);

        for (Long ucTenantId : ucTenantIdList) {
            try {
                // 设置租户id，使获取到的数据入库到指定租户库
                TenantHolder.setTenant(ucTenantId);
                log.info("计算停场时间，dateTime= {} , 租户id={}", getNowLocalDateTime(), ucTenantId);
                flightDataTaskBizService.calculateDownTime(flightDate);
            } catch (Exception e) {
                log.error("租户(tenantId = {}), 推送航班数据到客货邮行生成为0的数据出现异常，请检查相关日志", ucTenantId, e);
            } finally {
                TenantHolder.clear();
            }
        }
        log.info("完成计算停场时间，dateTime= {} ", getNowLocalDateTime());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void calculateDownTime(String flightDate) {
        try {
            log.info("计算停场时间开始啦，dateTime= {} ", getNowLocalDateTime());
            BaseConfigInfo baseConfigInfo = baseConfigInfoService.getByKey(FlightCons.CALCULATE_DOWN_TIME_CONFIG);
            if (ObjectUtils.isNotEmpty(baseConfigInfo)) {
                LocalDateTime startTimes = DateUtils.dateToLdt(DateUtils.parseDate(baseConfigInfo.getValue()));
                //获取本次处理数据的 “结束时间”
                LocalDateTime waitDisposeEndTime = baseFlightInfoMapper.getLastUpdateTime();
                String waitDisposeEndTimeStr = LocalDateTimeUtil.format(waitDisposeEndTime, DatePattern.NORM_DATETIME_FORMATTER);
                baseConfigInfo.setValue(waitDisposeEndTimeStr);
                baseConfigInfo.setUpdatedTime(LocalDateTime.now());

                if(StringUtils.isNotBlank(flightDate)){
                    startTimes = DateUtil.parseLocalDateTime(flightDate);
                }
                // 计算停场时间-实现
                calculateDownTimeImpl(startTimes);

                log.info("更新处理时间，dateTime= {} , waitDisposeEndTimeStr ={}", getNowLocalDateTime(), waitDisposeEndTimeStr);
                baseConfigInfoService.updateByKey(FlightCons.CALCULATE_DOWN_TIME_CONFIG, waitDisposeEndTimeStr);
            }
            log.info("计算停场时间结束啦，dateTime= {} ", getNowLocalDateTime());
        } catch (Exception e) {
            log.error("计算停场时间失败！异常信息:", e);
        }
    }

    private String getNowLocalDateTime(){
        return DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_MS_PATTERN);
    }

    private void calculateDownTimeImpl(LocalDateTime startTime) {
        List<FlightInfoVO> flightInfoList = baseFlightInfoMapper.getByGtUpdatedTime(startTime);
        if(CollUtil.isEmpty(flightInfoList)){
            log.info("没找到需要计算停场时间的航班，dateTime= {}", getNowLocalDateTime());
            return;
        }
        log.info("查询到需要计算停场时间的航班数量，dateTime= {} , size ={}", getNowLocalDateTime(), flightInfoList.size());
        for(FlightInfoVO flightInfo : flightInfoList){
            calculateDownTimes(flightInfo);
        }
    }

    /**
     * Title：calculateDownTimes
     * Description：计算停场时间-计算方法
     * author：李军呈
     * date： 2024/12/19 13:25
     * @param info 航班数据
     */
    private void calculateDownTimes(FlightInfoVO info) {
        log.info("计算停场时间的正式开始，dateTime= {} ", getNowLocalDateTime());
        List<FlightInfoVO> recalculateDownTimeList = new ArrayList<>();
        boolean changeFlag = false;
        try {
            if (Objects.nonNull(info.getAircraftNo())
                    && Objects.nonNull(info.getIsArrv())
                    && Objects.nonNull(getFlightTime(info))) {
                LocalDateTime flightTime = getFlightTime(info);
                // 获取该机号，在该起降时间前序/后序的十条航班数据
                // 以下三类不参与停场时间的计算 1、航班状态为：取消 2、状态为删除（因重复删除、航旅纵横删除、延误删除）3、人工删除
                List<FlightInfoVO> flightInfoList = EnumFlightArrvied.OUT_POST.getValue().equals(info.getIsArrv())
                        ? baseFlightInfoMapper.getPreorderFlight(info.getAircraftNo(), flightTime, info.getAirportCode(), info.getId(),
                                    info.getFlightDate().minusDays(30), info.getFlightDate())
                        : baseFlightInfoMapper.getPrologueFlight(info.getAircraftNo(), flightTime, info.getAirportCode(), info.getId(),
                                    info.getFlightDate(), info.getFlightDate().plusDays(30));
                for (FlightInfoVO flightInfo : flightInfoList) {
                    // 跳过空值
                    if (flightInfo.getFlightTime() != null) {
                        if (!info.getIsArrv().equals(flightInfo.getIsArrv())) {
                            log.info("停场时间计算，航班分别是id{}-{}，航班号{}-{}, 航班日期{}-{}",
                                    info.getId(), flightInfo.getId(), info.getFlightNo(), flightInfo.getFlightNo(), info.getFlightDate(), flightInfo.getFlightDate());
                            BaseFlightInfoExt infoExtend = flightDataTaskBizService.getBaseFlightInfoExtByBaseFlightId(info.getId());
                            BaseFlightInfoExt flightInfoExtend = flightDataTaskBizService.getBaseFlightInfoExtByBaseFlightId(flightInfo.getId());
                            // 将现关联起的两个航班之前的关联航班停场时间置空,如果他们俩之前就是关联航班则不用置空
                            // 如果成功置空，则将置空航班放到再次计算停场时间list中
                            if (Objects.nonNull(infoExtend) && Objects.nonNull(flightInfoExtend)
                                && (!Objects.equals(infoExtend.getStayStartTime(), flightInfoExtend.getStayStartTime())
                                    || !Objects.equals(infoExtend.getStayEndTime(), flightInfoExtend.getStayEndTime()))) {
                                log.info("停场时间计算，进到停场时间置空逻辑里面来了");
                                // 将新关联的前序/后序航班之前关联的航班停场时间置空
                                FlightInfoVO tempInfo = updateOldDownTime(flightInfo);
                                if (tempInfo != null) {
                                    recalculateDownTimeList.add(tempInfo);
                                }
                                // 将传入航班之前的前序起飞/后续降落航班停场时间置空；
                                FlightInfoVO tempInfo2 = updateOldDownTime(info);
                                if (tempInfo2 != null) {
                                    recalculateDownTimeList.add(tempInfo2);
                                }
                            }
                            // 计算停场时间
                            BaseFlightInfoExt infoExt = new BaseFlightInfoExt()
                                    .setBaseFlightId(info.getId())
                                    .setStayTime(hourDifference(info.getFlightTime(), flightInfo.getFlightTime()))
                                    .setStayStartTime(earlyTime(flightInfo.getFlightTime(), info.getFlightTime()))
                                    .setStayEndTime(laterTime(flightInfo.getFlightTime(), info.getFlightTime()))
                                    .setDeleted(Boolean.FALSE);
                            BaseFlightInfoExt flightInfoExt = new BaseFlightInfoExt()
                                    .setBaseFlightId(flightInfo.getId())
                                    .setStayTime(hourDifference(info.getFlightTime(), flightInfo.getFlightTime()))
                                    .setStayStartTime(earlyTime(flightInfo.getFlightTime(), info.getFlightTime()))
                                    .setStayEndTime(laterTime(flightInfo.getFlightTime(), info.getFlightTime()))
                                    .setDeleted(Boolean.FALSE);

                            log.info("比较两个BigDecimal,定义12 小时的BigDecimal");
                            if (infoExt.getStayTime().compareTo(new BigDecimal("12.00")) > 0) {
                                log.warn("停场时间大于12个小时，对比航班id{}-{}，航班号{}-{}, 航班日期{}-{}",
                                        info.getId(), flightInfo.getId(), info.getFlightNo(), flightInfo.getFlightNo(), info.getFlightDate(), flightInfo.getFlightDate());
                            }

                            // 设置当前起飞航班的前序降落航班的停场时间
                            // 更新降落航班的停场时间
                            saveOrUpdateBaseFlightInfoExt(infoExt);
                            saveOrUpdateBaseFlightInfoExt(flightInfoExt);
                            changeFlag = true;
                            break;
                        } else {
                            log.info("没有查询到日期为:{}的{}的前序进港的航班,id:{}", info.getFlightDate(), info.getFlightNo(), info.getId());
                            break;
                        }
                    }
                }
            } else {
                log.info("计算停场时间航班的机号为空，或是起降时间为空！！！");
            }
        } catch (Exception e) {
            log.error("重新结算停场时间 失败", e);
        } finally {
            // 如果传入航班没匹配到关联的前序或后序航班，则将本航班停场时间置为空
            // 并将本航班之前关联的前序/后续航班置空
            if (!changeFlag) {
                // 将传入航班之前的前序起飞/后续降落航班停场时间置空；
                FlightInfoVO tempInfo2 = updateOldDownTime(info);
                if (tempInfo2 != null) {
                    recalculateDownTimeList.add(tempInfo2);
                }
                baseFlightInfoExtService.lambdaUpdate()
                        .set(BaseFlightInfoExt::getStayTime, new BigDecimal(0))
                        .set(BaseFlightInfoExt::getStayStartTime, null)
                        .set(BaseFlightInfoExt::getStayEndTime, null)
                        .eq(BaseFlightInfoExt::getBaseFlightId, info.getId())
                        .update();
            }
            // 如果再次计算停场时间list不为空，则将list中的flightinfo重新计算停场时间
            for (FlightInfoVO reCalculateInfo : recalculateDownTimeList) {
                // 此处出现java.lang.StackOverflowError，不执行 calculateDownTimes(reCalculateInfo); 错误
                log.info("不会再次计算停场时间避免StackOverflowError，id:{}-{}", reCalculateInfo.getId(), reCalculateInfo.getFlightNo());
            }
        }
        log.info("计算停场时间的正式结束，dateTime= {} ", getNowLocalDateTime());
    }

    /**
     * Title：getFlightTime
     * Description：获取起降时间
     * author：李军呈
     * date： 2024/12/19 13:26
     * @param flightInfo 航班数据
     * @return java.time.LocalDateTime
     */
    private LocalDateTime getFlightTime(FlightInfoVO flightInfo){
        LocalDateTime bFlightTime = null;
        // 解析起降时间
        // 如果为降落航班
        if (EnumFlightArrvied.IS_ARRV.getValue().equals(flightInfo.getIsArrv())) {
            if (flightInfo.getRealLandingDatetime() != null) {
                bFlightTime = flightInfo.getRealLandingDatetime();
            } else if (flightInfo.getPredictLandingDatetime() != null) {
                bFlightTime = flightInfo.getPredictLandingDatetime();
            } else if (flightInfo.getPlanLandingDatetime() != null) {
                bFlightTime = flightInfo.getPlanLandingDatetime();
            }
        } else {
            if (flightInfo.getRealTakeOffDatetime() != null) {
                bFlightTime = flightInfo.getRealTakeOffDatetime();
            } else if (flightInfo.getPredictTakeOffDatetime() != null) {
                bFlightTime = flightInfo.getPredictTakeOffDatetime();
            } else if (flightInfo.getPlanTakeOffDatetime() != null) {
                bFlightTime = flightInfo.getPlanTakeOffDatetime();
            }
        }
        if (bFlightTime != null) {
            return bFlightTime;
        }
        log.info("起降时间解析失败，dateTime= {}", getNowLocalDateTime());
        return null;
    }

    /**
     * Title：getBaseFlightInfoExtByBaseFlightId
     * Description：查询保存停场时间
     * author：李军呈
     * date： 2024/12/25 13:47
     * @param baseFlightId 航班id
     */
    @Override
    public BaseFlightInfoExt getBaseFlightInfoExtByBaseFlightId(Long baseFlightId){
        log.info("连接数据库查询{}", baseFlightId);
        return baseFlightInfoExtService.lambdaQuery()
                .eq(BaseFlightInfoExt::getBaseFlightId, baseFlightId)
                .eq(BaseFlightInfoExt::getDeleted, Boolean.FALSE)
                .one();
    }

    /**
     * Title: hourDifference<br>
     * Author: 刘志恒<br>
     * Description: 计算两个时间之间相差的小时<br>
     * Date:  2021/6/21 15:10 <br>
     *
     * @param d1 :
     * @param d2 :
     */
    private BigDecimal hourDifference(LocalDateTime d1, LocalDateTime d2) {
        LocalDateTime earlierTime = d1.isBefore(d2)? d1 : d2;
        LocalDateTime laterTime = d1.isBefore(d2)? d2 : d1;
        // 计算两个LocalDateTime之间的时间间隔，确保用较晚时间减去较早时间
        Duration duration = Duration.between(earlierTime, laterTime);
        BigDecimal seconds = new BigDecimal(duration.getSeconds());
        return seconds.divide(new BigDecimal(3600), 2, RoundingMode.DOWN);
    }

    /**
     * Title: earlyTime<br>
     * Author: 刘志恒<br>
     * Description: 比较两个时间，返回两个时间中更早的那个
     *
     * @param d1 时间
     * @param d2 时间
     */
    private LocalDateTime earlyTime(LocalDateTime d1, LocalDateTime d2) {
        if (d1.isBefore(d2)) {
            return d1;
        } else {
            return d2;
        }
    }

    /**
     * Title: laterTime<br>
     * Author: 刘志恒<br>
     * Description: 比较两个时间，返回两个时间中更晚的那个
     *
     * @param d1 时间
     * @param d2 时间
     */
    private LocalDateTime laterTime(LocalDateTime d1, LocalDateTime d2) {
        if (d1.isAfter(d2)) {
            return d1;
        } else {
            return d2;
        }
    }

    private void saveOrUpdateBaseFlightInfoExt(BaseFlightInfoExt baseFlightInfoExt){
        BaseFlightInfoExt ext = flightDataTaskBizService.getBaseFlightInfoExtByBaseFlightId(baseFlightInfoExt.getBaseFlightId());
        if(Objects.nonNull(ext)){
            baseFlightInfoExtService.lambdaUpdate()
                    .set(BaseFlightInfoExt::getStayTime, baseFlightInfoExt.getStayTime())
                    .set(BaseFlightInfoExt::getStayStartTime, baseFlightInfoExt.getStayStartTime())
                    .set(BaseFlightInfoExt::getStayEndTime, baseFlightInfoExt.getStayEndTime())
                    .set(BaseFlightInfoExt::getUpdatedTime, LocalDateTime.now())
                    .eq(BaseFlightInfoExt::getBaseFlightId, baseFlightInfoExt.getBaseFlightId())
                    .update();
        } else {
            baseFlightInfoExtService.save(baseFlightInfoExt);
        }
    }

    /**
     * Title: updateOldDownTime<br>
     * Author: 刘志恒<br>
     * Description: 重新计算停场时间时，将之前关联航班的停场时间清零，并返回之前关联航班<br>
     *
     * @param info 置空此航班的关联航班的停场时间
     *             Date:  2021/11/4 15:47 <br>
     */
    private FlightInfoVO updateOldDownTime(FlightInfoVO info) {
        BaseFlightInfoExt infoExtend = flightDataTaskBizService.getBaseFlightInfoExtByBaseFlightId(info.getId());
        if(Objects.isNull(infoExtend)){
            return null;
        }
        // 如果当前航班的
        if (infoExtend.getStayStartTime() != null && infoExtend.getStayEndTime() != null
                && infoExtend.getStayTime() != null) {
            List<BaseFlightInfoExt> extList = baseFlightInfoExtService.lambdaQuery()
                    .eq(BaseFlightInfoExt::getStayStartTime, infoExtend.getStayStartTime())
                    .eq(BaseFlightInfoExt::getStayEndTime, infoExtend.getStayEndTime())
                    .ne(BaseFlightInfoExt::getId, infoExtend.getId()).list();
            if(CollUtil.isNotEmpty(extList)){
                List<Long> baseFlightIdList = extList.stream().map(BaseFlightInfoExt::getBaseFlightId).collect(Collectors.toList());
                // 根据传入航班获取其关联的起飞/降落航班
                FlightInfoVO flightInfo = baseFlightInfoMapper.getFlightInfoByStayTimeAndRegNo(
                        info.getAircraftNo(), info.getAirportCode(), baseFlightIdList);
                if (flightInfo != null) {
                    baseFlightInfoExtService.lambdaUpdate()
                            .set(BaseFlightInfoExt::getStayTime, new BigDecimal(0))
                            .set(BaseFlightInfoExt::getStayStartTime, null)
                            .set(BaseFlightInfoExt::getStayEndTime, null)
                            .eq(BaseFlightInfoExt::getBaseFlightId, info.getId())
                            .update();
                    return flightInfo;
                }
            }
        }
        return null;
    }


}

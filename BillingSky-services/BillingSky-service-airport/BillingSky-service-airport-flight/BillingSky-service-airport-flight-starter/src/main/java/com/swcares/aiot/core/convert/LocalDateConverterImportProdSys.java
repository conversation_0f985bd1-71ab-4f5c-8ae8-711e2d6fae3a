package com.swcares.aiot.core.convert;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.swcares.aiot.cons.BusinessExceptionCodeConstants;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title ：LocalDateConverter <br>
 * Package ：com.swcares.aiot.ass.business.bill.excel.convert <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 * Description : 自定义LocalDateStringConverter,用于解决使用easyexcel导出表格时候，默认不支持LocalDateTime日期格式<br>
 * <p>
 * 在需要的属性上添加注解 @ExcelProperty(value = "xx时间", converter = LocalDateStringConverter.class)
 *
 * <AUTHOR> <br>
 * date 2024年 01月18日 16:44 <br>
 * @version v1.0 <br>
 */
@Slf4j
public class LocalDateConverterImportProdSys implements Converter<LocalDate> {
    @Override
    public Class<LocalDate> supportJavaTypeKey() {
        return LocalDate.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public LocalDate convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty,
                                       GlobalConfiguration globalConfiguration) {
        if (cellData.getType().equals(CellDataTypeEnum.STRING)) {
            String cellValue = cellData.getStringValue();
            if (StringUtils.isBlank(cellValue)) {
                return null;
            }
            // 处理 MMDDYY格式
            LocalDate localDate = disposeMMDDYY(cellValue);
            if (localDate != null) {
                return localDate;
            }
            Date date = DateUtils.parseDate(cellValue);
            if (Objects.isNull(date)) {
                log.info("日期转化失败"+ cellValue);
                return null;
            }
            return DateUtils.dateToLd(date);
        } else {
            log.error("日期转化失败"+ cellData.getStringValue());
            throw new BusinessException(BusinessExceptionCodeConstants.TIME_PASS_ERROR);
        }
    }

    public static @Nullable LocalDate disposeMMDDYY(String cellValue) {
        if (cellValue.length() == 8) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yy");
            return LocalDate.parse(cellValue, formatter);
        }
        return null;
    }

    @Override
    public CellData<String> convertToExcelData(LocalDate localDate, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) {
        DateTimeFormatter formatter = getFormatter(excelContentProperty);
        return new CellData<>(localDate.format(formatter));
    }

    /**
     * Title : getFormatter <br>
     * Description : 根据配置注解生成formatter <br>
     *
     * @param excelContentProperty :
     * @return java.time.format.DateTimeFormatter
     *
     * <AUTHOR>  <br>
     * date 2024/1/19 14:34<br>
     */
    private DateTimeFormatter getFormatter(ExcelContentProperty excelContentProperty) {
        String format = null;
        if (excelContentProperty.getDateTimeFormatProperty() != null) {
            format = excelContentProperty.getDateTimeFormatProperty().getFormat();
        }
        if (StringUtils.isBlank(format)) {
            return DatePattern.NORM_DATE_FORMATTER;
        }
        //避免newFormatter
        if (StringUtils.equals(format, DatePattern.NORM_MONTH_PATTERN)) {
            return DatePattern.NORM_MONTH_FORMATTER;
        }
        if (StringUtils.equals(format, DatePattern.NORM_DATE_PATTERN)) {
            return DatePattern.NORM_DATE_FORMATTER;
        }
        return DateTimeFormatter.ofPattern(format);
    }
}
package com.swcares.aiot.core.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BaseAircraftSafeguardsInfoPagedDTO", description="飞机航班保障信息表")
public class BaseAircraftSafeguardsInfoPagedDTO extends PagedDTO{

        private static final long serialVersionUID = 1L;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startTime;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endTime;
}
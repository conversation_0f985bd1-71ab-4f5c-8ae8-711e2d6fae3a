package com.swcares.aiot.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
/**
 * ClassName：com.swcares.base.flight.api.vo.BaseFlightCargoVO <br>
 * Description：航班货邮行数据表返回展示对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-07-19 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="BaseFlightCargoVO", description="航班货邮行数据表")
public class BaseFlightCargoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "航班日期")
    private LocalDateTime flightDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "进出港标识;1：进港 0：出港")
    private String landFlag;

    @ApiModelProperty(value = "航段")
    private String flightSegment;

    @ApiModelProperty(value = "航班id")
    private Long baseFlightId;

    @ApiModelProperty(value = "本站货物重量")
    private Integer cargo;

    @ApiModelProperty(value = "本站邮件重量")
    private Integer mail;

    @ApiModelProperty(value = "本站行李重量")
    private Integer bag;

    @ApiModelProperty(value = "本站行李件数")
    private Integer bagNum;

    @ApiModelProperty(value = "过站货物重量")
    private Integer transitCargo;

    @ApiModelProperty(value = "过站邮件重量")
    private Integer transitMail;

    @ApiModelProperty(value = "过站行李重量")
    private Integer transitBag;

    @ApiModelProperty(value = "过站行李件数")
    private Integer transitBagNum;

    @ApiModelProperty(value = "货邮吞吐量")
    private Integer cmbThroughput;

    @ApiModelProperty(value = "载运率")
    private BigDecimal loadFactor;

    @ApiModelProperty(value = "货邮行总重量")
    private Integer cmbTotalKg;

    @ApiModelProperty(value = "本站货物重量指令采集数据标识(1:一致,0:不一致)")
    private Boolean cargoFlag;

    @ApiModelProperty(value = "本站邮件重量指令采集数据标识(1:一致,0:不一致)")
    private Boolean mailFlag;

    @ApiModelProperty(value = "本站行李重量指令采集数据标识(1:一致,0:不一致)")
    private Boolean bagFlag;

    @ApiModelProperty(value = "本站行李件数指令采集数据标识(1:一致,0:不一致)")
    private Boolean bagNumFlag;

    @ApiModelProperty(value = "过站货物重量指令采集数据标识(1:一致,0:不一致)")
    private Boolean transitCargoFlag;

    @ApiModelProperty(value = "过站邮件重量指令采集数据标识(1:一致,0:不一致)")
    private Boolean transitMailFlag;

    @ApiModelProperty(value = "过站行李重量指令采集数据标识(1:一致,0:不一致)")
    private Boolean transitBagFlag;

    @ApiModelProperty(value = "过站行李件数指令采集数据标识(1:一致,0:不一致)")
    private Boolean transitBagNumFlag;

    @ApiModelProperty(value = "机场三字码")
    private String airportCode;

    @ApiModelProperty(value = "是否拆分子数据（1为是，0为否）")
    private Integer isSplit;

    @ApiModelProperty(value = "是否确认（0：待确认，1：已确认，2：已修改）")
    private Integer confirm;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "数据来源")
    private String dataSources;

}

package com.swcares.aiot.flight.service;

import com.swcares.aiot.core.entity.BaseFlightInfoExt;

/**
 * ClassName：FlightDataTaskBizService
 * Description：航班相关定时任务接口
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2024/12/18 16:27
 * Version v1.0
 */
public interface FlightDataTaskBizService {

    /**
     * Title：calculateDownTime
     * Description：计算停场时间任务
     * author：李军呈
     * date： 2024/12/18 16:27
     * @param flightDate 计算日期
     */
    void calculateDownTimeTask(String flightDate);

    /**
     * Title：calculateDownTime
     * Description：计算停场时间
     * author：李军呈
     * date： 2024/12/18 16:44
     * @param flightDate 计算日期
     */
    void calculateDownTime(String flightDate);

    BaseFlightInfoExt getBaseFlightInfoExtByBaseFlightId(Long baseFlightId);

}

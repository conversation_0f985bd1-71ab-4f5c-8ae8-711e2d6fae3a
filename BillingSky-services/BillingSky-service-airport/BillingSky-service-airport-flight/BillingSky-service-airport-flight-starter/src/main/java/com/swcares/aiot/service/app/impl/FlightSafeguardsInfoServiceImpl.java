package com.swcares.aiot.service.app.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aiot.cons.FlightBusinessExceptionCodeConstant;
import com.swcares.aiot.core.common.StatusValue;
import com.swcares.aiot.core.common.config.RabbitMqConfiguration;
import com.swcares.aiot.core.dto.FlightSafeguardsInfoChangeStatusDTO;
import com.swcares.aiot.core.entity.BaseAircraftSafeguardsInfo;
import com.swcares.aiot.core.entity.BaseFlightInfo;
import com.swcares.aiot.core.enums.EnumDepartureArriveStatus;
import com.swcares.aiot.core.enums.EnumFlightStatusType;
import com.swcares.aiot.core.enums.EnumSafeguardType;
import com.swcares.aiot.core.enums.EnumSafeguardsStatusName;
import com.swcares.aiot.dto.FlightSafeguardsInfoQueryDTO;
import com.swcares.aiot.dto.MqAircraftSafeguardsInfoDTO;
import com.swcares.aiot.dto.MqDetailDto;
import com.swcares.aiot.dto.MqEventDto;
import com.swcares.aiot.enums.DeletedEnum;
import com.swcares.aiot.enums.FlightEventEnum;
import com.swcares.aiot.mapper.BaseAircraftSafeguardsInfoMapper;
import com.swcares.aiot.mapper.BaseFlightInfoMapper;
import com.swcares.aiot.service.app.FlightSafeguardsInfoService;
import com.swcares.aiot.utils.QueryWrapperUtil;
import com.swcares.aiot.vo.BaseFlightInfoVO;
import com.swcares.aiot.vo.FlightSafeguardsInfoVO;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * ClassName：com.swcares.aiot.node.safeguards.service.impl.FsBusAircraftSafeguardsInfoServiceImpl <br>
 * Description：飞机实体 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class FlightSafeguardsInfoServiceImpl extends ServiceImpl<BaseAircraftSafeguardsInfoMapper, BaseAircraftSafeguardsInfo> implements FlightSafeguardsInfoService {


    @Resource
    private BaseAircraftSafeguardsInfoMapper baseAircraftSafeguardsInfoMapper;

    @Resource
    private BaseFlightInfoMapper baseFlightInfoMapper;

    @Resource
    private RabbitTemplate rabbitTemplate;


    private final Map<String, EnumFlightStatusType> arrMap = new HashMap<>();
    private final Map<String, EnumFlightStatusType> depMap = new HashMap<>();


    @PostConstruct
    public void init() {
        // 进港航班状态对应关系
        arrMap.put(StatusValue.PLAN, EnumFlightStatusType.BEFORE_PLAN);
        arrMap.put(StatusValue.TAKEOFF, EnumFlightStatusType.BEFORE_TAKE_OFF);
        arrMap.put(StatusValue.ARRIVAL, EnumFlightStatusType.ARRIVE);
        arrMap.put(StatusValue.CANCEL, EnumFlightStatusType.ARRIVE_CAN);
        arrMap.put(StatusValue.DELAY, EnumFlightStatusType.ARRIVE_DLY);
        arrMap.put(StatusValue.DROP, EnumFlightStatusType.ARRIVE_ALT);
        arrMap.put(StatusValue.REVERSAL, EnumFlightStatusType.ARRIVE_RTN);

        // 出港航班对应关系
        depMap.put(StatusValue.PLAN, EnumFlightStatusType.STATION_PLA);
        depMap.put(StatusValue.TAKEOFF, EnumFlightStatusType.DEPARTURE);
        depMap.put(StatusValue.ARRIVAL, EnumFlightStatusType.AFTER_ARRIVE);
        depMap.put(StatusValue.CANCEL, EnumFlightStatusType.DEPARTURE_CAN);
        depMap.put(StatusValue.DELAY, EnumFlightStatusType.DEPARTURE_DLY);
        depMap.put(StatusValue.DROP, EnumFlightStatusType.DEPARTURE_ALT);
        depMap.put(StatusValue.REVERSAL, EnumFlightStatusType.DEPARTURE_RTN);
    }

    @Override
    public List<FlightSafeguardsInfoVO> safeguardsType(FlightSafeguardsInfoQueryDTO dto) {
        List<FlightSafeguardsInfoVO> getStatusInfo = baseAircraftSafeguardsInfoMapper.getStatusInfo(dto);
        getStatusInfo.forEach(e -> {
            if (EnumSafeguardType.PLAN.getCode().equals(e.getSafeguardType())
                    || EnumSafeguardType.DOING.getCode().equals(e.getSafeguardType())) {
                if (EnumSafeguardType.PLAN.getCode().equals(e.getSafeguardType())) {
                    e.setSafeguardsStatusName(EnumSafeguardsStatusName.PLAN.getDisplay());
                } else {
                    e.setSafeguardsStatusName(e.getSafeguardsStatusName());
                }
                if (arrMap.containsKey(e.getArrivalStatus())) {
                    e.setFlightStatus(arrMap.get(e.getArrivalStatus()).getDisplay());
                }

            } else {
                e.setSafeguardsStatusName(EnumSafeguardsStatusName.COMPLETE.getDisplay());
                if (depMap.containsKey(e.getDepartureStatus())) {
                    e.setFlightStatus(depMap.get(e.getDepartureStatus()).getDisplay());
                }
            }


        });

        return getStatusInfo;
    }


    @Override
    public Map<String, Integer> typeCount(FlightSafeguardsInfoQueryDTO dto) {
        Map<String, Integer> map = new LinkedHashMap<>();
        dto.setTypeOfSafeguards("PLAN");
        Integer count1 = baseAircraftSafeguardsInfoMapper.count(dto);
        dto.setTypeOfSafeguards("DONE");
        Integer count2 = baseAircraftSafeguardsInfoMapper.count(dto);
        dto.setTypeOfSafeguards("DOING");
        Integer count3 = baseAircraftSafeguardsInfoMapper.count(dto);
        map.put("PLAN", count1);
        map.put("DONE", count2);
        map.put("DOING", count3);
        return map;
    }

    @Override
    public Map<String, BaseFlightInfoVO> getFlightByFlightSafeguards(Long id) {
        Map<String, BaseFlightInfoVO> map = new HashMap<>();
        map.put(EnumDepartureArriveStatus.ARR.getCode(), null);
        map.put(EnumDepartureArriveStatus.DEP.getCode(), null);
        // 查找保障数据
        QueryWrapper<BaseAircraftSafeguardsInfo> queryWrapper = QueryWrapperUtil.createQueryWrapperAddColumnDel();
        queryWrapper.select("arrive_flight_id", "take_off_flight_id");
        queryWrapper.eq("id", id);
        queryWrapper.eq("deleted", DeletedEnum.NORMAL.getValue());
        BaseAircraftSafeguardsInfo baseAircraftSafeguardsInfoBusTwo = baseAircraftSafeguardsInfoMapper.selectOne(queryWrapper);
        if (ObjectUtils.isEmpty(baseAircraftSafeguardsInfoBusTwo)) {
            log.error("id为：{},的航班保障数据不存在", id);
            throw new BusinessException(FlightBusinessExceptionCodeConstant.FLIGHT_INFO_IS_NULL);
        }
        // 进港航班
        if (ObjectUtils.isNotEmpty(baseAircraftSafeguardsInfoBusTwo.getArriveFlightId())) {
            QueryWrapper<BaseFlightInfo> wrapper = QueryWrapperUtil.createQueryWrapperAddColumnDel();
            wrapper.eq("deleted", DeletedEnum.NORMAL.getValue());
            wrapper.eq("id", baseAircraftSafeguardsInfoBusTwo.getArriveFlightId());
            BaseFlightInfo baseFlightInfo = baseFlightInfoMapper.selectOne(wrapper);
            if (ObjectUtils.isNotEmpty(baseFlightInfo)) {
                map.put(EnumDepartureArriveStatus.ARR.getCode(), ObjectUtils.copyBean(baseFlightInfo, BaseFlightInfoVO.class));
            } else {
                map.put(EnumDepartureArriveStatus.ARR.getCode(), null);
            }
        }
        // 出港航班
        if (ObjectUtils.isNotEmpty(baseAircraftSafeguardsInfoBusTwo.getTakeOffFlightId())) {
            QueryWrapper<BaseFlightInfo> wrapper = QueryWrapperUtil.createQueryWrapperAddColumnDel();
            wrapper.eq("deleted", DeletedEnum.NORMAL.getValue());
            wrapper.eq("id", baseAircraftSafeguardsInfoBusTwo.getTakeOffFlightId());
            BaseFlightInfo baseFlightInfo = baseFlightInfoMapper.selectOne(wrapper);
            if (ObjectUtils.isNotEmpty(baseFlightInfo)) {
                map.put(EnumDepartureArriveStatus.DEP.getCode(), ObjectUtils.copyBean(baseFlightInfo, BaseFlightInfoVO.class));
            } else {
                map.put(EnumDepartureArriveStatus.DEP.getCode(), null);
            }
        }
        return map;
    }

    @Override
    public void changeSafeguardsStatus(FlightSafeguardsInfoChangeStatusDTO dto) {
        Long id = dto.getId();
        String safeguardType = dto.getSafeguardType();
        LambdaQueryWrapper<BaseAircraftSafeguardsInfo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BaseAircraftSafeguardsInfo::getId, id);
        BaseAircraftSafeguardsInfo exFlightSafeguardsInfo = baseAircraftSafeguardsInfoMapper.selectOne(queryWrapper);
        if (exFlightSafeguardsInfo == null) {
            throw new BusinessException(FlightBusinessExceptionCodeConstant.INVALID_SAFEGUARDS_ID);
        }
        if (CharSequenceUtil.equals(exFlightSafeguardsInfo.getSafeguardType(), safeguardType)) {
            throw new BusinessException(FlightBusinessExceptionCodeConstant.SAFEGUARD_TYPE_SAME_AS_BEFORE);
        }
        String currentUserName = UserContext.getCurrentUserName();
        if (EnumSafeguardType.DOING.getCode().equals(dto.getSafeguardType())) {
            dto.setSafeguardsStatusName(EnumSafeguardsStatusName.SAFEGUARDS_STATUS_NAME_NO_DATA.getDisplay());
        } else if (EnumSafeguardType.DONE.getCode().equals(dto.getSafeguardType())) {
            dto.setSafeguardsStatusName(EnumSafeguardsStatusName.COMPLETE.getDisplay());
        } else if (EnumSafeguardType.PLAN.getCode().equals(dto.getSafeguardType())) {
            dto.setSafeguardsStatusName(EnumSafeguardsStatusName.PLAN.getDisplay());
        }
        baseAircraftSafeguardsInfoMapper.changeSafeguardsStatus(dto, currentUserName);
        Long tenant = UserContext.getTenant();
        // 如果原数据是保障计划状态说明没有生成签单和保障节点，修改保障状态为正在保障或保障完成需要生成签单与节点
        if (EnumSafeguardType.PLAN.getCode().equals(exFlightSafeguardsInfo.getSafeguardType())) {
            String messageJson = generateJsonMessageConext(FlightEventEnum.NODE_GENERATE_EVENT.name(), tenant, exFlightSafeguardsInfo);
            rabbitTemplate.convertAndSend(RabbitMqConfiguration.EXCHANGE_FLIGHT_SERVICE, RabbitMqConfiguration.FLIGHT_EVENT_ROUTING_KEY.concat(String.valueOf(tenant)), messageJson);
        }
    }

    private String generateJsonMessageConext(String eventName, Long tenantId, BaseAircraftSafeguardsInfo dbBaseAircraftSafeguardsInfo) {
        List<MqDetailDto> mqList = new ArrayList<>();
        MqDetailDto mqDetailDto = new MqDetailDto();
        mqDetailDto.setFlightId(dbBaseAircraftSafeguardsInfo.getId());
        mqDetailDto.setTenantId(tenantId);
        mqDetailDto.setAircraftSafeguardsInfoDTO(ObjectUtils.copyBean(dbBaseAircraftSafeguardsInfo, MqAircraftSafeguardsInfoDTO.class));
        mqList.add(mqDetailDto);
        MqEventDto mqEventDto = new MqEventDto();
        mqEventDto.setEventName(eventName);
        mqEventDto.setEventMsg(mqList);
        String meassageStr = JSONUtil.toJsonPrettyStr(mqEventDto);
        log.info("发送消息内容:{}", meassageStr);
        return meassageStr;
    }
}

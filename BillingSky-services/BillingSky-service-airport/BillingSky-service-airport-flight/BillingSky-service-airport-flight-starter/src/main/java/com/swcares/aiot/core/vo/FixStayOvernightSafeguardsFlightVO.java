package com.swcares.aiot.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
/**
 * ClassName：com.swcares.aiot.core.vo.FixStayOvernightSafeguardsFlightVO <br>
 * Description：（永久）过夜保障航班返回展示对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-04-25 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FixStayOvernightSafeguardsFlightVO", description="（永久）过夜保障航班")
public class FixStayOvernightSafeguardsFlightVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;


    @ApiModelProperty(value = "进港航班号")
    private String arrivalFlightNo;

    @ApiModelProperty(value = "出港航班号")
    private String departureFlightNo;

    @ApiModelProperty(value = "进出港航班号拼接")
    private String arrDepFlightNo;

    @ApiModelProperty(value = "航线")
    private String airLine;

    @ApiModelProperty(value = "添加日期")
    private String addDatetime;

    @ApiModelProperty(value = "是否过夜")
    private Boolean isStayOvernight;

}

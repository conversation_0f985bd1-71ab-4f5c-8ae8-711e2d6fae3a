package com.swcares.aiot.core.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：BaseVO <br>
 * Package：com.swcares.base.common.vo <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 05月17日 10:27 <br>
 * @version v1.0 <br>
 */
@Data
public class BaseVO {

    @ApiModelProperty(value = "base_id")
    private Long baseId;

    @ApiModelProperty(value = "进港航班号")
    private String arrivalFlightNo;

    @ApiModelProperty(value = "出港航班号")
    private String departureFlightNo;

    @ApiModelProperty(value = "进出港航班号拼接")
    private String arrDepFlightNo;

    @ApiModelProperty(value = "航线")
    private String airLine;

    @ApiModelProperty(value = "添加日期")
    private String addDatetime;

    @ApiModelProperty(value = "是否过夜")
    private Boolean isStayOvernight;

    @ApiModelProperty(value = "计划到达时间")
    private LocalDateTime planLandingDatetime;

    @ApiModelProperty(value = "计划起飞时间")
    private LocalDateTime planTakeOffDatetime;

}

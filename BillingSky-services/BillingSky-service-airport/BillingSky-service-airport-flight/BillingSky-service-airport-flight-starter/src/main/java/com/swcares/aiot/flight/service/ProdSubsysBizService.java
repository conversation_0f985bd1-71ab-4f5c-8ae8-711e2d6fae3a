package com.swcares.aiot.flight.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.dto.ExternalProdSysImportDTO;
import com.swcares.aiot.core.dto.FileImportRecordPageDTO;
import com.swcares.aiot.core.dto.ImportFlightInfoDto;
import com.swcares.aiot.core.vo.BaseFileImportRecordVO;
import com.swcares.aiot.core.vo.ImportProdSysVerifyResultVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：FlightManagerService <br>
 * Package：com.swcares.base.flight.api.service.impl <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description: 航班信息         <br>
 *
 * <AUTHOR> <br>
 * date 2022年 06月17日 13:59 <br>
 * @version v1.0 <br>
 */
public interface ProdSubsysBizService {


    /**
     * Title：importFlightInfo
     * Description：导入航班客货邮行数据
     * author：李军呈
     * date： 2024/11/5 17:05
     *
     * @param file 文件
     * @param response 返回响应
     * @return com.swcares.aiot.core.vo.ImportProdSysVerifyResultVO 返回对象
     */
    ImportProdSysVerifyResultVO importFlightInfo(MultipartFile file, HttpServletResponse response) throws IOException;

    /**
     * Title：saveImportFlightInfoData
     * Description：确认导入的航班数据
     * author：李军呈
     * date： 2024/11/6 10:13
     *
     * @param dto 入参
     */
    boolean saveImportFlightInfoData(ImportFlightInfoDto dto);

    /**
     * Title：verifyFlight <br>
     * Description: 验证航班列表 <br>
     *
     * @param flightList 返回验证后结果，通过对象中属性verificationPass进行标识
     * author 周扬 <br>
     * date 2024/11/9 <br>
     */
    void verifyFlight(List<ExternalProdSysImportDTO> flightList);

    /**
     * Title：templateDownload
     * Description：航班数据与客货邮数据导入模板-下载
     * author：李军呈
     * date： 2024/11/8 14:16
     *
     * @param response response
     * @param fullName 模版路径
     */
    void templateDownload(HttpServletResponse response, String fullName) throws IOException;

    /**
     * Title：pageFileImportRecord <br>
     * Description: 分页查询 <br>
     *
     * @param dto 查询对象
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aiot.core.vo.BaseFileImportRecordVO>
     * author 周扬 <br>
     * date 2024/11/12 <br>
     */
    IPage<BaseFileImportRecordVO> pageFileImportRecord(FileImportRecordPageDTO dto);

    /**
     * Title：getImportLimitQuantity
     * Description：获取导入的限制数量
     * author：李军呈
     * date： 2024/11/27 13:49
     * @return java.lang.String
     */
    String getImportLimitQuantity();
}

package com.swcares.aiot.core.partition;

import org.springframework.cloud.stream.binder.PartitionSelectorStrategy;
import org.springframework.stereotype.Component;

/**
 * ClassName：FlightPartitionSelector
 * Description：自定义分区选择器
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2025/1/13 15:31
 * Version v1.0
 */
@Component
public class FlightPartitionSelector implements PartitionSelectorStrategy {
    @Override
    public int selectPartition(Object key, int partitionCount) {
        // 这里可以根据提取的分区键和分区数量来决定将消息发送到哪个分区
        // 使用 key 的 hashCode 来计算分区
        int hashCode = key.hashCode();
        // 确保分区在 0 到 partitionCount - 1 的范围内
        return Math.abs(hashCode % partitionCount);
    }
}

package com.swcares.aiot.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.common.cons.AppConstants;
import com.swcares.aiot.core.dto.FlightParkingManagerDTO;
import com.swcares.aiot.core.dto.FlightParkingManagerPagedDTO;
import com.swcares.aiot.core.entity.FlightParkingManager;
import com.swcares.aiot.service.FlightParkingManagerService;
import com.swcares.aiot.vo.FlightParkingManagerVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * ClassName：com.swcares.aiot.flight.parking.controller.FlightParkingManagerController <br>
 * Description：机位停用管理表 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/flight/parking/manager")
@Api(tags = "机位停用管理表接口")
@ApiVersion(AppConstants.SWAGGER_API_AIRCRAFT_STAND)
public class FlightParkingManagerController extends BaseController {
    @Resource
    private FlightParkingManagerService flightParkingManagerService;

    @PostMapping("/save")
    @ApiOperation(value = "新建机位停用管理表记录")
    public BaseResult<Object> save(@RequestBody FlightParkingManagerDTO dto) {
        FlightParkingManager flightParkingManager = ObjectUtils.copyBean(dto, FlightParkingManager.class);
        flightParkingManagerService.saveManager(flightParkingManager);
        return ok();
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "通过ID删除机位停用管理表记录")
    public BaseResult<Object> delete(@PathVariable @ApiParam(value = "主键id", required = true) Long id) {
        boolean deleted = flightParkingManagerService.logicRemoveById(id);
        if (!deleted) {
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        return ok();
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改机位停用管理表记录")
    public BaseResult<Object> update(@RequestBody FlightParkingManagerDTO dto) {
        FlightParkingManager flightParkingManager = ObjectUtils.copyBean(dto, FlightParkingManager.class);
        flightParkingManagerService.updateManager(flightParkingManager);
        return ok();
    }

    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询机位停用管理表记录")
    public PagedResult<List<FlightParkingManagerVO>> page(@RequestBody FlightParkingManagerPagedDTO dto) {
        IPage<FlightParkingManagerVO> result = flightParkingManagerService.page(dto);
        return ok(result);
    }
}

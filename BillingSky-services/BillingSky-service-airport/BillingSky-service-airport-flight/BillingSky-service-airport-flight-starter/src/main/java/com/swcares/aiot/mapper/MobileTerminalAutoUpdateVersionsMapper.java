package com.swcares.aiot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.core.dto.MobileTerminalAutoUpdateVersionsPagedDTO;
import com.swcares.aiot.core.dto.MobileTerminalAutoUpdateVersionsQueryDTO;
import com.swcares.aiot.core.entity.MobileTerminalAutoUpdateVersions;
import com.swcares.aiot.core.vo.MobileTerminalAutoUpdateVersionsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
/**
 * ClassName：com.swcares.base.flight.api.mapper.MobileTerminalAutoUpdateVersionsMapper <br>
 * Description：移动端自升级信息 Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-04-25 <br>
 * @version v1.0 <br>
 */
@Mapper
public interface MobileTerminalAutoUpdateVersionsMapper extends BaseMapper<MobileTerminalAutoUpdateVersions> {

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：chenyufeng <br>
     * date：2022-04-25 <br>
     * @param dto
     * @param page <br>
     * @return <br>
     */
    IPage<MobileTerminalAutoUpdateVersionsVO> page(@Param("dto") MobileTerminalAutoUpdateVersionsPagedDTO dto, Page<MobileTerminalAutoUpdateVersionsVO> page);

    /**
    * Title: list<br>
    * Author : chenyufeng<br>
    * Description :  移动端自升级信息_列表查询 <br>
    * @param dto  查询对象
    * date: 2022-04-25 <br>
    * return: List<MobileTerminalAutoUpdateVersionsVO>
     */
     List<MobileTerminalAutoUpdateVersionsVO> list(@Param("dto") MobileTerminalAutoUpdateVersionsQueryDTO dto);
    /**
     * Title: getNowVersionInfo <br>
     * Description: 获取系统的最新版本信息 <br>
     * author 周扬  <br>
     * date 2022/4/27 12:53<br>
     * @param dto 查询对象
     * @return  com.swcares.aiot.core.vo.MobileTerminalAutoUpdateVersionsVO
     */
    MobileTerminalAutoUpdateVersionsVO getNowVersionInfo(@Param("dto")MobileTerminalAutoUpdateVersionsQueryDTO dto);
}

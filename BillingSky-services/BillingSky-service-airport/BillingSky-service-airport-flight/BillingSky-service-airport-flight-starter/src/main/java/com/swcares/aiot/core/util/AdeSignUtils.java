package com.swcares.aiot.core.util;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Map;

/**
 * ClassName：com.swcares.base.flight.api.util.AdeSignUtils <br>
 * Description：ADE数字签名工具类 <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2024年1月25日 下午5:05:29 <br>
 * @version v1.0 <br>
 */
@Slf4j
@UtilityClass
public final class AdeSignUtils {
    /**
     * Description : 签名类型
     */
    public static final String HASH_TYPE_SHA256 = "SHA256";

    /**
     * Title：createSign <br>
     * Description：创建签名字符串 <br>
     * author：李军呈 <br>
     * date：2024年1月25日 下午5:06:13 <br>
     * @param parameters  签名的map
     * @param appsecret   签名的appsecret
     * @param timestamp   签名的时间戳
     * @param hashType    签名的加密类型
     * @return <br>
     */
    public static String createSign(Map<String, Object> parameters, String appsecret, String timestamp, String hashType) {
        StringBuffer sb = new StringBuffer();
        sb.append(appsecret).append(timestamp);
        //所有参与传参的参数按照accsii排序（升序）
        if (!CollectionUtils.isEmpty(parameters)) {
            parameters.entrySet()
                    .stream()
                    //过滤sign自身参数
                    .filter(paramEntry -> !ObjectUtils.isEmpty(paramEntry.getValue()) && !"sign".equals(paramEntry.getKey()) && !"key".equals(paramEntry.getKey()))
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(paramEntry -> sb.append('&').append(paramEntry.getKey()).append("=").append(paramEntry.getValue()));
        }
        if (HASH_TYPE_SHA256.equals(hashType)) {
            return DigestUtils.sha256Hex(sb.toString()).toUpperCase();
        }
        return sb.toString();
    }
}

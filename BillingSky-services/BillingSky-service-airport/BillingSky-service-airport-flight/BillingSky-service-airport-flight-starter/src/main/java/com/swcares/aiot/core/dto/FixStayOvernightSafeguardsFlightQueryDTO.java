package com.swcares.aiot.core.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName：com.swcares.aiot.node.safeguards.dto.FixStayOvernightSafeguardsFlightQueryDTO <br>
 * Description：（永久）过夜保障航班 查询_数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-04-25 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FixStayOvernightSafeguardsFlightQueryDTO", description="（永久）过夜保障航班")
public class FixStayOvernightSafeguardsFlightQueryDTO  implements BaseDTO, Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "进港航班号")
    private String arrivalFlightNo;

    @ApiModelProperty(value = "出港航班号")
    private String departureFlightNo;

    @ApiModelProperty(value = "航线")
    private String airLine;

    @ApiModelProperty(value = "删除")
    private Boolean deleted;

}

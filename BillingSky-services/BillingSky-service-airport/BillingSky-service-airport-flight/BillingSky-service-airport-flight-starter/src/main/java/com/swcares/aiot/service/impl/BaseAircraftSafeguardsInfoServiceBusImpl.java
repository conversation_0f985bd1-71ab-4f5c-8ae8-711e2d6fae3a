package com.swcares.aiot.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aiot.core.cons.BusinessExceptionCodeConstant;
import com.swcares.aiot.core.entity.BaseAircraftSafeguardsInfoBus;
import com.swcares.aiot.dto.*;
import com.swcares.aiot.enums.DeletedEnum;
import com.swcares.aiot.mapper.BaseAircraftSafeguardsInfoBusMapper;
import com.swcares.aiot.service.BaseAircraftSafeguardsInfoBusService;
import com.swcares.aiot.service.FlightParkingService;
import com.swcares.aiot.utils.QueryWrapperUtil;
import com.swcares.aiot.vo.BaseAircraftSafeguardsInfoVO;
import com.swcares.aiot.vo.FlightSafeguardVO;
import com.swcares.aiot.vo.FsBusAircraftParkingPageVO;
import com.swcares.aiot.vo.FsBusAircraftSafeguardsInfoVO;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

import static com.swcares.aiot.utils.AssertUtil.assertNull;


/**
 * ClassName：com.swcares.aiot.node.safeguards.service.impl.FsBusAircraftSafeguardsInfoServiceImpl <br>
 * Description：飞机保障信息表 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class BaseAircraftSafeguardsInfoServiceBusImpl extends ServiceImpl<BaseAircraftSafeguardsInfoBusMapper, BaseAircraftSafeguardsInfoBus> implements
        BaseAircraftSafeguardsInfoBusService {

    @Resource
    private BaseAircraftSafeguardsInfoBusMapper fsBusAircraftSafeguardsInfoMapper;

    @Resource
    private FlightParkingService flightParkingService;

    @Override
    public boolean logicRemoveById(Long id) {
        BaseAircraftSafeguardsInfoBus entity = new BaseAircraftSafeguardsInfoBus();
        entity.setId(id);
        entity.setDeleted(DeletedEnum.DELETED);
        return updateById(entity);
    }

    @Override
    public IPage<FsBusAircraftSafeguardsInfoVO> page(FsBusAircraftSafeguardsInfoPagedDTO dto) {
        IPage<FsBusAircraftSafeguardsInfoVO> page = baseMapper.page(dto, dto.createPage());
        HashMap<String, String> parkingGateMapping = flightParkingService.getParkingGateMapping(TenantHolder.getTenant());
        if (parkingGateMapping == null) {
            log.warn("无登机口与机位的关联信息，请检查");
        }
        page.getRecords().forEach(e -> {
            e.setGate(parkingGateMapping.get(e.getAircraftParking()));
            e.setArrDepFlightNo(e.getArrivalFlightNo() + "/" + e.getDepartureFlightNo());
        });
        return page;
    }

    @Override
    public List<FsBusAircraftSafeguardsInfoVO> list(FsBusAircraftSafeguardsInfoQueryDTO dto) {

        return baseMapper.list(dto, true);
    }

    @Override
    public FsBusAircraftParkingPageVO listByCondition(FsBusAircraftSafeguardsInfoQueryDTO dto) {
        //已分配机位的航班
        List<FsBusAircraftSafeguardsInfoVO> isDistributeList = baseMapper.list(dto, true);
        isDistributeList.forEach(e -> {
            e.setArrDepFlightNo(e.getArrivalFlightNo() + "/" + e.getDepartureFlightNo());
            if (e.getDepartureFlightNo() == null || e.getDepartureFlightNo().trim().isEmpty()) {
                e.setArrDepFlightNo(e.getArrivalFlightNo());
                e.setRealTakeOffDatetime(e.getRealLandingDatetime().plusHours(2));
                e.setManualFlag(true);
            }
            if (e.getArrivalFlightNo() == null || e.getArrivalFlightNo().trim().isEmpty()) {
                e.setArrDepFlightNo(e.getDepartureFlightNo());
                e.setRealLandingDatetime(e.getRealTakeOffDatetime().minusHours(2));
                e.setManualFlag(true);
            }
            e.setBusArriveTime(e.getRealLandingDatetime());
            if (e.getRealLandingDatetime().isBefore(dto.getFlightDateTime())) {
                e.setBusArriveTime(dto.getFlightDateTime());
            }
            e.setTotal(isDistributeList.size());
        });
        //未分配机位的航班
        List<FsBusAircraftSafeguardsInfoVO> isNotDistributeList = baseMapper.list(dto, false);
        isNotDistributeList.forEach(e -> {
            e.setArrDepFlightNo(e.getArrivalFlightNo() + "/" + e.getDepartureFlightNo());
            if (e.getDepartureFlightNo() == null || e.getDepartureFlightNo().trim().isEmpty()) {
                e.setArrDepFlightNo(e.getArrivalFlightNo());
                e.setRealTakeOffDatetime(e.getRealLandingDatetime().plusHours(2));
                e.setManualFlag(true);
            }
            if (e.getArrivalFlightNo() == null || e.getArrivalFlightNo().trim().isEmpty()) {
                e.setArrDepFlightNo(e.getDepartureFlightNo());
                e.setRealLandingDatetime(e.getRealTakeOffDatetime().minusHours(2));
                e.setManualFlag(true);
            }
            e.setBusArriveTime(e.getRealLandingDatetime());
            if (e.getRealLandingDatetime().isBefore(dto.getFlightDateTime())) {
                e.setBusArriveTime(dto.getFlightDateTime());
            }
            e.setTotal(isNotDistributeList.size());
        });

        FsBusAircraftParkingPageVO fsBusAircraftParkingPageVO = new FsBusAircraftParkingPageVO();
        fsBusAircraftParkingPageVO.setIsNotDistributeFlightInfos(isNotDistributeList);
        fsBusAircraftParkingPageVO.setIsDistributeFlightInfos(isDistributeList);
        return fsBusAircraftParkingPageVO;
    }

    @Override
    public BaseAircraftSafeguardsInfoBus getOne(Long id) {
        return fsBusAircraftSafeguardsInfoMapper.getOne(id);
    }

    @Override
    public List<BaseAircraftSafeguardsInfoBus> getByIdList(List<Long> idList) {
        return fsBusAircraftSafeguardsInfoMapper.getByIdList(idList);
    }

    @Override
    public List<FsBusAircraftSafeguardsInfoVO> getFlightInfoByFlightParkingNo(String aircraftParking, LocalDateTime selectTime, Boolean gtOrLt) {
        return fsBusAircraftSafeguardsInfoMapper.getFlightInfoByFlightParkingNo(aircraftParking, selectTime, gtOrLt);
    }

    @Override
    public BaseAircraftSafeguardsInfoBus getSelectOne(Long id) {
        return fsBusAircraftSafeguardsInfoMapper.getSelectOne(id);
    }

    @Override
    public Map<String, List<FsBusAircraftSafeguardsInfoVO>> getByAircraftParkingList(List<String> allAircraftParkingList) {
        Map<String, List<FsBusAircraftSafeguardsInfoVO>> map = new HashMap<>();
        for (String aircraft : allAircraftParkingList) {
            List<BaseAircraftSafeguardsInfoBus> list = fsBusAircraftSafeguardsInfoMapper.selectByFlightNo(aircraft);
            List<FsBusAircraftSafeguardsInfoVO> fsBusAircraftSafeguardsInfoVos = ObjectUtils.copyBeans(list, FsBusAircraftSafeguardsInfoVO.class);
            map.put(aircraft, fsBusAircraftSafeguardsInfoVos);
        }
        return map;
    }

    @Override
    public List<FlightSafeguardVO> selectSafeguardsType(FlightSafeguardsInfoQueryDTO dto) {
        return fsBusAircraftSafeguardsInfoMapper.selectSafeguardsType(dto);
    }

    @Override
    public boolean saveNoRepeat(BaseAircraftSafeguardsInfoBus entity) {
        QueryWrapper<BaseAircraftSafeguardsInfoBus> queryWrapper = QueryWrapperUtil.createQueryWrapperAddColumnDel();
        //添加去重复条件
        List<BaseAircraftSafeguardsInfoBus> repeatList = baseMapper.selectList(queryWrapper);
        assertNull(repeatList, BusinessExceptionCodeConstant.REPEAT_DATA_CODE);
        return save(entity);
    }


    @Override
    public Map<String, Integer> typeCount(FlightSafeguardsInfoQueryDTO dto) {
        Map<String, Integer> map = new LinkedHashMap<>();
        dto.setTypeOfSafeguards("PLAN");
        Integer count1 = fsBusAircraftSafeguardsInfoMapper.count(dto);
        dto.setTypeOfSafeguards("DONE");
        Integer count2 = fsBusAircraftSafeguardsInfoMapper.count(dto);
        dto.setTypeOfSafeguards("DOING");
        Integer count3 = fsBusAircraftSafeguardsInfoMapper.count(dto);
        map.put("PLAN", count1);
        map.put("DONE", count2);
        map.put("DOING", count3);
        return map;
    }

    @Override
    public IPage<FsBusAircraftSafeguardsInfoVO> page1(BaseFlightInfoPagedDTO dto) {
        BaseFlightInfoPaged1DTO baseFlightInfoPaged1DTO = new BaseFlightInfoPaged1DTO();
        baseFlightInfoPaged1DTO.setFlightNo(dto.getFlightNo())
                .setSelectType(dto.getSelectType())
                .setPageNumber(dto.getPageNumber())
                .setPageSize(dto.getPageSize())
                .setItems(dto.getItems())
                .setIsAsc(dto.getIsAsc());
        if (Objects.nonNull(dto.getFlightDateTime())) {
            LocalDate localDate = dto.getFlightDateTime().toLocalDate();
            baseFlightInfoPaged1DTO.setStartTime(localDate.atStartOfDay())
                    .setEndTime(LocalDateTime.of(localDate, LocalTime.MAX));
        }
        IPage<FsBusAircraftSafeguardsInfoVO> allTypes = fsBusAircraftSafeguardsInfoMapper.page1(baseFlightInfoPaged1DTO, dto.createPage());
        HashMap<String, String> parkingGateMapping = flightParkingService.getParkingGateMapping(TenantHolder.getTenant());
        if (parkingGateMapping == null) {
            log.warn("无登机口与机位的关联信息，请检查");
        }
        if (!allTypes.getRecords().isEmpty()) {
            allTypes.getRecords().forEach(e -> {
                e.setGate(parkingGateMapping.get(e.getAircraftParking()));
                if ("PLAN".equals(e.getSafeguardType())) {
                    e.setSafeguardsStatusName("计划");
                } else if ("DONE".equals(e.getSafeguardType())) {
                    e.setSafeguardsStatusName("完成");
                } else {
                    e.setSafeguardsStatusName(e.getSafeguardsStatusName());
                }

            });
        }
        return allTypes;
    }

    @Override
    public AircraftSafeguardsInfoBusDTO selectSafeguardsById(String id) {
        Long aLong = null;
        if (CharSequenceUtil.isNotEmpty(id)) {
            aLong = Long.valueOf(id);
        }
        QueryWrapper<BaseAircraftSafeguardsInfoBus> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0);
        queryWrapper.eq("id", aLong);
        BaseAircraftSafeguardsInfoBus baseAircraftSafeguardsInfoBus1 = fsBusAircraftSafeguardsInfoMapper.selectOne(queryWrapper);
        return ObjectUtils.copyBean(baseAircraftSafeguardsInfoBus1, AircraftSafeguardsInfoBusDTO.class);
    }

    @Override
    public List<FsBusAircraftSafeguardsInfoVO> getFlightParkingByCondition(String aircraftParking, LocalDateTime selectTime, Boolean gtOrLt) {
        return fsBusAircraftSafeguardsInfoMapper.getFlightParkingByCondition(aircraftParking, selectTime, gtOrLt);

    }

    @Override
    public List<FsBusAircraftSafeguardsInfoVO> listBySafeguardsType(BaseAircraftSafeguardsInfoQueryDTO dto) {
        return fsBusAircraftSafeguardsInfoMapper.listBySafeguardsType(dto);
    }

    @Override
    public List<FsBusAircraftSafeguardsInfoVO> getFlightByArrDepTime(FsBusAircraftSafeguardsInfoDTO dto) {
        return fsBusAircraftSafeguardsInfoMapper.getFlightByArrDepTime(dto);
    }

    @Override
    public List<BaseAircraftSafeguardsInfoVO> selectFlightInfo(FlightSafeguardsInfoQueryDTO dto) {
        return fsBusAircraftSafeguardsInfoMapper.selectAllFlightInfo(dto);
    }

    @Override
    public List<FsBusAircraftSafeguardsInfoVO> selectAll(BaseFlightInfoPagedDTO dto) {
        return fsBusAircraftSafeguardsInfoMapper.selectAll(dto);
    }

    @Override
    public void updateByBusId(BaseAircraftSafeguardsInfoBus baseAircraftSafeguardsInfoBus) {
        fsBusAircraftSafeguardsInfoMapper.updateByBusId(baseAircraftSafeguardsInfoBus);
    }


    @Override
    public List<FsBusAircraftSafeguardsInfoVO> getOvernightInfo(FlightSafeguardsInfoQueryDTO dto) {
        return fsBusAircraftSafeguardsInfoMapper.getOvernightInfo(dto);
    }

}

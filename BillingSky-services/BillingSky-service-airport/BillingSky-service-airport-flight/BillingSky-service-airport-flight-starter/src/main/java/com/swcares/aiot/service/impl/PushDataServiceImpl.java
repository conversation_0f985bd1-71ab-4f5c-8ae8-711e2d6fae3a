package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.aiot.Message;
import com.swcares.aiot.core.common.config.RabbitMqConfiguration;
import com.swcares.aiot.core.common.cons.AppConstants;
import com.swcares.aiot.core.common.cons.FlightCons;
import com.swcares.aiot.core.entity.*;
import com.swcares.aiot.flight.service.IMessageService;
import com.swcares.aiot.header.MessageHeader;
import com.swcares.aiot.mapper.BaseFlightCargoMapper;
import com.swcares.aiot.mapper.BaseFlightCargoSegmentMapper;
import com.swcares.aiot.mapper.BaseFlightInfoMapper;
import com.swcares.aiot.mapper.BaseFlightTravelerMapper;
import com.swcares.aiot.mapper.BaseFlightTravelerSegmentMapper;
import com.swcares.aiot.payload.MessagePayload;
import com.swcares.aiot.service.BaseConfigInfoService;
import com.swcares.aiot.service.PushDataService;
import com.swcares.aiot.service.mq.SendDataToRabbitMqService;
import com.swcares.aiot.vo.FlightInfoVO;
import com.swcares.baseframe.common.core.tenant.TenantDataSourceParam;
import com.swcares.baseframe.common.core.tenant.TenantDataSourceParamFetcher;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.lang.DateUtils;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：PushDataServiceImpl <br>
 * Package：com.swcares.base.flight.api.service.impl <br>
 * Copyright 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 * Description: 推送航班、货邮行数据  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 08月24日 13:50 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class PushDataServiceImpl implements PushDataService {
    /**
     * Description : BaseConfigInfo 表中  key 的值
     */
    private static final String PUSH_FLIGHT_DATA_LIST = "push_flight_data_list";

    @Resource
    private SendDataToRabbitMqService sendDataToRabbitMq;

    /**
     * Description : 用于获取租户数据源信息的接口
     */
    @Resource
    private TenantDataSourceParamFetcher tenantDataSourceParamFetcher;


    @Resource
    private BaseConfigInfoService baseConfigInfoService;

    @Resource
    private BaseFlightInfoMapper baseFlightInfoMapper;

    @Resource
    private BaseFlightTravelerMapper baseFlightTravelerMapper;

    @Resource
    private BaseFlightCargoMapper baseFlightCargoMapper;

    @Resource
    private BaseFlightTravelerSegmentMapper travelerSegmentMapper;

    @Resource
    private BaseFlightCargoSegmentMapper cargoSegmentMapper;

    @Resource
    private IMessageService messageService;

    private static final String MESSAGE_ERROR = "数据发送失败,id是{},租户{},error:";


    @Override
    public void pushDataToSubsystem() {
        log.info("推送数据给各个子系统开始运行。时间为：{}", DateUtils.formatDate(new Date(), DateUtils.DOT_PTN_YMD_HMSS));
        // 读取此系统——在UC中租户列表
        List<TenantDataSourceParam> ucTenantDataSourceParams = tenantDataSourceParamFetcher.getAllTenantDataSourceParams();
        Set<Long> ucTenantIdList = ucTenantDataSourceParams.stream().map(TenantDataSourceParam::getTenantId).collect(Collectors.toSet());
        if (ObjectUtils.isEmpty(ucTenantIdList)) {
            log.error("此系统——在UC中租户列表在连接信息中未获取到！请检查redis！ucTenantDataSourceParams = {}", ucTenantDataSourceParams);
            return;
        }
        log.info("pushDataToSubsystem方法，UC中租户列表 ucTenantIdList = {}", ucTenantIdList);
        // 3、将设置租户将航班数据 发送到 中间件 mq
        for (Long ucTenantId : ucTenantIdList) {
            boolean pushFlightDataToMqEnable = ConfigUtil.getBoolean(AppConstants.APP_SYSTEM_CONFIG_TYPE_KEY, AppConstants.PUSH_FLIGHT_DATA_TO_MQ_ENABLE_KEY, ucTenantId);
            if (!pushFlightDataToMqEnable) {
                log.debug("ucTenantId = {}, 未开启  发送航班数据到mq-任务开关 ，请检查配置表！ key={}",
                        ucTenantId, AppConstants.PUSH_FLIGHT_DATA_TO_MQ_ENABLE_KEY);
                continue;
            }
            try {
                // 单个租户 推送航数据到mq
                TenantHolder.setTenant(ucTenantId);
                boolean successFlag = this.pushFlightDateSingleTenant(ucTenantId);
                log.info("ucTenantId = {}，推送数据完成，执行结果 successFlag = {}", ucTenantId, successFlag);
            } catch (Exception e) {
                log.error("（单个租户 推送航数据到mq 失败，请检查租户id= {}", ucTenantId, e);
            } finally {
                TenantHolder.clear();
            }
        }


    }

    /**
     * Title：pushFlightDateSingleTenant
     * Description: pushFlightDateSingleTenant
     *
     * @param tempTenantId 临时入库租户id
     * @return : boolean
     * <AUTHOR>
     * date 2024/3/5 23:21
     */
    private boolean pushFlightDateSingleTenant(Long tempTenantId) {
        log.info("开始推送航班数据到各子系统，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
        String pushDataEndTime = LocalDateTimeUtil.format(LocalDateTime.now().minusMinutes(1), DatePattern.NORM_DATETIME_FORMATTER);

        log.info("获取本次处理数据的开始时间（数据最后一次操作的时间），dateTime= {}", getNowLocalDateTime());
        BaseConfigInfo baseConfigInfo = baseConfigInfoService.getByKey(PUSH_FLIGHT_DATA_LIST);
        if (baseConfigInfo == null) {
            log.info("无同步值，请确认base_config_info,是否有值");
            return false;
        }
        Date waitDisposeStartTime = DateUtils.parseDate(baseConfigInfo.getValue());
        log.info("（数据最后一次操作的时间），dateTime= {}", baseConfigInfo.getValue());

        List<FlightInfoVO> flightInfoList = baseFlightInfoMapper.getDataToLastUpdateTime(waitDisposeStartTime);
        List<BaseFlightTraveler> travelerList = baseFlightTravelerMapper.getDataToLastUpdateTime(waitDisposeStartTime);
        List<BaseFlightCargo> cargoList = baseFlightCargoMapper.getDataToLastUpdateTime(waitDisposeStartTime);
        List<BaseFlightTravelerSegment> travelerSegmentList = travelerSegmentMapper.getDataToLastUpdateTime(waitDisposeStartTime);
        List<BaseFlightCargoSegment> cargoSegmentList = cargoSegmentMapper.getDataToLastUpdateTime(waitDisposeStartTime);
        List<FlightInfoVO> stayTimeDataList = baseFlightInfoMapper.getStayTimeDataToLastUpdateTime(waitDisposeStartTime);
        log.info("本次推送的航班数量{}，旅客数量{}，货邮行数量{}，拆分旅客{}，拆分货邮行{}，停场时间数量{}",
                flightInfoList.size(), travelerList.size(), cargoList.size(),
                travelerSegmentList.size(), cargoSegmentList.size(), stayTimeDataList.size());

        if (CollUtil.isNotEmpty(flightInfoList)) {
            sendFlightInfo(flightInfoList, tempTenantId);
            log.info("发送航班数据到kafak完成，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
            sendDataToRabbitMq.sendFlightVoDataToFlightExchange(flightInfoList, tempTenantId, RabbitMqConfiguration.FLIGHT_DATA_BASE_ROUTING_KEY);
            log.info("发送航班数据到mq的交换机完成，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
        }
        if (CollUtil.isNotEmpty(travelerList)) {
            sendFlightTraveler(travelerList, tempTenantId);
            log.info("发送旅客数据到kafak完成，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
            sendDataToRabbitMq.sendTravelerListDataToFlightExchange(travelerList, tempTenantId, RabbitMqConfiguration.FLIGHT_DATA_TRAVELER_ROUTING_KEY);
            log.info("发送旅客数据到mq的交换机完成，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
        }
        if (CollUtil.isNotEmpty(cargoList)) {
            sendFlightcargo(cargoList, tempTenantId);
            log.info("发送货邮行数据到kafak完成，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
            sendDataToRabbitMq.sendCargoListDataToFlightExchange(cargoList, tempTenantId, RabbitMqConfiguration.FLIGHT_DATA_CARGO_ROUTING_KEY);
            log.info("发送货邮行数据到mq的交换机完成，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
        }
        if (CollUtil.isNotEmpty(travelerSegmentList)) {
            sendFlightTravelerSegment(travelerSegmentList, tempTenantId);
            log.info("发送拆分后旅客数据到kafak完成，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
        }
        if (CollUtil.isNotEmpty(cargoSegmentList)) {
            sendFlightCargoSegment(cargoSegmentList, tempTenantId);
            log.info("发送拆分后货邮数据到kafak完成，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
        }
        if (CollUtil.isNotEmpty(stayTimeDataList)) {
            sendStayTimeData(stayTimeDataList, tempTenantId);
            log.info("发送停场时间数据到kafak完成，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
        }

        log.info("更新最后的处理时间，pushDataEndTime= {}", pushDataEndTime);
        baseConfigInfoService.updateByKey(PUSH_FLIGHT_DATA_LIST, pushDataEndTime);
        return true;
    }

    private void sendFlightInfo(List<FlightInfoVO> flightInfoList, Long tempTenantId){
        log.info("推送航班消息到kafka，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
        flightInfoList.forEach(flight -> {
            try {
                sendMessage(JSONUtil.toJsonStr(flight), tempTenantId, FlightCons.TYPE_FLIGHT_INFO, flight.getId());
            } catch (Exception e) {
                log.error(MESSAGE_ERROR, flight.getId(), tempTenantId, e);
            }
        });
    }

    private void sendFlightTraveler(List<BaseFlightTraveler> travelerList, Long tempTenantId){
        log.info("推送旅客信息到kafka，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
        travelerList.forEach(flight -> {
            try {
                sendMessage(JSONUtil.toJsonStr(flight), tempTenantId, FlightCons.TYPE_FLIGHT_TRAVELER, flight.getId());
            } catch (Exception e) {
                log.error(MESSAGE_ERROR, flight.getId(), tempTenantId, e);
            }
        });
    }

    private void sendFlightcargo(List<BaseFlightCargo> cargoList, Long tempTenantId){
        log.info("推送货邮行信息到kafka，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
        cargoList.forEach(flight -> {
            try {
                sendMessage(JSONUtil.toJsonStr(flight), tempTenantId, FlightCons.TYPE_FLIGHT_CARGO, flight.getId());
            } catch (Exception e) {
                log.error(MESSAGE_ERROR, flight.getId(), tempTenantId, e);
            }
        });
    }

    private void sendFlightTravelerSegment(List<BaseFlightTravelerSegment> travelerSegmentList, Long tempTenantId){
        log.info("推送旅客拆分信息到kafka，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
        travelerSegmentList.forEach(flight -> {
            try {
                sendMessage(JSONUtil.toJsonStr(flight), tempTenantId, FlightCons.TYPE_FLIGHT_TRAVELER_SEGMENT, flight.getId());
            } catch (Exception e) {
                log.error(MESSAGE_ERROR, flight.getId(), tempTenantId, e);
            }
        });
    }

    private void sendFlightCargoSegment(List<BaseFlightCargoSegment> cargoSegmentList, Long tempTenantId){
        log.info("推送货邮行拆分信息到kafka，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
        cargoSegmentList.forEach(flight -> {
            try {
                sendMessage(JSONUtil.toJsonStr(flight), tempTenantId, FlightCons.TYPE_FLIGHT_CARGO_SEGMENT, flight.getId());
            } catch (Exception e) {
                log.error(MESSAGE_ERROR, flight.getId(), tempTenantId, e);
            }
        });
    }

    private void sendStayTimeData(List<FlightInfoVO> flightInfoVO, Long tempTenantId){
        log.info("推送停场时间数据到kafka，租户{}，dateTime= {}", tempTenantId, getNowLocalDateTime());
        flightInfoVO.forEach(flight -> {
            try {
                sendMessage(JSONUtil.toJsonStr(flight), tempTenantId, FlightCons.TYPE_FLIGHT_INFO, flight.getId());
            } catch (Exception e) {
                log.error(MESSAGE_ERROR, flight.getId(), tempTenantId, e);
            }
        });
    }

    /**
     * Title：sendMessage
     * Description：发送数据到kafka
     * author：李军呈
     * date： 2024/12/16 16:17
     * @param data  数据
     * @param tempTenantId 租户
     * @param dataType 数据类型
     * @param msgId 消息id,传数据主键id, 同一航班数据发送到同一分区有序
     */
    private void sendMessage(String data, Long tempTenantId, String dataType, Long msgId){
        Message message = new Message();
        MessageHeader messageHeader = new MessageHeader();
        MessagePayload<Object> payload = new MessagePayload<>();
        payload.setData(data);
        message.setPayload(payload);
        messageHeader.setMsgId(msgId);
        messageHeader.setDataType(dataType);
        messageHeader.setSendCustCode(FlightCons.SEND_SYSTEM_FLIGHT);
        messageHeader.setTargetCustCodes(Collections.singletonList(tempTenantId.toString()));
        message.setHeader(messageHeader);
        messageService.send(message);
    }

    private String getNowLocalDateTime(){
        return DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_MS_PATTERN);
    }

}

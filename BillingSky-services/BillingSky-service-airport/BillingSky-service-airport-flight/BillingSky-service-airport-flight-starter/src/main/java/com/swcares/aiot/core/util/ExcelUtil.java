/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ExcelUtils <br>
 * Package：com.swcares.aiot.datacenter.importer.utils <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 */
package com.swcares.aiot.core.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.swcares.aiot.cons.BusinessExceptionCodeConstants;
import com.swcares.baseframe.common.exception.BusinessException;
import com.worm.hutool.HttpUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

/**
 * ClassName：com.swcares.aiot.flight.parking.util <br>
 * Description：Excel工具类 <br>
 *
 * <AUTHOR> <br>
 * date 2022/3/11 16:16 <br>
 * @version v1.0.0 <br>
 */
public class ExcelUtil {

    private static final Logger log = LoggerFactory.getLogger(ExcelUtil.class);

    private ExcelUtil() {
    }

    public static void exportExcel(List<?> list, Class<?> pojoClass, String fileName, HttpServletResponse response) {
        try {
            ExportParams exportParams = new ExportParams(null, null, ExcelType.XSSF);
            final Workbook workbook = ExcelExportUtil.exportExcel(exportParams, pojoClass, list);
            downLoadExcel(fileName, workbook, response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(BusinessExceptionCodeConstants.EXPORT_DATA_FAILED, fileName);
        }
    }

    /**
     * Title：downLoadExcel <br>
     * Description：下载文件<br>
     *
     * @param fileName :
     * @param workbook :
     * @param response :
     * <AUTHOR> <br>
     * date：2021-06-01 10:53 <br>
     */
    private static void downLoadExcel(String fileName, Workbook workbook, HttpServletResponse response)
            throws IOException {
        ServletOutputStream output = null;
        try {
            final String downloadName = URLEncoder.encode(fileName + ".xlsx", "UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader(HttpUtils.HEADER_CONTENT_DISPOSITION, "attachment;filename=" + downloadName);
            output = response.getOutputStream();
            workbook.write(output);
        } catch (final Exception e) {
            throw new IOException(e.getMessage());
        } finally {
            if (output != null) {
                output.flush();
                output.close();
            }
        }
    }

}

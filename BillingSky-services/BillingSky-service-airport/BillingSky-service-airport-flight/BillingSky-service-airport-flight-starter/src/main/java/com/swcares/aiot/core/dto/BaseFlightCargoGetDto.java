package com.swcares.aiot.core.dto;

import com.swcares.aiot.core.entity.AdeFlightInfo;
import com.swcares.aiot.core.entity.AircraftInformation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Accessors(chain = true)
public class BaseFlightCargoGetDto implements Serializable {
    private String fltCode;
    private LocalDateTime flightDate;
    private String airportId;
    private String flightSegment;
    private String landFlag;
    private Map<String, AdeFlightInfo> adeMap;
    private Map<String, AdeFlightInfo> adePassMap;
    private AircraftInformation aircraftInformation;
}

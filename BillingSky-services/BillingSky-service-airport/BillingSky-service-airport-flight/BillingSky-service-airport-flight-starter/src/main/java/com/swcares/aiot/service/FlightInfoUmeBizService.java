package com.swcares.aiot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.core.dto.DataSettingDTO;
import com.swcares.aiot.core.dto.FlightInfoUmePagedDTO;
import com.swcares.aiot.core.vo.FlightInfoUmeVO;

import java.util.Date;

/**
 * ClassName：com.swcares.base.cc.service.FlightInfoUmeService <br>
 * Description：机场行（SAE）航班数据(A-CDM) 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-01-28 <br>
 * @version v1.0 <br>
 */
public interface FlightInfoUmeBizService {

    /**
     * Title：getCollectFlightDataFromUmeByAirportCode <br>
     * Description: 指定机场采集指定日期的航班入库到航班系统库 <br>
     * date 2024/5/24 <br>
     *
     * @param code  机场三字码<br>
     * @param executeDate  执行日期<br>
     *  <br>
     */
    void getCollectFlightDataFromUmeByAirportCode(String code, Date executeDate);
    /**
     * Title:  saveNoRepeat<br>
     * Description:获取航班纵横的航班数据 <br>
     * date：2024-01-28 <br>
     *
     * @param airportCode  机场三字码<br>
     * @param executeDate  执行日期
     */
    void getFlightDataFromUmeByAirportCode(String airportCode, Date executeDate);

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * date：2024-02-22 <br>
     * @param dto 查询对象<br>
     * @return <br>
     */
    IPage<FlightInfoUmeVO> page(FlightInfoUmePagedDTO dto);

    /**
     * Title：syncDataToBaseFlightInfo <br>
     * Description: 同步ume采集表的数据到 航班业务表 <br>
     * date 2024/5/27 <br>
     *
     * @return : boolean  例如：true : 同步成功  false ：同步失败  <br>
     */
    boolean syncDataToBaseFlightInfo();

    /**
     * Title：incrementSyncOfSingleTenant <br>
     * Description: 增量同步航班数据<br>
     * date 2024/5/28 <br>
     *
     * @return boolean true : 同步成功 false：同步失败 <br>
     */
    boolean incrementSyncOfSingleTenant();

    /**
     * Title：syncUiSettingUmeFlightData <br>
     * Description: 根据配置条件 将ume表数据 同步到  base-flight-info 表中 <br>
     * date 2024/5/28 <br>
     *
     * @param dataSettingDto  配置传输对象
     * @return boolean true : 同步成功 false：同步失败 <br>
     */
    boolean syncUiSettingUmeFlightData(DataSettingDTO dataSettingDto);

    /**
     * Title：flightDelayCheck
     * Description：航班延误校验
     * date： 2024/11/19 16:03
     */
    void flightDelayCheck(String startDate);

    /**
     * Title：flightDelayCheck8Hours
     * Description：航班延误8小时校验
     * date： 2024/11/19 16:20
     */
    void flightDelayCheck8Hours(String startDate);
}

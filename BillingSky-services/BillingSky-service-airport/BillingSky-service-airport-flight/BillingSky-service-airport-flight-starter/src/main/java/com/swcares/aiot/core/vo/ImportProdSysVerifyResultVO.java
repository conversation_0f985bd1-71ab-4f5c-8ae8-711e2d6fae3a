package com.swcares.aiot.core.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.swcares.aiot.file.vo.AttachmentVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.aiot.core.vo.ImportProdSysVaildResultVO <br>
 * Description：生产子系统导入返还数据对象 <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> 扬 <br>
 * date 2024/11/7 13:07 <br>
 * @version V1.0 <br>
 */
@Data
@ApiModel(value = "ImportProdSysVerifyResultVO", description = "验证结果数据对象")
public class ImportProdSysVerifyResultVO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "类型（1=客货邮行文件导入）")
    private Integer type;

    @ApiModelProperty(value = "是否显示(1:显示，0:不显示)")
    private Boolean displayed;

    @ApiModelProperty(value = "总数量")
    private Integer totalNumber;

    @ApiModelProperty(value = "新增航班数量")
    private Integer addFlightNumber;

    @ApiModelProperty(value = "成功数量")
    private Integer successNumber;

    @ApiModelProperty(value = "错误数量")
    private Integer errorNumber;

    @ApiModelProperty(value = "存在数量")
    private Integer existNumber;

    @ApiModelProperty(value = "导入文件内容")
    private AttachmentVO importAttachmentVO;

    @ApiModelProperty(value = "错误文件内容")
    private AttachmentVO errorAttachmentVO;

    @ApiModelProperty(value = "成功文件内容")
    private String successFileContent;

    @ApiModelProperty(value = "导入文件key")
    private String importFileKey;

    @ApiModelProperty(value = "错误文件key")
    private String errorFileKey;

    @ApiModelProperty(value = "成功文件key")
    private String successFileKey;

    @ApiModelProperty(value = "扩展字段(JSON列)")
    @TableField("ext")
    private String ext;

    @ApiModelProperty(value = "任务ID")
    private String taskId;


}

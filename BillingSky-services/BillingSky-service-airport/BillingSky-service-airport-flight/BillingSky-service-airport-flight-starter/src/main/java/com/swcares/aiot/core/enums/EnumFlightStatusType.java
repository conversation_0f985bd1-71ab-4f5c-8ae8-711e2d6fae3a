package com.swcares.aiot.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum EnumFlightStatusType {

    /**
     * Description : 前站计划 <br/>
     */
    BEFORE_PLAN("BEFORE_PLAN", "前站计划"),
    /**
     * Description : 前站起飞 <br/>
     */
    BEFORE_TAKE_OFF("BEFORE_TAKE_OFF", "前站起飞"),
    /**
     * Description : 到达本站 <br/>
     */
    ARRIVE("ARRIVE", "到达本站"),
    /**
     * Description : 进港取消 <br/>
     */
    ARRIVE_CAN("ARRIVE_CAN", "进港取消"),
    /**
     * Description : 进港延误 <br/>
     */
    ARRIVE_DLY("ARRIVE_DLY", "进港延误"),
    /**
     * Description : 进港备降 <br/>
     */
    ARRIVE_ALT("ARRIVE_ALT", "进港备降"),
    /**
     * Description : 进港返航 <br/>
     */
    ARRIVE_RTN("ARRIVE_RTN", "进港返航"),
    /**
     * Description : 本站起飞 <br/>
     */
    DEPARTURE("DEPARTURE", "本站起飞"),
    /**
     * Description : 下站到达 <br/>
     */
    AFTER_ARRIVE("AFTER_ARRIVE", "下站到达"),
    /**
     * Description : 本站计划 <br/>
     */
    STATION_PLA("STATION_PLA", "本站计划"),
    /**
     * Description : 出港取消 <br/>
     */
    DEPARTURE_CAN("DEPARTURE_CAN", "出港取消"),
    /**
     * Description : 出港延误 <br/>
     */
    DEPARTURE_DLY("DEPARTURE_DLY", "出港延误"),
    /**
     * Description : 出港备降 <br/>
     */
    DEPARTURE_ALT("DEPARTURE_ALT", "出港备降"),
    /**
     * Description : 出港返航 <br/>
     */
    DEPARTURE_RTN("DEPARTURE_RTN", "出港返航");

    private final String code;
    private final String display;

}

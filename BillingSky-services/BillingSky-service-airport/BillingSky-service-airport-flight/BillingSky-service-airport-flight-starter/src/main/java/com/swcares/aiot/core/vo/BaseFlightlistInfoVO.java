package com.swcares.aiot.core.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：BaseFlightlistInfoVO <br>
 * Package：com.swcares.base.common.vo <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 04月05日 19:10 <br>
 * @version v1.0 <br>
 */
@Data
public class BaseFlightlistInfoVO {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "出发机场三字码")
    private String departureAirportCode;

    @ApiModelProperty(value = "到达机场三字码")
    private String destinationAirportCode;

    @ApiModelProperty(value = "计划到达时间")
    private LocalDateTime planLandingDatetime;

    @Excel(name="实际到达时间")
    @ApiModelProperty(value = "实际到达时间")
    private LocalDateTime realLandingDatetime;

    @ApiModelProperty(value = "预计到达时间")
    private LocalDateTime predictLandingDatetime;

    @ApiModelProperty(value = "计划起飞时间")
    private LocalDateTime planTakeOffDatetime;

    @ApiModelProperty(value = "实际起飞时间")
    @Excel(name="实际到达时间")
    private LocalDateTime realTakeOffDatetime;

    @ApiModelProperty(value = "预计到达时间")
    private LocalDateTime predictTakeOffDatetime;

    @ApiModelProperty(value = "航班日期")
    private LocalDateTime flightDateTime;

    @ApiModelProperty(value = "延误时间长度")
    private String lengthOfDelay;

    @ApiModelProperty(value = "航班状态")
    private String status;

    @ApiModelProperty(value = "计算航班数")
    private Integer total;
}

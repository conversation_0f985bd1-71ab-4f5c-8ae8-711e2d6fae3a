package com.swcares.aiot.core.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * ClassName：dto.FlightParkingManagerPagedDTO <br>
 * Description：机位停用管理表 分页数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="FlightParkingManagerPagedDTO", description="机位停用管理表")
public class FlightParkingManagerPagedDTO extends PagedDTO{

    private static final long serialVersionUID = 1L;

    @NotNull
    @ApiModelProperty(value = "机位id")
    private Long fkFlightParking;



}

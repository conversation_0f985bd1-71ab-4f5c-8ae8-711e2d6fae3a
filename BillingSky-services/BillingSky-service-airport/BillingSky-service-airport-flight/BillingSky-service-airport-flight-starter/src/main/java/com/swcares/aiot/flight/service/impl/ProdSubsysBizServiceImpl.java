package com.swcares.aiot.flight.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.TenantConvertUtil;
import com.swcares.aiot.client.IConfConfigBizClient;
import com.swcares.aiot.core.common.cons.AdeCons;
import com.swcares.aiot.core.common.cons.FileExceptionCodeCons;
import com.swcares.aiot.core.common.cons.FlightCons;
import com.swcares.aiot.core.dto.ExternalProdSysExportDTO;
import com.swcares.aiot.core.dto.ExternalProdSysImportDTO;
import com.swcares.aiot.core.dto.FileImportRecordPageDTO;
import com.swcares.aiot.core.dto.ImportFlightInfoDto;
import com.swcares.aiot.core.entity.AdeFlightInfo;
import com.swcares.aiot.core.entity.BaseFileImportRecord;
import com.swcares.aiot.core.entity.BaseFlightInfo;
import com.swcares.aiot.core.enums.EnumBaseFileImportRecordType;
import com.swcares.aiot.core.enums.EnumDepartureArriveStatus;
import com.swcares.aiot.core.enums.EnumFileBusinessType;
import com.swcares.aiot.core.enums.EnumFlightArrvied;
import com.swcares.aiot.core.service.IBaseFileImportRecordService;
import com.swcares.aiot.core.vo.BaseFileImportRecordVO;
import com.swcares.aiot.core.vo.ImportProdSysVerifyResultVO;
import com.swcares.aiot.enums.DeletedEnum;
import com.swcares.aiot.file.client.AttachmentClient;
import com.swcares.aiot.file.vo.AttachmentVO;
import com.swcares.aiot.flight.cons.ExceptionCodeFlightImport;
import com.swcares.aiot.flight.cons.MatchTypeCons;
import com.swcares.aiot.flight.manager.IFlightManagerManService;
import com.swcares.aiot.flight.service.ProdSubsysBizService;
import com.swcares.aiot.flight.util.FileToMultipartFileUtil;
import com.swcares.aiot.service.*;
import com.swcares.aiot.service.impl.ExcelDataListenerForChunk;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.common.util.SpringUtils;
import com.swcares.baseframe.utils.lang.DateUtils;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.worm.hutool.IoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：FlightManagerServiceImpl <br>
 * Package：com.swcares.base.flight.api.service.impl <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description: 航班管理实现类         <br>
 *
 * <AUTHOR> <br>
 * date 2022年 06月17日 14:02 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class ProdSubsysBizServiceImpl implements ProdSubsysBizService {

    /**
     * Description : 错误信息——航线中有多个当前机场
     */
    private static final String MANY_CURRENT_AIRPORT_CODE = "航线中有多个当前机场";
    @Resource
    private AttachmentClient attachmentClient;
    @Resource
    private BaseFileImportRecordService baseFileImportRecordService;
    @Resource
    private AdeFlightInfoBizService adeFlightInfoBizService;
    @Resource
    private AdeFlightInfoService adeFlightInfoService;
    @Resource
    private BaseFlightInfoService baseFlightInfoService;
    @Resource
    private IBaseFileImportRecordService iBaseFileImportRecordService;
    @Resource
    private IFlightManagerManService flightManagerManService;
    @Resource
    private ProdSubsysBizService prodSubsysBizService;


    /**
     * Title：validateFileSuffix <br>
     * Description: 验证文件后缀<br>
     *
     * @param file 文件
     * @param allowedSuffixes 允许的文件后缀
     * @return boolean  true:验证通过 false: 验证不通过
     * author 周扬 <br>
     * date 2024/11/7 <br>
     */
    public static boolean validateFileSuffix(MultipartFile file, String[] allowedSuffixes) {
        String originalFileName = file.getOriginalFilename();
        if (originalFileName == null) {
            return false;
        }
        int lastIndex = originalFileName.lastIndexOf('.');
        if (lastIndex == -1) {
            return false;
        }
        String fileSuffix = originalFileName.substring(lastIndex);
        for (String allowedSuffix : allowedSuffixes) {
            if (fileSuffix.equalsIgnoreCase(allowedSuffix)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public ImportProdSysVerifyResultVO importFlightInfo(MultipartFile file, HttpServletResponse response) throws IOException {
        StopWatch stopWatch = new StopWatch();
        // 1.0文件格式验证
        checkSuffix(file);
        stopWatch.start("步骤1：保存整体文件 到 文件服务器");
        // 2.0 、保存整体文件 到 文件服务器
        AttachmentVO attachmentVO = this.upload(file);
        String taskId = String.valueOf(attachmentVO.getId());
        // 2.1 、保存文件记录
        BaseFileImportRecord baseFileImportRecord = saveImportRecord(attachmentVO, taskId);
        stopWatch.stop();
        stopWatch.start("步骤2：excel解析");
        //3.0 解析文件、将结果放入redis和文件服务器
        readEasyExcel(file, attachmentVO, taskId);
        stopWatch.stop();

        stopWatch.start("步骤3：将redis中存储的航班对象分组");
        // 3.0.返回文件唯一标识 给前端和文件的fileKey
        Long tenantId = TenantHolder.getTenant();
        List<ExternalProdSysImportDTO> importFlightInfoDataToRedis = flightManagerManService.getImportFlightInfoDataToRedis(tenantId, taskId);
        if (Objects.isNull(importFlightInfoDataToRedis)) {
            log.warn("redis中未获取上传的文件内容！ taskId = {}", taskId);
            throw new BusinessException(ExceptionCodeFlightImport.TASK_ID_ERROR);
        }
        stopWatch.stop();
        // 全量数据校验
        fullDataCheck(importFlightInfoDataToRedis, taskId);

        stopWatch.start("步骤4:生成返回对象并组织对象");
        List<ExternalProdSysImportDTO> successListFlight = new ArrayList<>();
        List<ExternalProdSysImportDTO> errorListFlight = new ArrayList<>();
        List<ExternalProdSysImportDTO> waitingListFlight = new ArrayList<>();
        List<ExternalProdSysImportDTO> existListFlight = new ArrayList<>();
        // 结果分组
        checkResultGroup(importFlightInfoDataToRedis, errorListFlight, successListFlight, waitingListFlight, existListFlight);
        stopWatch.stop();

        stopWatch.start("步骤5:上传错误文件");
        String errorFileName = attachmentVO.getOriginalName().replace(".", "_错误文件.");
        AttachmentVO errorAttachment = uploadErrorFile(errorListFlight, errorFileName, false);
        stopWatch.stop();

        // 更新上传信息到数据库中
        stopWatch.start("步骤6:更新数据库对象");
        String fileKey = errorAttachment != null ? String.valueOf(errorAttachment.getId()) : null;
        baseFileImportRecord.setErrorFileKey(fileKey)
                .setErrorFileContent(JSONUtil.toJsonStr(errorAttachment))
                .setTotalNumber(successListFlight.size() + errorListFlight.size())
                .setSuccessNumber(successListFlight.size())
                .setErrorNumber(errorListFlight.size())
                .setAddFlightNumber(waitingListFlight.size());
        baseFileImportRecordService.updateById(baseFileImportRecord);
        // 构建返回对象
        ImportProdSysVerifyResultVO importProdSysVerifyResultVO = buildImportProdSysVerifyResultVO(baseFileImportRecord, errorAttachment, existListFlight, taskId);
        stopWatch.stop();
        // 打印执行时间
        log.info(stopWatch.prettyPrint());

        return importProdSysVerifyResultVO;
    }

    /**
     * Title：buildImportProdSysVerifyResultVO <br>
     * Description: 构件返回对象 <br>
     *
     * @param baseFileImportRecord baseFileImportRecord
     * @param errorAttachment errorAttachment
     * @param existListFlight existListFlight
     * @param taskId taskId
     * @return com.swcares.aiot.core.vo.ImportProdSysVerifyResultVO
     * author 周扬 <br>
     * date 2024/11/13 <br>
     */
    private static @NotNull ImportProdSysVerifyResultVO buildImportProdSysVerifyResultVO(BaseFileImportRecord baseFileImportRecord, AttachmentVO errorAttachment, List<ExternalProdSysImportDTO> existListFlight, String taskId) {
        ImportProdSysVerifyResultVO importProdSysVerifyResultVO = ObjectUtils.copyBean(baseFileImportRecord, ImportProdSysVerifyResultVO.class);
        importProdSysVerifyResultVO.setErrorAttachmentVO(errorAttachment);
        importProdSysVerifyResultVO.setImportAttachmentVO(JSONUtil.toBean(baseFileImportRecord.getImportFileContent(), AttachmentVO.class));
        importProdSysVerifyResultVO.setExistNumber(existListFlight.size());
        importProdSysVerifyResultVO.setTaskId(taskId);
        return importProdSysVerifyResultVO;
    }


    /**
     * Title：checkRelaDbRule <br>
     * Description: checkRelaDbRule <br>
     *
     * @param importFlightInfoDataToRedis 航班数据
     * @param errorListFlight 验证不通过列表
     * @param successListFlight 验证通过列表
     * @param waitingListFlight 待添加数据
     * @param existListFlight 已存在数据
     * <p>
     * author 周扬 <br>
     * date 2024/11/8 <br>
     */
    private void checkResultGroup(List<ExternalProdSysImportDTO> importFlightInfoDataToRedis, List<ExternalProdSysImportDTO> errorListFlight, List<ExternalProdSysImportDTO> successListFlight, List<ExternalProdSysImportDTO> waitingListFlight, List<ExternalProdSysImportDTO> existListFlight) {
        for (ExternalProdSysImportDTO redisImportFlightInfoData : importFlightInfoDataToRedis) {
            // 错误数据
            if (ObjectUtils.isNotEmpty(redisImportFlightInfoData.getErrorReason())) {
                errorListFlight.add(redisImportFlightInfoData);
            }
            // 验证通过的数据
            if (ObjectUtils.isEmpty(redisImportFlightInfoData.getErrorReason())) {
                successListFlight.add(redisImportFlightInfoData);
            }
            // 待新增数据
            if (Boolean.TRUE.equals(redisImportFlightInfoData.getVerificationPass()) && MatchTypeCons.MATCH_TYPE_ADD.equals(redisImportFlightInfoData.getMatchingType())) {
                waitingListFlight.add(redisImportFlightInfoData);
            }
            // 已存在的数据
            if (Boolean.TRUE.equals(redisImportFlightInfoData.getVerificationPass()) && MatchTypeCons.MATCH_TYPE_EXIST.equals(redisImportFlightInfoData.getMatchingType())) {
                existListFlight.add(redisImportFlightInfoData);
            }
        }
    }



    /**
     * Title：uploadErrorFile <br>
     * Description: uploadErrorFile <br>
     *
     * @param listFlight 数据对象
     * @param fileName 文件名
     * @param isSuccessData 是否是生成成功数据文件
     * @return com.swcares.aiot.file.vo.AttachmentVO  文件上传信息, null 为 未生成错误文件
     * author 周扬 <br>
     * date 2024/11/9 <br>
     */
    private @Nullable AttachmentVO uploadErrorFile(@Nullable List<ExternalProdSysImportDTO> listFlight, String fileName, boolean isSuccessData) throws IOException {
        if (ObjectUtils.isEmpty(listFlight)) {
            return null;
        }
        AttachmentVO errorAttachment;
        // 设置文件路径和名称
        String filePath = File.separator + "opt" + File.separator + "file" + File.separator + "sign" + File.separator + "bill" + File.separator + "billBusData" + File.separator;
        String random = RandomUtil.randomNumbers(6);
        String outputDirectory = filePath.concat(LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtils.DEF_PTN_Y2MD_HMS)) + random);
        File fileDir = new File(outputDirectory);
        try {
            if (!fileDir.exists()) {
                boolean isSuccessFlag = fileDir.mkdirs();
                if (isSuccessFlag) {
                    log.error("文件创建失败，filePath = {}", outputDirectory);
                }
            }
            // 使用 EasyExcel 写入文件
            File errorFile = new File(filePath + fileName);
            if(isSuccessData){
                EasyExcelFactory.write(errorFile, ExternalProdSysExportDTO.class).sheet("sheet1").doWrite(listFlight);
            }else {
                EasyExcelFactory.write(errorFile, ExternalProdSysImportDTO.class).sheet("sheet1").doWrite(listFlight);
            }
            MultipartFile errorMultipartFile = FileToMultipartFileUtil.convertFileToMultipartFile(errorFile);
            // 上传文件
            errorAttachment = this.upload(errorMultipartFile);
        } finally {
            deleteLocalFiles(fileDir);
        }
        return errorAttachment;
    }

    /**
     * Title：readEasyExcel <br>
     * Description: 读取redis信息 <br>
     *
     * @param file 文件
     * @param attachmentVO 返回对象
     * @param taskId 任务Id
     */
    private void readEasyExcel(MultipartFile file, AttachmentVO attachmentVO, String taskId) {
        try {
            // 获取excel文件导入数量上限
            String excelRowMaxLimit = prodSubsysBizService.getImportLimitQuantity();
            InputStream inputStream = file.getInputStream();
            int totalRowCount = getExcelRowCount(inputStream);
            if (Integer.parseInt(excelRowMaxLimit) < totalRowCount) {
                log.warn("数据量超过最大限制，请检查数据。fileKey = {}, counter = {}", taskId, totalRowCount);
                throw new BusinessException(ExceptionCodeFlightImport.EXCEL_FILE_ROW_SURPASS_MAX_LIMIT, excelRowMaxLimit);
            }
            EasyExcelFactory.read(file.getInputStream(), ExternalProdSysImportDTO.class, new ExcelDataListenerForChunk(taskId)).sheet().doRead();
        } catch (ExcelAnalysisException excelAnalysisException) {
            if (excelAnalysisException.getCause() instanceof BusinessException) {
                throw (BusinessException) excelAnalysisException.getCause();
            } else {
                log.warn("excel解析异常！ attachmentVO = {}", attachmentVO, excelAnalysisException);
                throw new BusinessException(ExceptionCodeFlightImport.EXCEL_ANALYSIS_ERROR);
            }
        } catch (IOException e) {
            log.error("文件流获取失败! file = {}", attachmentVO, e);
            throw new BusinessException(ExceptionCodeFlightImport.FILE_IO_ERROR);
        }
    }

    /**
     * Title：getExcelRowCount <br>
     * Description: getExcelRowCount <br>
     *
     * @param inputStream 获取总行数
     * @return int
     * author 周扬 <br>
     * date 2024/11/19 <br>
     */
    private static int getExcelRowCount(InputStream inputStream) {
        // 使用EasyExcel简单读取并统计行数，这里可以根据实际情况优化获取行数的方式
        AtomicInteger rowCount = new AtomicInteger(0);

        EasyExcelFactory.read(inputStream, new AnalysisEventListener<Object>() {
            @Override
            public void invoke(Object data, AnalysisContext context) {
                rowCount.incrementAndGet();
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                log.info("document why this method is empty");
            }
        }).sheet().doRead();

        return rowCount.get();
    }

    private static void checkSuffix(MultipartFile file) {
        String[] allowedSuffixes = {".xlsx", ".xls"};
        boolean isValid = validateFileSuffix(file, allowedSuffixes);
        if (!isValid) {
            throw new BusinessException(ExceptionCodeFlightImport.FILE_SUFFIX_ERROR);
        }
    }


    public static void deleteLocalFiles(File directory) {
        try {
            FileUtils.deleteDirectory(directory);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * Title：saveImportRecord <br>
     * Description: 保存文件地址 <br>
     *
     * @param attachmentVO 文件信息
     * author 周扬 <br>
     * date 2024/11/7 <br>
     * @param taskId 任务id
     */
    private BaseFileImportRecord saveImportRecord(AttachmentVO attachmentVO, String taskId) {
        BaseFileImportRecord baseFileImportRecord = new BaseFileImportRecord();
        baseFileImportRecord.setId(Long.parseLong(taskId));
        baseFileImportRecord.setType(EnumBaseFileImportRecordType.PROD_SYS_FLIGHT_CMBT_DATA.getCode());
        baseFileImportRecord.setImportFileKey(String.valueOf(attachmentVO.getId()));
        baseFileImportRecord.setImportFileContent(JSONUtil.toJsonStr(attachmentVO));
        baseFileImportRecord.setDisplayed(Boolean.FALSE);
        baseFileImportRecord.setDeleted(Boolean.FALSE);
        LoginUserDetails user = UserContext.getCurrentUser();
        baseFileImportRecord.setCreatedBy(user.getUsername());
        baseFileImportRecord.setCreatedTime(LocalDateTime.now());
        baseFileImportRecord.setUpdatedTime(LocalDateTime.now());
        baseFileImportRecord.setImportUserName(user.getEmployeeName());
        baseFileImportRecordService.save(baseFileImportRecord);
        return baseFileImportRecord;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveImportFlightInfoData(ImportFlightInfoDto dto) {
        try {
            log.info("确认导入的航班数据Start,方法名(saveImportFlightInfoData)，dateTime= {}", DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_MS_PATTERN));
            // 根据taskId获取导入的航班数据
            List<ExternalProdSysImportDTO> improDataList = flightManagerManService.getImportFlightInfoDataToRedis(TenantHolder.getTenant(), dto.getTaskId());
            log.info("根据taskId获取导入数据，size= {}", improDataList.size());

            // 获取待新增航班数据
            List<ExternalProdSysImportDTO> toAddData = improDataList.stream().filter(im ->
                    im.getVerificationPass() != null && im.getVerificationPass() && FlightCons.MATCHING_TYPE_ADD.equals(im.getMatchingType())).collect(Collectors.toList());
            log.info("获取待新增航班数据，size= {}", toAddData.size());

            // 获取待新增ADE数据
            List<ExternalProdSysImportDTO> toAddAdeData = improDataList.stream().filter(e -> e.getVerificationPass() != null && e.getVerificationPass()).collect(Collectors.toList());
            log.info("获取待新增ADE数据，size= {}", toAddAdeData.size());

            // 导入航班转换成BaseFlightInfo
            List<BaseFlightInfo> toAddFlightInfo = importFlightInfoToBaseFlightInfo(toAddData);
            // 导入航班转换成AdeFlightInfo
            List<AdeFlightInfo> toAddAdeInfo = importFlightInfoToAdeFlightInfo(toAddAdeData);

            log.info("新增航班数据，dateTime= {}", DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_MS_PATTERN));
            baseFlightInfoService.saveBatch(toAddFlightInfo);

            log.info("新增客货邮行数数据，dateTime= {}", DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_MS_PATTERN));
            saveOrUpdateBatchs(toAddAdeInfo);

            log.info("调用数据拆分接口，dateTime= {}", DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_MS_PATTERN));
            adeFlightInfoBizService.startBaseFlightToCargoAndTraveler();

            log.info("调用数据计算接口，dateTime= {}", DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_MS_PATTERN));
            callCalculationModul(toAddAdeInfo);

            // 保存成功的导入记录
            saveSuccessFileContent(dto.getTaskId(), toAddAdeData);
            // 最后删除redisKey
            flightManagerManService.removeRedisByKey(dto.getTaskId());
        } catch (Exception e) {
            log.info("航班数据导入确认失败，失败原因=", e);
            throw new BusinessException(ExceptionCodeFlightImport.IMPORT_FLIGHT_INFO_ERROR);
        }
        log.info("确认导入的航班数据End,方法名(saveImportFlightInfoData)，dateTime= {}", DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_MS_PATTERN));
        return true;
    }


    private void callCalculationModul(List<AdeFlightInfo> adeflightInfoList) {
        adeFlightInfoBizService.updateBaseFlightCargoAndTraveler(null, AdeCons.IMPORT_TYPE, UserContext.getCurrentUser().getTenantId(), adeflightInfoList);
    }


    @Override
    public void verifyFlight(List<ExternalProdSysImportDTO> list) {
        StopWatch stopWatch = new StopWatch();
        // 无数据库验证
        stopWatch.start("无数据库验证");
        checkNoDbRela(list);
        stopWatch.stop();
        // db关联验证
        stopWatch.start("db关联验证");
        checkFlightInfo(list);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        for (ExternalProdSysImportDTO importDto : list) {
            // 错误数据
            if (ObjectUtils.isNotEmpty(importDto.getErrorReason())) {
                importDto.setVerificationPass(false);
            }
            // 验证通过的数据
            if (ObjectUtils.isEmpty(importDto.getErrorReason())) {
                importDto.setVerificationPass(true);
            }
        }

    }

    /**
     * Title：checkNoDbRela <br>
     * Description: checkNoDbRela <br>
     *
     * @param waitingCheckList 待验证的列表
     * return java.util.List<com.swcares.aiot.core.dto.ExternalProdSysImportDTO> 验证通过的列表
     * author 周扬 <br>
     * date 2024/11/11 <br>
     */
    public static void checkNoDbRela(List<ExternalProdSysImportDTO> waitingCheckList) {
        String tenantCode = TenantConvertUtil.getTenantCode(TenantHolder.getTenant());
        if (ObjectUtils.isEmpty(tenantCode)) {
            log.error("未获取到当前租户的编码！tenantId={}", TenantHolder.getTenant());
            throw new BusinessException(ExceptionCodeFlightImport.TENANT_NO_FIND_TENANT_CODE_ERROR);
        }
        try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
            // 验证格式和必填项
            for (ExternalProdSysImportDTO externalProdSysImportDTO : waitingCheckList) {
                Validator validator = factory.getValidator();
                Set<ConstraintViolation<ExternalProdSysImportDTO>> violations = validator.validate(externalProdSysImportDTO);
                for (ConstraintViolation<ExternalProdSysImportDTO> violation : violations) {
                    String errorMsg = ObjectUtils.isEmpty(externalProdSysImportDTO.getErrorReason()) ? "" : externalProdSysImportDTO.getErrorReason();
                    externalProdSysImportDTO.setErrorReason(errorMsg.concat(violation.getMessage()).concat(";"));
                }
                // 若导入的航班为航线中有两个租户机场（上海-南宁-徐州-北京-徐州-广州）
                checkManyCurrentAiport(externalProdSysImportDTO, tenantCode);
                // 设置验证是否通过 (使用错误原因进行判断)
                boolean isAccessFlag = CharSequenceUtil.isBlank(externalProdSysImportDTO.getErrorReason());
                externalProdSysImportDTO.setVerificationPass(isAccessFlag);

            }
        }
    }

    /**
     * Title：checkManyCurrentAiport <br>
     * Description: 若导入的航班为航线中有两个租户机场（上海-南宁-徐州-北京-徐州-广州） <br>
     *
     * @param externalProdSysImportDTO externalProdSysImportDTO
     * @param tenantCode 租户编码
     * author 周扬 <br>
     * date 2024/11/11 <br>
     */
    private static void checkManyCurrentAiport(ExternalProdSysImportDTO externalProdSysImportDTO, String tenantCode) {
        if (ObjectUtils.isEmpty(externalProdSysImportDTO) || ObjectUtils.isEmpty(externalProdSysImportDTO.getAirline())) {
            return;
        }
        String airline = externalProdSysImportDTO.getAirline();
        int countOccurrenceCount = countOccurrences(airline, tenantCode);
        if (countOccurrenceCount > 1) {
            String errorMsg = StringUtils.isEmpty(externalProdSysImportDTO.getErrorReason()) ? "" : externalProdSysImportDTO.getErrorReason();
            externalProdSysImportDTO.setErrorReason(errorMsg.concat(MANY_CURRENT_AIRPORT_CODE).concat(";"));
        }
    }

    /**
     * Title：countOccurrences <br>
     * Description: 统计字符串（subString）出现的次数 <br>
     *
     * @param mainString ：
     * @param subString :
     * @return int
     * author 周扬 <br>
     * date 2024/11/8 <br>
     */
    private static int countOccurrences(String mainString, String subString) {
        if (mainString.isEmpty() || subString.isEmpty()) {
            return 0;
        }

        int count = 0;
        int fromIndex = 0;

        while ((fromIndex = mainString.indexOf(subString, fromIndex)) != -1) {
            count++;
            fromIndex += subString.length();
        }

        return count;
    }


    private AttachmentVO upload(MultipartFile file) {
        //按业务类型建文件夹
        BaseResult<AttachmentVO> uploadResult = attachmentClient.upload(file, 0L, null,
                EnumFileBusinessType.DC_FLIGHT_PROD_SYS.name(),
                UserContext.getCurrentUserName());
        if (Objects.isNull(uploadResult) || Objects.isNull(uploadResult.getData()) ||
                //上传失败
                2 == uploadResult.getData().getFileStatus()) {
            throw new BusinessException(FileExceptionCodeCons.FILE_UPLOAD_FAILED);
        }
        return uploadResult.getData();
    }

    /**
     * Title：checkFlightInfo
     * Description：导入数据校验，赋值匹配结果(新增航班、系统已有航班)
     * author：李军呈
     * date： 2024/11/7 19:48
     * @param importDataList 导入数据集合
     */
    private void checkFlightInfo(List<ExternalProdSysImportDTO> importDataList) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("checkFlightInfo = 步骤1");
        if (ObjectUtils.isEmpty(importDataList)) {
            return;
        }
        List<LocalDate> flightDateList = importDataList.stream().filter(ExternalProdSysImportDTO::getVerificationPass).map(ExternalProdSysImportDTO::getFlightDate).collect(Collectors.toList());
        flightDateList = timeExtension(flightDateList).stream().distinct().collect(Collectors.toList());
        if (ObjectUtils.isEmpty(flightDateList)) {
            return;
        }
        stopWatch.stop();
        log.info("拿到去重后的航班日期，flightDateList= {}", flightDateList);
        stopWatch.start("checkFlightInfo=查询数据库");
        List<BaseFlightInfo> flightInfoList = listByFlightDate(flightDateList);
        stopWatch.stop();
        log.info("拿到系统存在的航班数据，size= {}", flightInfoList.size());
        stopWatch.start("checkFlightInfo = 步骤4");
        importDataList.forEach(flight -> {
            // 前面验证不通过的不在进行验证, qjsj为空的后面全量校验
            if (Boolean.FALSE.equals(flight.getVerificationPass()) || Objects.isNull(flight.getFlightTime())) {
                return;
            }
            // 获取需要对比的航班数据, 并且系统中已存在
            if (isSegmentData(flight)) {
                if (determineIncludeFlight(flight, flightInfoList)) {
                    flight.setMatchingType(FlightCons.MATCHING_TYPE_EXISTS);
                } else {
                    flight.setMatchingType(FlightCons.MATCHING_TYPE_ADD);
                    // 新增航班数据，根据五要素判断是否与系统的数据重复
                    fiveElementsFlightExists(flight, flightInfoList);
                }
            }
        });
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
    }

    /**
     * Title：fiveElementsFlightExists
     * Description：根据五要素判断是否与系统的航班重复
     * author：李军呈
     * date： 2024/11/8 10:44
     * @param importFlight 导入的航班
     * @param flightInfoList 系统中的航班
     */
    private void fiveElementsFlightExists(ExternalProdSysImportDTO importFlight, List<BaseFlightInfo> flightInfoList) {
        flightInfoList.forEach(flight -> {
            if (flight.getFlightNo().equals(importFlight.getAirlineCode() + importFlight.getFlightNo())
                    && flight.getFlightDate().equals(importFlight.getFlightDate())
                    && flight.getFlightSegment().equals(importFlight.getFlightSegment())
                    && flight.getIsArrv().equals(getIsArrv(importFlight.getIsArrv()))) {
                importFlight.setVerificationPass(Boolean.FALSE);
                importFlight.setErrorReason(FlightCons.FLIGHT_INFO_EXISTS);
            }
        });
    }

    /***
     * Title：fiveElementsImportFlightExists
     * Description：新增航班，根据五要素校验新增数据中是否有重复数据
     * author：李军呈
     * date： 2024/11/13 14:50
     * @param importDataList 导入的航班数据
     */
    private void fiveElementsImportFlightExists(List<ExternalProdSysImportDTO> importDataList) {
        // 拿到五要素值集合
        List<String> list = new ArrayList<>();
        importDataList.forEach(im ->{
            if(im.getVerificationPass() != null && im.getVerificationPass() && FlightCons.MATCHING_TYPE_ADD.equals(im.getMatchingType())){
                im.setFiveElementsUnique(
                        DateUtil.format(im.getFlightDate().atStartOfDay(), DateUtils.PTN_YMD)+FlightCons.SPLICING_IDENT+
                        im.getAirlineCode()+im.getFlightNo()+FlightCons.SPLICING_IDENT+
                        im.getFlightSegment()+FlightCons.SPLICING_IDENT+
                        im.getIsArrv());
                list.add(im.getFiveElementsUnique());
            }
        });
        // 拿到重复的五要素组合值
        List<String> duplicateData = findDuplicatesUsingMap(list);
        log.info("导入航班数据中存在重复数据，data= {}", duplicateData);
        importDataList.forEach(im ->{
            if(duplicateData.contains(im.getFiveElementsUnique())){
                im.setVerificationPass(Boolean.FALSE);
                im.setErrorReason(FlightCons.FLIGHT_DATA_REPEAT);
            }
        });
    }

    public static List<String> findDuplicatesUsingMap(List<String> list) {
        List<String> result = new ArrayList<>();
        Map<String, Integer> countMap = new HashMap<>();
        // 遍历列表，统计每个字符串出现的次数
        for (String str : list) {
            countMap.put(str, countMap.getOrDefault(str, 0) + 1);
        }
        // 遍历map，找出出现次数大于1的字符串
        for (Map.Entry<String, Integer> entry : countMap.entrySet()) {
            if (entry.getValue() > 1) {
                result.add(entry.getKey());
            }
        }
        return result;
    }

    /**
     * Title：timeExtension
     * Description：将集合中所有日期加前后一天
     * author：李军呈
     * date： 2024/11/7 17:21
     * @param dateList 日期集合
     * @return java.util.List<java.time.LocalDate>
     */
    private List<LocalDate> timeExtension(List<LocalDate> dateList) {
        List<LocalDate> newDates = new ArrayList<>();
        for (LocalDate date : dateList) {
            newDates.add(date.minusDays(1));
            newDates.add(date);
            newDates.add(date.plusDays(1));
        }
        return newDates;
    }

    private List<BaseFlightInfo> listByFlightDate(List<LocalDate> flightDateList) {
        return baseFlightInfoService.getBaseFlightInfoList(flightDateList);
    }

    /**
     * Title：isSegmentData
     * Description：是否是航班数据，排除拆分段数据
     * author：李军呈
     * date： 2024/11/7 18:20
     * @param dto 入参Dto
     * @return boolean
     */
    private boolean isSegmentData(ExternalProdSysImportDTO dto) {
        return dto.getAirline().contains(dto.getFlightSegment());
    }

    /**
     * Title：determineIncludeFlight
     * Description：判断导入的数据存在系统航班集合中
     * author：李军呈
     * date： 2024/11/7 19:06
     * @param importFlight 导入的航班数据
     * @param flightInfoList 系统中的航班集合
     * @return boolean
     */
    private boolean determineIncludeFlight(ExternalProdSysImportDTO importFlight, List<BaseFlightInfo> flightInfoList) {
        for (BaseFlightInfo flight : flightInfoList) {
            // 文件中的数据与系统中的航班校验唯一的规则：
            // 航班号(CYR&HBH)、航段（HD）、实际降落(起飞)时间（前后两小时内）(DATE&QJSJ)、进出港(IO)
            if (flight.getFlightNo().equals(importFlight.getAirlineCode() + importFlight.getFlightNo())
                    && flight.getFlightSegment().equals(importFlight.getFlightSegment())
                    && takeoffAndLandingTimeContain(flight, importFlight)
                    && flight.getIsArrv().equals(getIsArrv(importFlight.getIsArrv()))) {
                // 校验文件中的航线于系统中的航线是否一致
                if (!flight.getAirline().equals(importFlight.getAirline())) {
                    importFlight.setVerificationPass(Boolean.FALSE);
                    importFlight.setErrorReason(FlightCons.AIRLINE_DIFFERENT);
                }
                importFlight.setPlanFlightDate(flight.getFlightDate());
                return true;
            }
        }
        return false;
    }

    /**
     * Title：takeoffAndLandingTimeContain
     * Description：起飞降落时间是否包含导入的实际时间(前后两小时内)
     * author：李军呈
     * date： 2024/11/7 19:32
     * @param flightInfo 系统航班
     * @param importFlight 导入航班
     * @return boolean
     */
    private boolean takeoffAndLandingTimeContain(BaseFlightInfo flightInfo, ExternalProdSysImportDTO importFlight) {
        if(Objects.isNull(importFlight.getFlightDate()) || Objects.isNull(importFlight.getFlightTime())){
            return false;
        }
        // 将LocalDate和LocalTime拼接成flightDateTime
        LocalDateTime flightDateTime = LocalDateTime.of(importFlight.getFlightDate(), importFlight.getFlightTime());
        // 获取两小时后的时间
        LocalDateTime entTime = flightDateTime.plusHours(2);
        // 获取两小时前的时间
        LocalDateTime startTime = flightDateTime.minusHours(2);
        // 出港实际起飞、进港实际降落 时间对比
        return (FlightCons.DEPARTURE_TYPE.equals(importFlight.getIsArrv())
                && ObjectUtil.isNotEmpty(flightInfo.getRealTakeOffDatetime())
                && flightInfo.getRealTakeOffDatetime().isAfter(startTime)
                && flightInfo.getRealTakeOffDatetime().isBefore(entTime))
                || (FlightCons.ARRIVAL_TYPE.equals(importFlight.getIsArrv())
                && ObjectUtil.isNotEmpty(flightInfo.getRealLandingDatetime())
                && flightInfo.getRealLandingDatetime().isAfter(startTime)
                && flightInfo.getRealLandingDatetime().isBefore(entTime));
    }

    private String getIsArrv(String importIsArrv) {
        return importIsArrv.equals(FlightCons.DEPARTURE_TYPE) ? EnumFlightArrvied.OUT_POST.getValue() : EnumFlightArrvied.IS_ARRV.getValue();
    }

    /**
     * Title：importFlightInfoToBaseFlightInfo
     * Description：导入航班数据转换成BaseFlightInfo表数据
     * author：李军呈
     * date： 2024/11/7 20:47
     * @param importFlightInfoList 导入数据集合
     * @return java.util.List<com.swcares.aiot.core.entity.BaseFlightInfo>
     */
    private List<BaseFlightInfo> importFlightInfoToBaseFlightInfo(List<ExternalProdSysImportDTO> importFlightInfoList) {
        List<BaseFlightInfo> baseFlightInfoList = new ArrayList<>();
        importFlightInfoList.forEach(data -> {
            BaseFlightInfo flightInfo = new BaseFlightInfo();
            flightInfo.setAirlineCode(data.getAirlineCode());
            flightInfo.setFlightNo(data.getAirlineCode() + data.getFlightNo());
            flightInfo.setFlightDate(data.getFlightDate());
            flightInfo.setFlightDateTime(LocalDateTime.of(data.getFlightDate(), data.getFlightTime()));
            flightInfo.setDepartureAirportCode(data.getFlightSegment().substring(0, 3));
            flightInfo.setDestinationAirportCode(data.getFlightSegment().substring(data.getFlightSegment().length() - 3));
            flightInfo.setFlightSegment(data.getFlightSegment());
            flightInfo.setFlightSegmentProperty(FlightCons.FLIGHT_SEGMENT_PROPERTY);
            flightInfo.setAirline(data.getAirline());
            flightInfo.setFltmission(data.getFlightType());
            flightInfo.setTask(data.getFlightType());
            flightInfo.setStatus(EnumDepartureArriveStatus.ARR.getCode());
            flightInfo.setIsArrv(getIsArrv(data.getIsArrv()));
            flightInfo.setAircraftNo(data.getAircraftNo());
            flightInfo.setAircraftModel(data.getAircraftModel());
            if (FlightCons.ARRIVAL_TYPE.equals(data.getIsArrv())) {
                flightInfo.setPlanLandingDatetime(LocalDateTime.of(data.getFlightDate(), data.getFlightTime()));
                flightInfo.setPredictLandingDatetime(LocalDateTime.of(data.getFlightDate(), data.getFlightTime()));
                flightInfo.setRealLandingDatetime(LocalDateTime.of(data.getFlightDate(), data.getFlightTime()));
            }
            if (FlightCons.DEPARTURE_TYPE.equals(data.getIsArrv())) {
                flightInfo.setPlanTakeOffDatetime(LocalDateTime.of(data.getFlightDate(), data.getFlightTime()));
                flightInfo.setPredictTakeOffDatetime(LocalDateTime.of(data.getFlightDate(), data.getFlightTime()));
                flightInfo.setRealTakeOffDatetime(LocalDateTime.of(data.getFlightDate(), data.getFlightTime()));
            }
            flightInfo.setAirportCode(data.getAirportCode());
            flightInfo.setAcdmDeleted(DeletedEnum.NORMAL);
            flightInfo.setSyncUpdatedTime(LocalDateTime.now());
            flightInfo.setStatusType(Integer.valueOf(FlightCons.MATCHING_TYPE_EXISTS));
            baseFlightInfoList.add(flightInfo);
        });
        return baseFlightInfoList;
    }

    private List<AdeFlightInfo> importFlightInfoToAdeFlightInfo(List<ExternalProdSysImportDTO> importFlightInfoList) {
        List<AdeFlightInfo> adeFlightInfoList = new ArrayList<>();
        importFlightInfoList.forEach(data -> {
            AdeFlightInfo adeFlightInfo = new AdeFlightInfo();
            adeFlightInfo.setSource(AdeCons.IMPORT_TYPE);
            adeFlightInfo.setSourceId(Long.valueOf(AdeCons.CONSTANT_ZERO));
            adeFlightInfo.setOpTm(DateUtil.format(data.getFlightDate().atStartOfDay(), DateUtils.PTN_YMD_HMSS));
            adeFlightInfo.setCreatedBy(AdeCons.UPDATE_BY_CH);
            adeFlightInfo.setUpdatedBy(AdeCons.UPDATE_BY_CH);
            adeFlightInfo.setFltCode(data.getAirlineCode() + data.getFlightNo());
            adeFlightInfo.setFltDate(getFltDate(data));
            adeFlightInfo.setDeparture(data.getFlightSegment().substring(0, 3));
            adeFlightInfo.setDestination(data.getFlightSegment().substring(data.getFlightSegment().length() - 3));
            adeFlightInfo.setBaggagecount(String.valueOf(data.getBagNum()));
            adeFlightInfo.setBaggageweight(String.valueOf(data.getBag()));
            adeFlightInfo.setCargoweight(String.valueOf(data.getCargo()));
            adeFlightInfo.setMailweight(String.valueOf(data.getMail()));
            adeFlightInfo.setAPsrcount(String.valueOf(data.getAdultNum()));
            adeFlightInfo.setCPsrcount(String.valueOf(data.getChildNum()));
            adeFlightInfo.setIPsrcount(String.valueOf(data.getInfantNum()));
            adeFlightInfo.setBerthFPsrcount(AdeCons.CONSTANT_ZERO);
            adeFlightInfo.setBerthCPsrcount(AdeCons.CONSTANT_ZERO);
            adeFlightInfo.setBerthYPsrcount(AdeCons.CONSTANT_ZERO);
            adeFlightInfo.setCsadu(String.valueOf(data.getTransitAdultNum()));
            adeFlightInfo.setCschd(String.valueOf(data.getTransitChildNum()));
            adeFlightInfo.setCsift(String.valueOf(data.getTransitInfantNum()));
            adeFlightInfo.setAirportId(data.getAirportCode());
            adeFlightInfo.setRegno(data.getAircraftNo());
            adeFlightInfo.setTraBagCount(AdeCons.CONSTANT_ZERO);
            adeFlightInfo.setTraMailWeight(AdeCons.CONSTANT_ZERO);
            adeFlightInfo.setTraBagWeight(AdeCons.CONSTANT_ZERO);
            adeFlightInfo.setTraCargoWeight(AdeCons.CONSTANT_ZERO);
            adeFlightInfo.setLdmExtInfo(AdeCons.BAL_AND_EIC);
            adeFlightInfo.setTransferPayloadWeightSI(AdeCons.CONSTANT_ZERO);
            adeFlightInfoList.add(adeFlightInfo);
        });
        return adeFlightInfoList;
    }

    private String getFltDate(ExternalProdSysImportDTO dto) {
        return ObjectUtil.isNotEmpty(dto.getPlanFlightDate()) ?
                DateUtil.format(dto.getPlanFlightDate().atStartOfDay(), DateUtils.PTN_YMD) :
                DateUtil.format(dto.getFlightDate().atStartOfDay(), DateUtils.PTN_YMD);
    }

    private void saveSuccessFileContent(String taskId, List<ExternalProdSysImportDTO> improDataList) throws IOException {
        log.info("生成成功文件的数量，size= {}", improDataList.size());
        // 获取成功的数据
        // 生成excel文件 并上传
        AttachmentVO attachmentVO = uploadErrorFile(improDataList, "successFile.xlsx", true);
        String fileKey = attachmentVO != null ? String.valueOf(attachmentVO.getId()) : null;
        // 获取导入记录
        BaseFileImportRecord importRecord = iBaseFileImportRecordService.getById(taskId);
        importRecord.setDisplayed(Boolean.TRUE)
                .setSuccessFileContent(JSONUtil.toJsonStr(attachmentVO))
                .setSuccessFileKey(fileKey);
        // 更新导入记录
        iBaseFileImportRecordService.updateById(importRecord);
    }

    @Override
    public void templateDownload(HttpServletResponse response, String fullName) throws IOException {
        IoUtils.downloadXlsxFromResource(response, fullName);
    }

    /**
     * Title：flightQjsjAssignment
     * Description：QJSJ为空的航班，补填本字段值
     * author：李军呈
     * date： 2024/11/8 15:33
     * @param importDataList 导入数据集合
     */
    private void flightQjsjAssignment(List<ExternalProdSysImportDTO> importDataList) {
        // 只处理 LUM-CKG-YBP 进港类型QJSJ为空的数据
        importDataList.forEach(im -> {
            // 前面验证不通过的不在进行验证
            if (Boolean.FALSE.equals(im.getVerificationPass())) {
                return;
            }
            if (FlightCons.ARRIVAL_TYPE.equals(im.getIsArrv()) && ObjectUtil.isEmpty(im.getFlightTime())) {
                // 获取AC段的QJSJ 赋值给BC段
                List<ExternalProdSysImportDTO> importDto = importDataList.stream().filter(ExternalProdSysImportDTO::getVerificationPass).filter(
                        data -> data.getFlightDate().equals(im.getFlightDate())
                                && data.getAirlineCode().equals(im.getAirlineCode())
                                && data.getFlightNo().equals(im.getFlightNo())
                                && data.getAirline().equals(im.getAirline())
                                && data.getAircraftNo().equals(im.getAircraftNo())
                                && data.getIsArrv().equals(im.getIsArrv())
                                && ObjectUtil.isNotEmpty(data.getFlightTime())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(importDto)) {
                    im.setFlightTime(importDto.get(0).getFlightTime());
                } else {
                    im.setVerificationPass(Boolean.FALSE);
                    im.setErrorReason(FlightCons.QJSJ_IS_NULL);
                }
            }
        });
    }

    public void saveOrUpdateBatchs(List<AdeFlightInfo> list) {
        List<String> flightDateList = list.stream().map(AdeFlightInfo::getFltDate).distinct().collect(Collectors.toList());
        List<AdeFlightInfo> adeInfoList = adeFlightInfoBizService.listByFltDate(flightDateList);
        list.forEach(ade -> adeInfoList.forEach(ades -> {
            if (ades.getFltDate().equals(ade.getFltDate())
                    && ades.getFltCode().equals(ade.getFltCode())
                    && ades.getDeparture().equals(ade.getDeparture())
                    && ades.getDestination().equals(ade.getDestination())
                    && ades.getRegno().equals(ade.getRegno())) {
                ade.setId(ades.getId());
            }
        }));
        adeFlightInfoService.saveOrUpdateBatch(list);
    }

    @Resource
    private BaseFileImportRecordBizService baseFileImportRecordBizService;
    @Override
    public IPage<BaseFileImportRecordVO> pageFileImportRecord(FileImportRecordPageDTO dto) {
        IPage<BaseFileImportRecordVO> page = baseFileImportRecordBizService.page(dto);
        for (BaseFileImportRecordVO recordVO : page.getRecords()) {
            if (ObjectUtil.isNotEmpty(recordVO.getImportFileContent())) {
                AttachmentVO attachmentVO = JSONUtil.toBean(recordVO.getImportFileContent(), AttachmentVO.class);
                recordVO.setImportAttachmentVO(attachmentVO);
                recordVO.setFileName(attachmentVO.getOriginalName());
            }
            if (ObjectUtil.isNotEmpty(recordVO.getErrorFileContent())) {
                recordVO.setErrorAttachmentVO(JSONUtil.toBean(recordVO.getErrorFileContent(), AttachmentVO.class));
            }
            if (ObjectUtil.isNotEmpty(recordVO.getSuccessFileContent())) {
                recordVO.setSuccessAttachmentVO(JSONUtil.toBean(recordVO.getSuccessFileContent(), AttachmentVO.class));
            }
            recordVO.setImportFileContent(null);
            recordVO.setErrorFileContent(null);
            recordVO.setSuccessFileContent(null);
        }
        return page;
    }

    /**
     * Title：fullDataCheck
     * Description：校验redis中的全量数据
     * author：李军呈
     * date： 2024/11/14 15:46
     * @param importFlightInfoDataToRedis 从redis中获取的集合
     */
    private void fullDataCheck(List<ExternalProdSysImportDTO> importFlightInfoDataToRedis, String taskId){
        // QJSJ为空的航班，本字段补填值
        flightQjsjAssignment(importFlightInfoDataToRedis);
        log.info("fullDataCheck补填QJSJ为空的航班-结束，size= {}", importFlightInfoDataToRedis.size());

        // 校验起降时间为空的航班数据,只有在补填QJSJ后才能批量校验
        checkQjsjIsNullFlightInfo(importFlightInfoDataToRedis);
        log.info("校验起降时间为空的航班数据-结束，size= {}", importFlightInfoDataToRedis.size());

        // 新增航班，根据五要素校验新增数据中是否有重复数据
        fiveElementsImportFlightExists(importFlightInfoDataToRedis);
        log.info("fullDataCheck新增航班重复校验-结束，size= {}", importFlightInfoDataToRedis.size());

        // 将数据添加到redis
        flightManagerManService.appendRedisValue(importFlightInfoDataToRedis, taskId, Boolean.FALSE);
    }

    /**
     * Title：checkQjsjIsNullFlightInfo
     * Description：校验起降时间为空的航班数据
     * author：李军呈
     * date： 2024/11/15 9:56
     * @param importDataList 导入数据集合
     */
    private void checkQjsjIsNullFlightInfo(List<ExternalProdSysImportDTO> importDataList) {
        if (ObjectUtils.isEmpty(importDataList)) {
            return;
        }
        List<ExternalProdSysImportDTO> qjsjIsNullDataList = importDataList.stream().filter(im ->
                im.getVerificationPass() && Objects.isNull(im.getMatchingType()) && isSegmentData(im)).collect(Collectors.toList());
        log.info("拿到去QJSJ为空的航班数据，qjsjIsNullDataList= {}", qjsjIsNullDataList.size());
        List<LocalDate> flightDateList = qjsjIsNullDataList.stream().map(ExternalProdSysImportDTO::getFlightDate).collect(Collectors.toList());
        flightDateList = timeExtension(flightDateList).stream().distinct().collect(Collectors.toList());
        if (ObjectUtils.isEmpty(flightDateList)) {
            return;
        }
        log.info("拿到去重后的航班日期，flightDateList= {}", flightDateList);
        List<BaseFlightInfo> flightInfoList = listByFlightDate(flightDateList);
        log.info("拿到系统存在的航班数据，size= {}", flightInfoList.size());
        qjsjIsNullDataList.forEach(flight -> {
            // 获取需要对比的航班数据, 并且系统中已存在
            if (isSegmentData(flight)) {
                if (determineIncludeFlight(flight, flightInfoList)) {
                    flight.setMatchingType(FlightCons.MATCHING_TYPE_EXISTS);
                } else {
                    flight.setMatchingType(FlightCons.MATCHING_TYPE_ADD);
                    // 新增航班数据，根据五要素判断是否与系统的数据重复
                    fiveElementsFlightExists(flight, flightInfoList);
                }
            }
        });
    }

    @Override
    public String getImportLimitQuantity() {
        IConfConfigBizClient iConfConfigBizClient = SpringUtils.getBean(IConfConfigBizClient.class);
        BaseResult<JSONObject> chainConfig = iConfConfigBizClient.getConfig(FlightCons.BASIC_FLIGHT_SERVICES_IMPORT);
        if (ObjectUtil.isEmpty(chainConfig) || HttpStatus.HTTP_OK != chainConfig.getCode() || null == chainConfig.getData()) {
            log.error("getImportLimitQuantity, error = {}", chainConfig);
            throw new BusinessException(ExceptionCodeFlightImport.JSON_ANALYSIS_ERROR);
        }
        return chainConfig.getData().getString(FlightCons.BASIC_FLIGHT_SERVICES_IMPORT_NUMBER);
    }

}

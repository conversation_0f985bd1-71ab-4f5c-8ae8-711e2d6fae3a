package com.swcares.aiot.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ClassName：FlightDateTypeEnum
 * Description：航班日期类型枚举
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2024/8/16 9:57
 * Version v1.0
 */
@Getter
@AllArgsConstructor
public enum EnumFlightDateType {
    /**
     * 1. 航班日期
     */
    FLIGHT_DATE_TYPE("flightDate","航班日期"),
    /**
     * 2. 起降日期
     */
    TAKE_OFF_DATE_TYPE("flightTime", "起降日期");

    private final String value;
    private final String name;
}

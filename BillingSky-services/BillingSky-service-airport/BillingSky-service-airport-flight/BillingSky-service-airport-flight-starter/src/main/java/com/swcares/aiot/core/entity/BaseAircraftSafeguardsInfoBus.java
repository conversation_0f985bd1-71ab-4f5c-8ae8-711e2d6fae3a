package com.swcares.aiot.core.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.aiot.enums.DeletedEnum;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * ClassName：com.swcares.aiot.node.safeguards.entity.FsBusAircraftSafeguardsInfo <br>
 * Description：飞机保障信息表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="FsBusAircraftSafeguardsInfo", description="飞机保障信息表")
@TableName("base_aircraft_safeguards_info")
public class BaseAircraftSafeguardsInfoBus extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键Id")
    private Long id;

    @ApiModelProperty(value = "进港航班号id")
    private Long arriveFlightId;

    @ApiModelProperty(value = "出港航班号id")
    private Long takeOffFlightId;

    @ApiModelProperty(value = "进港航班号")
    private String arrivalFlightNo;

    @ApiModelProperty(value = "出港航班号")
    private String departureFlightNo;

    @ApiModelProperty(value = "航线")
    private String airLine;

    @ApiModelProperty(value = "保障状态")
    private String safeguardsStatusName;

    @ApiModelProperty(value = "进港状态")
    private String arrivalStatus;

    @ApiModelProperty(value = "出港状态")
    private String departureStatus;

    @ApiModelProperty(value = "数据是否完整")
    private Boolean isDataIntegrity;

    @ApiModelProperty(value = "是否过夜")
    private Boolean isStayOvernight;

    @ApiModelProperty(value = "机号")
    private String aircraftNo;

    @ApiModelProperty(value = "机型")
    private String aircraftModel;

    @ApiModelProperty(value = "机位")
    private String aircraftParking;

    @ApiModelProperty(value = "是否手动修改")
    private Boolean isManualAircraftParking;

    @ApiModelProperty(value = "登机口")
    private String gate;
    @ApiModelProperty(value = "行李转盘")
    private String baggageCarouselSerialNumber;

    @ApiModelProperty(value = "值机柜台")
    private String  checkInCounter;
    @ApiModelProperty(value = "计划到达时间")
    private LocalDateTime planLandingDatetime;

    @ApiModelProperty(value = "预计到达时间")
    private LocalDateTime predictLandingDatetime;

    @ApiModelProperty(value = "实际到达时间")
    private LocalDateTime realLandingDatetime;

    @ApiModelProperty(value = "到达时间（计算值）")
    private LocalDateTime landingDatetime;

    @ApiModelProperty(value = "计划起飞时间")
    private LocalDateTime planTakeOffDatetime;

    @ApiModelProperty(value = "预计出发时间")
    private LocalDateTime predictTakeOffDatetime;

    @ApiModelProperty(value = "实际出发时间")
    private LocalDateTime realTakeOffDatetime;

    @ApiModelProperty(value = "出发时间(计算值)")
    private LocalDateTime takeOffDatetime;

    @ApiModelProperty(value = "实际撤轮档时间")
    private LocalDateTime cobt;

    @ApiModelProperty(value = "目标撤轮挡时间")
    private LocalDateTime tobt;

    @ApiModelProperty(value = "计算起飞时间")
    private LocalDateTime ctot;

    @ApiModelProperty(value = "航班日期")
    private LocalDateTime flightDateTime;

    @ApiModelProperty(value = "保障状态")
    private String safeguardType;
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "删除标识", hidden = true)
    private DeletedEnum deleted;
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "部门ID", hidden = true)
    private Long deptId;

}

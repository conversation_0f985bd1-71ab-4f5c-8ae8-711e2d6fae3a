package com.swcares.aiot.core.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * ClassName：com.swcares.base.flight.api.dto.AircraftInformationDTO <br>
 * Description：数据设置 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2024-02-26 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "DataSettingDTO", description = "数据设置")
public class DataSettingDTO implements BaseDTO, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Description : 任务标识编码
     */
    @ApiModelProperty(value = "任务标识编码", example = "W/Z-UNKNOWN-PASSENGER,W/Z")
    private String taskIdentificationCode;

    /**
     * Description : 生效开始日期
     */
    @ApiModelProperty(value = "生效开始日期", example = "2024-05-30")
    @NotNull
    private LocalDate effectiveCommencementDate;


}

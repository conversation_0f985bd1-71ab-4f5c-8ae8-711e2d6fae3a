package com.swcares.aiot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aiot.core.entity.BaseFlightCargoSegment;

import java.util.List;

/**
 * ClassName：BaseFlightCargoSegmentService
 * Description：航班货邮行拆分数据表 服务类
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2024/10/16 11:39
 * Version v1.0
 */
public interface BaseFlightCargoSegmentService extends IService<BaseFlightCargoSegment> {

    /**
     * Title：deleteBatchBaseFlightIds <br>
     * Description：根据航班id批量删除货邮行拆分数据 <br>
     * author：李军呈 <br>
     * date：2024年03月14日 下午2:17:10 <br>
     */
    void deleteBatchBaseFlightIds(List<Long> baseFlightIdList);
}

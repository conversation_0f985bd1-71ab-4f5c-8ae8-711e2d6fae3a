package com.swcares.aiot.core.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.aiot.dto.CollectFlightInfoUmePagedDTO <br>
 * Description：机场行（SAE）航班数据(A-CDM) 分页数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-02-22 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="CollectFlightInfoUmePagedDTO", description="机场行（SAE）航班数据(A-CDM)")
public class FlightInfoUmePagedDTO extends PagedDTO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班起飞日期")
    private String deptFlightStartDate;

    @ApiModelProperty(value = "航班起飞日期")
    private String deptFlightEndDate;

    @ApiModelProperty(value = "航空公司二字码")
    private String airlineCode;

    @ApiModelProperty(value = "航班状态")
    private String status;

}

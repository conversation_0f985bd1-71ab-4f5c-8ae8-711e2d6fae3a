package com.swcares.aiot.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aiot.core.entity.BaseConfigInfo;
import com.swcares.aiot.mapper.BaseConfigInfoMapper;
import com.swcares.aiot.service.BaseConfigInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


/**
 * ClassName：com.swcares.base.flight.api.service.impl.BaseConfigInfoServiceImpl <br>
 * Description：基础配置信息表 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR>
 * date 2024/4/22 13:27
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class BaseConfigInfoServiceImpl extends ServiceImpl<BaseConfigInfoMapper, BaseConfigInfo> implements BaseConfigInfoService {


    @Override
    public BaseConfigInfo getByKey(String key) {
        LambdaQueryWrapper<BaseConfigInfo> queryWrapper = new QueryWrapper<BaseConfigInfo>().lambda()
                .eq(BaseConfigInfo::getKey, key)
                .eq(BaseConfigInfo::getDeleted, Boolean.FALSE);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<BaseConfigInfo> getByKeyList(List<String> keyList) {
        LambdaQueryWrapper<BaseConfigInfo> queryWrapper = new QueryWrapper<BaseConfigInfo>().lambda()
                .in(BaseConfigInfo::getKey, keyList)
                .eq(BaseConfigInfo::getDeleted, Boolean.FALSE);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public String getValueByKey(String key, String defaultValue) {
        LambdaQueryWrapper<BaseConfigInfo> queryWrapper = new QueryWrapper<BaseConfigInfo>().lambda()
                .eq(BaseConfigInfo::getKey, key)
                .eq(BaseConfigInfo::getDeleted, Boolean.FALSE);
        BaseConfigInfo baseConfigInfo = baseMapper.selectOne(queryWrapper);
        return Objects.isNull(baseConfigInfo) ? defaultValue : baseConfigInfo.getValue();
    }

    @Override
    public boolean updateByKey(String key, String value) {
        return createConfig(key, value);
    }

    @Override
    public boolean createConfig(String key, String value) {
        if (value == null) {
            value = "";
        }
        if (CharSequenceUtil.isBlank(key)) {
            return false;
        }
        BaseConfigInfo baseConfigInfo = checkKeyExists(key);
        if (Objects.isNull(baseConfigInfo)) {
            return save(new BaseConfigInfo().setValue(value).setKey(key));
        } else if (Boolean.TRUE.equals(baseConfigInfo.getDeleted())) {
            return updateById(baseConfigInfo.setDeleted(Boolean.FALSE).setValue(value));
        } else {
            LambdaUpdateWrapper<BaseConfigInfo> eq = Wrappers.lambdaUpdate(BaseConfigInfo.class)
                    .set(BaseConfigInfo::getValue, value)
                    .eq(BaseConfigInfo::getKey, key);
            return update(new BaseConfigInfo(), eq);
        }
    }

    private BaseConfigInfo checkKeyExists(String key) {
        LambdaQueryWrapper<BaseConfigInfo> queryWrapper = new QueryWrapper<BaseConfigInfo>().lambda()
                .eq(BaseConfigInfo::getKey, key);
        return baseMapper.selectOne(queryWrapper);
    }
}

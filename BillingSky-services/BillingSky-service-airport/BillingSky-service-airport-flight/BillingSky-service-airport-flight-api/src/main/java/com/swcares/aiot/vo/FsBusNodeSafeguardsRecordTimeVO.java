package com.swcares.aiot.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel(value="FsBusNodeSafeguardsRecordTimeVO", description="节点保障时间记录")
public class FsBusNodeSafeguardsRecordTimeVO implements Serializable {
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "预计保障时间")
    private LocalDateTime predictDatetime;

    @ApiModelProperty(value = "飞机保障表ID")
    private Long aircraftSafeguardsInfoId;

    @ApiModelProperty(value = "航前航后")
    private String safeguardsType;

    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    @ApiModelProperty(value = "节点Code")
    private String nodeCode;

    @ApiModelProperty(value = "实际保障时间")
    private LocalDateTime realDatetime;

    @ApiModelProperty(value = "保障人账号")
    private String safeguardAccount;

    @ApiModelProperty(value = "保障人ID")
    private String safeguardID;

    @ApiModelProperty(value = "部门ID")
    private String departmentId;

    @ApiModelProperty(value = "节点名称后缀")
    private String nodeNameSuffixNum;

    @ApiModelProperty(value = "是否靠桥")
    private Boolean aircraftFar;

    @ApiModelProperty(value = "系统廊桥属性")
    private String sysLoungeBridgeChannelCount;

    @ApiModelProperty(value = "廊桥属性")
    private String loungeBridgeChannelCount;

    @ApiModelProperty(value = "航前是否有数据")
    private Boolean isDataBeforeFlight;
}

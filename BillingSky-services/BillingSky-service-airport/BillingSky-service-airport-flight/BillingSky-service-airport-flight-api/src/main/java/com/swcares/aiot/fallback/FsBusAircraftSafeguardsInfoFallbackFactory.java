package com.swcares.aiot.fallback;


import com.esotericsoftware.minlog.Log;
import com.swcares.aiot.client.IFsBusAircraftSafeguardsInfoClient;
import com.swcares.aiot.cons.FlightBusinessExceptionCodeConstant;
import com.swcares.aiot.dto.*;
import com.swcares.aiot.vo.FlightSafeguardVO;
import com.swcares.aiot.vo.FlightSafeguardsInfoVO;
import com.swcares.aiot.vo.FsBusAircraftSafeguardsInfoVO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.exception.BusinessException;
import org.springframework.cloud.openfeign.FallbackFactory;

import java.util.List;
import java.util.Map;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：FsBusAircraftSafeguardsInfoFallBackFactory <br>
 * Package：com.swcares.aiot.flight.parking.feign.impl <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 03月08日 14:45 <br>
 * @version v1.0 <br>
 */
public class FsBusAircraftSafeguardsInfoFallbackFactory implements FallbackFactory<IFsBusAircraftSafeguardsInfoClient> {
    @Override
    public IFsBusAircraftSafeguardsInfoClient create(Throwable throwable) {
        return new IFsBusAircraftSafeguardsInfoClient() {

            @Override
            public BaseResult<FsBusAircraftSafeguardsInfoVO> get(Long id) {
                Log.error("远程调用失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);
            }

            @Override
            public BaseResult<List<FsBusAircraftSafeguardsInfoVO>> getByIdList(List<Long> idList) {
                Log.error("远程调用失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);

            }

            @Override
            public BaseResult<FsBusAircraftSafeguardsInfoVO> getSelectOne(Long id) {
                Log.error("远程调用失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);

            }


            @Override
            public BaseResult<List<FlightSafeguardVO>> safeguardsType(FlightSafeguardsInfoQueryDTO dto) {
                Log.error("远程调用失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);
            }

            @Override
            public BaseResult<BaseFlightDetalsDTO> selectDetails(Long id) {
                Log.error("远程调用失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);
            }

            @Override
            public PagedResult<List<FsBusAircraftSafeguardsInfoVO>> page1(BaseFlightInfoPagedDTO dto) {
                Log.error("远程调用失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);
            }

            @Override
            public BaseResult<Object> update(FsBusAircraftSafeguardsInfoDTO dto) {
                Log.error("远程调用失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);

            }

            @Override
            public BaseResult<Object> updateById(FsBusAircraftSafeguardsInfoDTO dto) {
                Log.error("远程调用失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);
            }

            @Override
            public BaseResult<List<FsBusAircraftSafeguardsInfoVO>> listByIds(List<Long> ids) {
                Log.error("远程调用失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);
            }

            @Override
            public BaseResult<Map<Long, AircraftDateDTO>> dateMapByIds(List<Long> ids) {
                Log.error("远程调用失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);
            }

            @Override
            public BaseResult<List<Long>> getId(String flightNum, String exeDate, String depAirport, String destinationAirport) {
                Log.error("远程调用失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);
            }

            @Override
            public BaseResult<Boolean> generateAircraftSafeguardList() {
                Log.error("远程调用（生成航班保障列表）失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);
            }

            @Override
            public BaseResult<AircraftSafeguardsInfoBusDTO> selectSafeguardsById(String id) {
                Log.error("远程调用失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);
            }

            @Override
            public BaseResult<List<FsBusAircraftSafeguardsInfoVO>> selectAll(BaseFlightInfoPagedDTO dto) {
                Log.error("远程调用失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);
            }

            @Override
            public BaseResult<List<FlightSafeguardsInfoVO>> selectSafeguardsType(FlightSafeguardsInfoQueryDTO dto) {
                Log.error("远程调用失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);
            }

            @Override
            public BaseResult<Object> updateIsStayOvernight(Long id) {
                Log.error("远程调用失败,请重试");
                throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);
            }
        };
    }
}

package com.swcares.aiot.enums;

/**
 * ClassName：com.swcares.aiot.node.safeguards.enums.DictTypeEnum <br>
 * Description：字典类型枚举类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-05-09 12:05:69 <br>
 * @version v1.0 <br>
 */
public enum DictTypeEnum {

         /**
         * Description : 记录类型 <br/>
         */
        FS_RECORD_TYPE("FS_RECORD_TYPE","记录类型"),
         /**
         * Description : 启动停用 <br/>
         */
        STATUS("STATUS","启动停用"),
         /**
         * Description : 进港离港状态 <br/>
         */
        DEPARTURE_ARRIVE_STATUS("departure_arrive_status","进港离港状态"),
         /**
         * Description : 保障类型 <br/>
         */
        SAFEGUARDS_TYPE("SAFEGUARDS_TYPE","保障类型"),
         /**
         * Description : 廊桥属性（限制） <br/>
         */
        LOUNGE_BRIDGE_ATRR("LOUNGE_BRIDGE_ATRR","廊桥属性（限制）"),
         /**
         * Description : 是否 <br/>
         */
        WHETHER("WHETHER","是否"),
         /**
         * Description : 廊桥属性 <br/>
         */
        LOUNGE_BRIDGE("lounge_bridge","廊桥属性"),
         /**
         * Description : 演练地点 <br/>
         */
        DRILL_SITE_DICT("drill_site_dict","演练地点"),
         /**
         * Description : 通知节点 <br/>
         */
        ADVICE_NODE("advice_node","通知节点"),
         /**
         * Description : 固定提醒类型 <br/>
         */
        REMIND_TYPE_DICT("remind_type_dict","固定提醒类型"),
         /**
         * Description : 事件类型字典 <br/>
         */
        EVENT_TYPE_DICT("event_type_dict","事件类型字典"),
         /**
         * Description : 机位类型字典 <br/>
         */
        FLIGHT_PARKING_FORMAT("flight_parking_format","机位类型字典"),
         /**
         * Description : 航站楼航班属性字典 <br/>
         */
        AIRPORT_TERMINAL_FORMAT("airport_terminal_format","航站楼航班属性字典"),
         /**
         * Description : 公安类型字典 <br/>
         */
        POLICE_FORMAT("police_format","公安类型字典"),
         /**
         * Description : 消防保障级别字典 <br/>
         */
        FIRE_CONTROL_FORMAT("fire_control_format","消防保障级别字典"),
         /**
         * Description : 消防站类型字典 <br/>
         */
        FIRE_STATION_FORMAT("fire_station_format","消防站类型字典"),
         /**
         * Description : 廊桥类型字典表 <br/>
         */
        BRIDGE_FORMAT("bridge_format","廊桥类型字典表"),
         /**
         * Description : 签单内容_数据格式 <br/>
         */
        BILL_ITEM_DATA_FORMAT("bill_item_data_format","签单内容_数据格式"),
         /**
         * Description : 签单类型 <br/>
         */
        BILL_TYPE("bill_type","签单类型"),
         /**
         * Description : 节点记录状态字典 <br/>
         */
        NODE_RECORD_STATUS_FORMAT("node_record_status_format","节点记录状态字典"),
        /**
         * Description : 操作方式 <br/>
         */
        NS_SOURCE_OF_OPERATING("NS_SOURCE_OF_OPERATING","操作方式");

    private String code;
    private String display;

    DictTypeEnum(String code, String display) {
        this.code = code;
        this.display = display;
    }

    public String getCode() {
        return code;
    }


    public String getDisplay() {
        return display;
    }






}

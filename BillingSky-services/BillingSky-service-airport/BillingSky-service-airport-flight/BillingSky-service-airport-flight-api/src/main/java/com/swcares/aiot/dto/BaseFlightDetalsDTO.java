package com.swcares.aiot.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：BaseFlightDetalsVO <br>
 * Package：com.swcares.base.common.vo <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 04月05日 19:34 <br>
 * @version v1.0 <br>
 */
@Data
public class BaseFlightDetalsDTO {

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private LocalDateTime flightDateTime;

    @ApiModelProperty(value = "航空公司两字码")
    private String airlineCode;

    @ApiModelProperty(value = "出发机场三字码")
    private String departureAirportCode;

    @ApiModelProperty(value = "到达机场三字码")
    private String destinationAirportCode;

    @ApiModelProperty(value = "计划到达时间")
    private LocalDateTime planLandingDatetime;

    @ApiModelProperty(value = "是到港")
    private Boolean isArrv;

    @Excel(name="实际到达时间")
    @ApiModelProperty(value = "实际到达时间")
    private LocalDateTime realLandingDatetime;

    @ApiModelProperty(value = "预计到达时间")
    private LocalDateTime predictLandingDatetime;

    @ApiModelProperty(value = "计划起飞时间")
    private LocalDateTime planTakeOffDatetime;

    @ApiModelProperty(value = "实际起飞时间")
    @Excel(name="实际到达时间")
    private LocalDateTime realTakeOffDatetime;

    @ApiModelProperty(value = "预计起飞时间")
    private LocalDateTime predictTakeOffDatetime;

    @ApiModelProperty(value = "航班状态")
    private String status;

    @ApiModelProperty(value = "机号")
    private String aircraftNo;

    @ApiModelProperty(value = "机型")
    private String aircraftModel;

    @ApiModelProperty(value = "机位")
    private String aircraftParkingPosition;

    @ApiModelProperty(value = "登机口")
    private String gate;

    @ApiModelProperty(value = "值机柜台")
    private String checkInCounter;

    @ApiModelProperty(value = "行李转盘序号")
    private String baggageCarouselSerialNumber;

    @ApiModelProperty(value = "前序航班")
    private BaseFlightDTO orderBeforeFlight;
}

package com.swcares.aiot.fallback;


import com.esotericsoftware.minlog.Log;
import com.swcares.aiot.client.IFlightInfoManagerClient;
import com.swcares.aiot.cons.FlightBusinessExceptionCodeConstant;
import com.swcares.baseframe.common.exception.BusinessException;
import org.springframework.cloud.openfeign.FallbackFactory;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：FsBaseFlightInfoFeignServiceBackFactory <br>
 * Package：com.swcares.aiot.sign.bill.feign.impl <br>
 * Copyright 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * date 2022年 10月13日 15:42 <br>
 * @version v1.0 <br>
 */
public class FlightInfoManagerFallbackFactory implements FallbackFactory<IFlightInfoManagerClient> {


    @Override
    public IFlightInfoManagerClient create(Throwable cause) {
        return (flightDate, flightNo, isArrv) -> {
            Log.error("远程调用失败,请重试");
            throw new BusinessException(FlightBusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);
        };
    }
}

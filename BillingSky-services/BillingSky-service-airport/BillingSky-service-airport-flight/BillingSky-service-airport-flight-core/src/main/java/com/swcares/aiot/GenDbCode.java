package com.swcares.aiot;

import com.worm.MybatisPlusGenProperties;
import com.worm.MybatisPlusGenUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class GenDbCode {
    public static void main(String[] args) {
        MybatisPlusGenProperties mybatisPlusGenProperties = new MybatisPlusGenProperties();
        // 开启自动生成
        mybatisPlusGenProperties.setEnable(true);
        // 输出目录
        mybatisPlusGenProperties.setOutputPath("./BillingSky-services/BillingSky-service-airport/BillingSky-service-airport-flight/BillingSky-service-airport-flight-core");
        // 数据库名
        mybatisPlusGenProperties.setDbName("bls_airport_flight_ybp");
        // 主机ip
        mybatisPlusGenProperties.setHost("*************");
        // 端口
        mybatisPlusGenProperties.setPort(32145);
        //
        mybatisPlusGenProperties.setUname("root");
        //
        mybatisPlusGenProperties.setPwd("Aiotsw@test");
        mybatisPlusGenProperties.setTablePrefix("");

        mybatisPlusGenProperties.setIncludeTableNames(Arrays.asList("base_flight_info_ext",""));
        MybatisPlusGenUtils.rebuild(mybatisPlusGenProperties);
    }
}

package com.swcares.aiot.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 导入记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("base_file_import_record")
@ApiModel(value="BaseFileImportRecord对象", description="导入记录表")
public class BaseFileImportRecord extends Model<BaseFileImportRecord> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "类型（1=客货邮行文件导入）")
    @TableField("type")
    private Integer type;

    @ApiModelProperty(value = "是否显示(1:显示，0:不显示)")
    @TableField("displayed")
    private Boolean displayed;

    @ApiModelProperty(value = "导入用户姓名")
    @TableField("import_user_name")
    private String importUserName;

    @ApiModelProperty(value = "总数量")
    @TableField("total_number")
    private Integer totalNumber;

    @ApiModelProperty(value = "成功数量")
    @TableField("success_number")
    private Integer successNumber;

    @ApiModelProperty(value = "错误数量")
    @TableField("error_number")
    private Integer errorNumber;

    @ApiModelProperty(value = "新增航班数量")
    @TableField("add_flight_number")
    private Integer addFlightNumber;

    @ApiModelProperty(value = "导入文件key")
    @TableField("import_file_key")
    private String importFileKey;

    @ApiModelProperty(value = "导入文件内容")
    @TableField("import_file_content")
    private String importFileContent;

    @ApiModelProperty(value = "错误文件key")
    @TableField("error_file_key")
    private String errorFileKey;

    @ApiModelProperty(value = "错误文件内容")
    @TableField("error_file_content")
    private String errorFileContent;

    @ApiModelProperty(value = "成功文件key")
    @TableField("success_file_key")
    private String successFileKey;

    @ApiModelProperty(value = "成功文件内容")
    @TableField("success_file_content")
    private String successFileContent;

    @ApiModelProperty(value = "扩展字段(JSON列)")
    @TableField("ext")
    private String ext;

    @ApiModelProperty(value = "删除标识（1：删除 0：正常）")
    @TableField(value = "deleted", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

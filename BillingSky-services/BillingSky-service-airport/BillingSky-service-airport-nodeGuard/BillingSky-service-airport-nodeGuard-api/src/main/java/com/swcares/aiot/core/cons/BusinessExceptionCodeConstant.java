package com.swcares.aiot.core.cons;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：BusinessExceptionCodeConstant <br>
 * Package：com.swcares.aiot.node.safeguards.common.cons <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 03月07日 21:35 <br>
 * @version v1.0 <br>
 */
public class BusinessExceptionCodeConstant {
    private BusinessExceptionCodeConstant() {

    }

    /**
     * Description : 数据重复 <br/>
     */

    public static final Integer REPEAT_DATA_CODE = concatAiotCommonPrefix("0000");

    /**
     * Description :  该数据已有其他数据正在使用中 <br/>
     */
    public static final Integer BE_USING_ERROR_CODE = concatAiotCommonPrefix("1000");


    public static Integer concatAiotCommonPrefix(String code) {

        return Integer.parseInt("30" + code);
    }

    /**
     * Description : 调用某服务请联系管理员 <br/>
     */
    public static final int CALLING_FLIGHT_PARKING_GET_ERROR = concatPrefix("0001");

    public static final int CREATE_ERROR = concatPrefix("4002001");

    public static final int UPDATE_ERROR = concatPrefix("4002002");

    public static final int QUERY_ERROR = concatPrefix("4002003");

    public static final int DELETE_ERROR = concatPrefix("4002004");

    public static final int ACCESS_SERVER_ERROR = concatPrefix("4002005");

    public static final int NODE_AFTER_IS_NULL = concatPrefix("4002006");

    public static final int NODE_BEFORE_IS_NULL = concatPrefix("4002008");

    public static final int GREAT_THAN_FIVE_DUP_NODE = concatPrefix("4002007");
    public static final int ACCESS_BILL_SIGN_SERVER_ERROR = concatPrefix("4100000");
    /**
     * Description：实例类不具备排序功能 <br>
     */
    public static final int ENTITY_HAS_NOT_SORT_NUM = concatPrefix("4100011");
    /**
     * Description：重复数据 <br>
     */
    public static final int DUP_DATA = concatPrefix("4100012");
    /**
     * Description：节点已经被关联 <br>
     */
    public static final int NODE_IS_REAL = concatPrefix("4100013");
    /**
     * Description：节点关联名称已存在 <br>
     */
    public static final int NODE_REAL_NAME_IS_EXIST = concatPrefix("4100014");
    /**
     * Description：节点关联关系已存在 <br>
     */
    public static final int NODE_REAL_IS_EXIST = concatPrefix("4100015");
    /**
     * Description:保障点已存在无法新增<br>
     */
    public static final int THE_SAFEGUARD_POINT_ALREADY_EXISTS_AND_CANNOT_BE_ADDED = concatPrefix("4100016");

    /**
     * Description:保障点已存在无法修改<br>
     */
    public static final int THE_SAFEGUARD_POINT_ALREADY_EXISTS_AND_CANNOT_BE_MODIFIED = concatPrefix("4100017");

    /**
     * Description:时间格式输入错误<br>
     */
    public static final int THE_TIME_FORMAT_IS_INCORRECT = concatPrefix("4100018");
    /**
     * Description : 航站楼编号出现重复 <br/>
     */
    public static final int AIRPORT_TERMINAL_ERROR_CODE = concatPrefix("5000001");

    /**
     * Description： 登机口编号重复<br>
     */
    public static final int BOARDING_GATE_ERROR_CODE = concatPrefix("5000002");
    /**
     * Description： 航站楼不存在<br>
     */
    public static final int AIRPORT_TERMINAL_NOT_DATA = concatPrefix("5000003");
    /**
     * Description： 此记录不存在<br>
     */
    public static final int DATA_IS_NULL = concatPrefix("5000004");
    /**
     * Description：启用状态禁止删除 <br>
     */
    public static final int ENABLE_STATE_DELETE_ERROR = concatPrefix("5000005");
    /**
     * Description：该登机口已绑定到机位，不可停用 <br>
     */
    public static final int NOT_DATA_DELETED = concatPrefix("5000006");
    /**
     * Description：行李转盘不存在 <br>
     */
    public static final int BAGGAGE_TURNTABLE_NOT_DATA = concatPrefix("5000007");
    /**
     * Description：行李转盘编号不能重复 <br>
     */
    public static final int BAGGAGE_TURNTABLENO_REPEAT = concatPrefix("5000008");
    /**
     * Description：修改失败，请检查该机位是否为廊桥机位 <br>
     */
    public static final int UPDATE_ERROR_IN_BRIDGE = concatPrefix("5000009");
    /**
     * Description：飞机已起飞，无法新增节点 <br>
     */
    public static final int FLIGHT_WAS_DEP = concatPrefix("5000011");
    /**
     * Description：节点已被保障标准引用 <br>
     */
    public static final int RULE_REAL = concatPrefix("5000012");
    /**
     * Description：节点已被签单引用<br>
     */
    public static final int BILL_REAL = concatPrefix("5000013");
    /**
     * Description：该行李转盘已绑定机位，不可停用<br>
     */
    public static final int PACKAGE_REAL = concatPrefix("5000014");
    /**
     * Description：启用状态禁止删除<br>
     */
    public static final int PACKAGE_OPEN = concatPrefix("5000015");
    /**
     * Description：廊桥编号重复<br>
     */
    public static final int CORRIDOR_BRIDGE_ERROR_CODE = concatPrefix("5000016");
    /**
     * Description：廊桥已绑定机位 不可停用<br>
     */
    public static final int CORRIDOR_BRIDGE_NOT_DELETED = concatPrefix("5000017");
    /**
     * Description：机构名称已存在无法新增<br>
     */
    public static final int THE_ORGANIZATION_NAME_ALREADY_EXISTS_AND_CANNOT_BE_ADDED = concatPrefix("5000018");
    /**
     * Description：机构名称已存在无法修改<br>
     */
    public static final int THE_ORGANIZATION_NAME_ALREADY_EXISTS_AND_CANNOT_BE_MODIFIED = concatPrefix("5000019");
    /**
     * Description：请传入正确的数据 <br>
     */
    public static final int DTO_DATA_ERROR = concatPrefix("5000020");
    /**
     * Description：设备类型重复 <br>
     */
    public static final int DEVICE_TYPE_REPEAT = concatPrefix("5000021");
    /**
     * Description：公安局名称重复无法新增<br>
     */
    public static final int PUNLIC_SECURITY_BUREAU_NAME_DUPLICATE_CANNOT_BE_ADDED = concatPrefix("5000022");
    /**
     * Description：公安局名称重复无法修改<br>
     */
    public static final int THE_PUBLIC_SECURITY_BUREAU_NAME_IS_DUPLICATE_AND_CANNOT_BE_MODIFIED = concatPrefix("5000023");

    /**
     * Description：机场油库/加油站 名称重复<br>
     */
    public static final int OIL_DEPOT_NAME_CODE_ERROR = concatPrefix("5000024");
    /**
     * Description：应急预案名称不能重复<br>
     */
    public static final int PLAN_NAME_REPEAT = concatPrefix("5000025");
    /**
     * Description：应急演练名称不能重复<br>
     */
    public static final int DRILL_NAME_REPEAT = concatPrefix("5000026");
    /**
     * Description：记录信息不存在<br>
     */
    public static final int RECORD_INFO_DOES_NOT_EXIST = concatPrefix("5000027");
    /**
     * Description：此帐号，无所属部门<br>
     */
    public static final int HAS_NOT_AUTHORITY = concatPrefix("5000028");
    /**
     * Description：航班保障状态处于保障完成,无法移除过夜标识<br>
     */
    public static final int THE_FLIGHT_GUARANTEE_STATUS_IS_COMPLETED_AND_THE_OVERNIGHT_SIGN_CANNOT_BE_REMOVER = concatPrefix("5000029");

    public static Integer concatPrefix(String code) {
        return Integer.parseInt("70" + code);
    }


}

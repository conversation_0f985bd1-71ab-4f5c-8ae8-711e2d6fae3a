package com.swcares.aiot.feign.impl;


import com.swcares.aiot.core.cons.BusinessExceptionCodeConstant;
import com.swcares.aiot.client.NodeSafeguardsFeign;
import org.springframework.stereotype.Component;

import com.esotericsoftware.minlog.Log;

import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.exception.BusinessException;

import lombok.extern.slf4j.Slf4j;


/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FlightParkingServiceImpl <br>
 * Package：com.swcares.aiot.node.safeguards.feign.impl <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 03月07日 19:12 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class NodeSafeguardsFeignFallBack implements NodeSafeguardsFeign {

    @Override
    public BaseResult<Boolean> checkSafeguard(String id) {
        Log.error("远程调用失败,请重试! id = {}", id);
        throw new BusinessException(BusinessExceptionCodeConstant.CALLING_FLIGHT_PARKING_GET_ERROR);
    }
}

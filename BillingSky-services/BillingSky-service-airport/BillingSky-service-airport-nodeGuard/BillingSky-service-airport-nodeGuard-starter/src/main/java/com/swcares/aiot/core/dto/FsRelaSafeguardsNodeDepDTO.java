package com.swcares.aiot.core.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName：com.swcares.aiot.node.safeguards.dto.FsRelaSafeguardsNodeDepDTO <br>
 * Description：保障节点和部门关联表（控制记录权限） 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FsRelaSafeguardsNodeDepDTO", description="保障节点和部门关联表（控制记录权限）")
public class FsRelaSafeguardsNodeDepDTO  implements BaseDTO, Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "节点ID")
    private Long baseSafeguardsNodeId;

}

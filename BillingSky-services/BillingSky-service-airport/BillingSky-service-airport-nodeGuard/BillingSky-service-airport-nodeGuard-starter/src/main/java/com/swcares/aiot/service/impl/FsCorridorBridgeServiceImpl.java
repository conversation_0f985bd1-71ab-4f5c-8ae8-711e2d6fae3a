package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aiot.client.IFlightParkingClient;
import com.swcares.aiot.mapper.FsCorridorBridgeMapper;
import com.swcares.aiot.core.cons.SignBusinessExceptionCodeConstant;
import com.swcares.aiot.dto.FlightParkingQueryDTO;
import com.swcares.aiot.vo.FlightParkingVO;
import com.swcares.aiot.core.dto.FsCorridorBridgeDTO;
import com.swcares.aiot.core.dto.FsCorridorBridgePagedDTO;
import com.swcares.aiot.core.dto.FsCorridorBridgeQueryDTO;
import com.swcares.aiot.core.entity.FsCorridorBridge;
import com.swcares.aiot.core.vo.BridgeTypeVO;
import com.swcares.aiot.core.vo.FsCorridorBridgeVO;
import com.swcares.aiot.core.vo.TerminalNoVO;
import com.swcares.aiot.service.FsCorridorBridgeService;
import com.swcares.aiot.utils.QueryWrapperUtil;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.swcares.aiot.utils.AssertUtil.assertNull;

/**
 * ClassName：com.swcares.aiot.node.safeguards.service.impl.FsCorridorBridgeServiceImpl <br>
 * Description：廊桥表 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Service
public class FsCorridorBridgeServiceImpl extends ServiceImpl<FsCorridorBridgeMapper, FsCorridorBridge> implements FsCorridorBridgeService {

    @Resource
    private FsCorridorBridgeMapper fsCorridorBridgeMapper;

    @Resource
    private IFlightParkingClient IFlightParkingClient;


    @Override
    public void logicRemoveById(Long id) {
        fsCorridorBridgeMapper.deleteBridge(id);
    }

    @Override
    public List<FsCorridorBridgeVO> selectInfo(String bridge) {
        return fsCorridorBridgeMapper.selectInformation(bridge);
    }

    @Override
    public IPage<FsCorridorBridgeVO> page(FsCorridorBridgePagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

    @Override
    public List<FsCorridorBridgeVO> list(FsCorridorBridgeQueryDTO dto) {
        return baseMapper.list(dto);
    }

    @Override
    public List<FsCorridorBridgeVO> selectAll() {
        QueryWrapper<FsCorridorBridge> wrapper = new QueryWrapper<>();
        wrapper.eq("deleted", 0).and(fsCorridorBridgeQueryWrapper -> fsCorridorBridgeQueryWrapper.eq("is_valid", true));
        List<FsCorridorBridge> fsCorridorBridges = baseMapper.selectList(wrapper);
        return fsCorridorBridges.stream().map(fsCorridorBridge -> ObjectUtils.copyBean(fsCorridorBridge, FsCorridorBridgeVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<Integer, String>> getTerminalData(String terminalNo) {
        return fsCorridorBridgeMapper.selectData(terminalNo);
    }

    @Override
    public FsCorridorBridgeVO selectById(Long id) {
        return fsCorridorBridgeMapper.listById(id);
    }

    @Override
    public List<BridgeTypeVO> getBridgeType(String bridgeType) {
        return fsCorridorBridgeMapper.getBridgeType(bridgeType);
    }

    @Override
    public List<TerminalNoVO> getTerminalNo(String terminalNo) {
        return fsCorridorBridgeMapper.getTerminalNo(terminalNo);
    }

    @Override
    public void modifyStatus(Long id, Boolean isValid) {
        //校验数据
        QueryWrapper<FsCorridorBridge> wrapper = new QueryWrapper<>();
        wrapper.eq("deleted", 0).and(fsCorridorBridgeQueryWrapper -> fsCorridorBridgeQueryWrapper.eq("id", id));
        FsCorridorBridge fsCorridorBridge = baseMapper.selectOne(wrapper);
        if (ObjectUtils.isEmpty(fsCorridorBridge)) {
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        if (!isValid) {
            FlightParkingQueryDTO dto = new FlightParkingQueryDTO();
            dto.setFkCorridorBridge(id);
            BaseResult<List<FlightParkingVO>> parkingList = IFlightParkingClient.getParkingList(dto);
            if (CollectionUtil.isNotEmpty(parkingList.getData())) {
                throw new BusinessException(SignBusinessExceptionCodeConstant.CORRIDOR_BRIDGE_NOT_DELETED);
            }
        }
        fsCorridorBridge.setIsValid(isValid);
        int i = baseMapper.updateById(fsCorridorBridge);
        if (i < 1) {
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
    }

    @Override
    public void updateCorridorBridge(FsCorridorBridgeDTO dto) {
        FsCorridorBridge newFsCorridorBridge = ObjectUtils.copyBean(dto, FsCorridorBridge.class);
        //校验数据
        FsCorridorBridge fsCorridorBridge = baseMapper.selectById(dto.getId());
        if (ObjectUtils.isEmpty(fsCorridorBridge)) {
            throw new BusinessException(SignBusinessExceptionCodeConstant.DATA_IS_NULL);
        }
        if (!fsCorridorBridge.getBridgeNo().equals(newFsCorridorBridge.getBridgeNo())) {
            QueryWrapper<FsCorridorBridge> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("deleted", 0).and(fsCorridorBridgeQueryWrapper -> fsCorridorBridgeQueryWrapper.eq("bridge_no", dto.getBridgeNo()));
            List<FsCorridorBridge> fsCorridorBridges = baseMapper.selectList(queryWrapper);
            if (CollectionUtil.isNotEmpty(fsCorridorBridges)) {
                throw new BusinessException(SignBusinessExceptionCodeConstant.CORRIDOR_BRIDGE_ERROR_CODE);
            }
        }
        int i = baseMapper.updateById(newFsCorridorBridge);
        if (i < 1) {
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }

    }

    @Override
    public void saveCorridorBridge(FsCorridorBridgeDTO dto) {
        FsCorridorBridge fsCorridorBridge = ObjectUtils.copyBean(dto, FsCorridorBridge.class);
        //校验数据
        QueryWrapper<FsCorridorBridge> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bridge_no", fsCorridorBridge.getBridgeNo()).and(fsCorridorBridgeQueryWrapper -> fsCorridorBridgeQueryWrapper.eq("deleted", 0));
        List<FsCorridorBridge> fsCorridorBridges = baseMapper.selectList(queryWrapper);
        if (ObjectUtils.isNotEmpty(fsCorridorBridges)) {
            throw new BusinessException(SignBusinessExceptionCodeConstant.CORRIDOR_BRIDGE_ERROR_CODE);
        }
        int insert = baseMapper.insert(fsCorridorBridge);
        if (insert < 1) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
    }


    @Override
    public boolean saveNoRepeat(FsCorridorBridge entity) {
        QueryWrapper<FsCorridorBridge> queryWrapper = QueryWrapperUtil.createQueryWrapperAddColumnDel();
        //添加去重复条件
        List<FsCorridorBridge> repeatList = baseMapper.selectList(queryWrapper);
        assertNull(repeatList, SignBusinessExceptionCodeConstant.SIGN_REPEAT_DATA_CODE);
        return save(entity);
    }
}

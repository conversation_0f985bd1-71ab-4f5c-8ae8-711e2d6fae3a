package com.swcares.aiot.core.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.swcares.aiot.enums.DeletedEnum;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * ClassName：com.swcares.aiot.node.safeguards.entity.FsAirportPolice <br>
 * Description：机场公安表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="FsAirportPolice", description="机场公安表")
public class FsAirportPolice extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公安名称")
    @Size(min=1,max=60)
    @NotNull
    private String policeName;

    @ApiModelProperty(value = "公安类型")
    private String policeType;

    @ApiModelProperty(value = "机场三字码")
    @Size(min=1,max=6)
    @NotNull
    private String airportCode;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "删除标识", hidden = true)
    private DeletedEnum deleted;
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "部门ID", hidden = true)
    private Long deptId;

}

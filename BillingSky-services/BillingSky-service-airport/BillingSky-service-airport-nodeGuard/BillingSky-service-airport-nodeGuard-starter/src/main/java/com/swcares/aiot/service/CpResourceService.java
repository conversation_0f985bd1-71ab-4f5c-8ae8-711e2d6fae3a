package com.swcares.aiot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aiot.core.dto.CpResourcePagedDTO;
import com.swcares.aiot.core.dto.CpResourceQueryDTO;
import com.swcares.aiot.core.entity.CpResource;
import com.swcares.aiot.core.vo.CpResourceVO;

import java.util.List;

/**
 * ClassName：com.swcares.aiot.node.safeguards.service.CpResourceService <br>
 * Description：资源信息 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
public interface CpResourceService extends IService<CpResource> {

    /**
     * Title：logicRemoveById <br>
     * Description：逻辑删除 <br>
     * author：code-generator <br>
     * date：2022-03-02 <br>
     *
     * @param id 主键id<br>
     * @return <br>
     */
    boolean logicRemoveById(Long id);

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：code-generator <br>
     * date：2022-03-02 <br>
     *
     * @param dto 查询对象<br>
     * @return <br>
     */
    IPage<CpResourceVO> page(CpResourcePagedDTO dto);

    /**
     * Title:  saveNoRepeat<br>
     * Description: 保存不重复的数据 <br>
     * author 周扬
     * date：2022-03-02 <br>
     *
     * @param entity 保存对象<br>
     * @return boolean  <br>
     */
    boolean saveNoRepeat(CpResource entity);


    /**
     * Title：page <br>
     * Description：资源信息_列表查询 <br>
     * author：code-generator <br>
     * date：2022-03-02 <br>
     *
     * @param dto 查询对象<br>
     * @return <br>
     */
    List<CpResourceVO> list(CpResourceQueryDTO dto);
}

package com.swcares.aiot.core.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Future;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * ClassName：com.swcares.aiot.node.safeguards.dto.FsEmergencyDrillDTO <br>
 * Description：应急演练表 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FsEmergencyDrillDTO", description="应急演练表")
public class FsEmergencyDrillDTO  implements BaseDTO, Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @Length(min=1,max=90)
    @ApiModelProperty(value = "演练名称")
    private String drillName;

    @Length(min=1,max=90)
    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @Future
    @ApiModelProperty(value = "演练时间")
    private LocalDateTime drillTime;

    @Length(min=1,max=32)
    @ApiModelProperty(value = "演练地点")
    private String drillSite;

    @Length(min=1,max = 255)
    @ApiModelProperty(value = "参演人员")
    private String participants;

}

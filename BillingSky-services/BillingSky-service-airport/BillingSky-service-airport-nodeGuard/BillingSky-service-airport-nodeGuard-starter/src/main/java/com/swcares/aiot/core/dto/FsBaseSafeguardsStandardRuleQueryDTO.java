package com.swcares.aiot.core.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * ClassName：com.swcares.aiot.node.safeguards.dto.FsBaseSafeguardsStandardRuleQueryDTO <br>
 * Description：保障标准表 查询_数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-04 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FsBaseSafeguardsStandardRuleQueryDTO", description="保障标准表")
public class FsBaseSafeguardsStandardRuleQueryDTO  implements BaseDTO, Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "保障节点ID")
    private Long safeguardsNodeId;

    @ApiModelProperty(value = "参照节点ID")
    private String referNodeId;

    @ApiModelProperty(value = "标准时长（分钟）")
    private Integer standardTime;

    @ApiModelProperty(value = "是否为内置参照点")
    private Boolean isSysReferPoint;

}

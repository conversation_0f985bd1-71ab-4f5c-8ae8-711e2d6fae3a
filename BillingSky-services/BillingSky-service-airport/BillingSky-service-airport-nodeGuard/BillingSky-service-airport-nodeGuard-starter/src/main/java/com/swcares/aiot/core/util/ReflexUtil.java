package com.swcares.aiot.core.util;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> <br>
 * date 2022/3/11 16:44 <br>
 * @version v1.0.0 <br>
 * ClassName : ReflexUtil
 */
@Slf4j
@UtilityClass
public class ReflexUtil {


    /**
     * Title：getFieldList <br>
     * Description： 获取属性列表，包括父类<br>
     * author：liuyanling <br>
     * date：2021年6月3日 下午5:58:38 <br>
     *
     * @param cls :
     * @return <br>
     */
    private static List<Field> getFieldList(Class<?> cls) {
        List<Field> fieldsList = new ArrayList<>();
        while (cls != null) {
            fieldsList.addAll(Arrays.asList(cls.getDeclaredFields()));
            cls = cls.getSuperclass();
        }
        return fieldsList;
    }

    /**
     * Title：hasField <br>
     * Description： 是否含有某个属性字段<br>
     * author：liuyanling <br>
     * date：2021年6月30日 下午4:14:57 <br>
     *
     * @param c         :
     * @param fieldName :
     * @return <br>
     */
    @SuppressWarnings("rawtypes")
    public static boolean hasField(Class c, String fieldName) {
        List<Field> fieldsList = getFieldList(c);
        if (!ObjectUtils.isEmpty(fieldName) && !CollectionUtils.isEmpty(fieldsList)) {
            for (Field f : fieldsList) {
                if (fieldName.equals(f.getName())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Title：getFieldValueByFieldName <br>
     * Description： 反射根据属性名获取属性值<br>
     * author：liuyanling <br>
     * date：2021年6月3日 下午5:58:38 <br>
     *
     * @param obj       :
     * @param fieldName :
     * @return <br>
     */
    public static Object getFieldValueByFieldName(Object obj, String fieldName) {
        try {
            List<Field> fieldsList = getFieldList(obj.getClass());
            if (!CollectionUtils.isEmpty(fieldsList)) {
                for (Field field : fieldsList) {
                    // 设置对象的访问权限，保证对private的属性的访问
                    field.setAccessible(true);
                    if (field.getName().equals(fieldName)) {
                        return field.get(obj);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * Title：setFieldValueByFieldName <br>
     * Description： 通过反射设置属性的值<br>
     * author：liuyanling <br>
     * date：2021年6月3日 下午4:09:13 <br>
     *
     * @param obj        实体类对象
     * @param fieldName  属性名
     * @param fieldValue 属性值<br>
     */
    public static void setFieldValueByFieldName(Object obj, String fieldName, Object fieldValue) {
        try {
            List<Field> fieldsList = getFieldList(obj.getClass());
            if (!CollectionUtils.isEmpty(fieldsList)) {
                for (Field field : fieldsList) {
                    if (field.getName().equals(fieldName)) {
                        field.setAccessible(true);
                        field.set(obj, fieldValue);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * Title：checkObjAllFieldsIsNull <br>
     * Description：判断对象中属性值是否全为空（忽略基本类型字段）<br>
     * author：liuyanling <br>
     * date：2021年6月30日 下午1:52:32 <br>
     *
     * @param obj             :
     * @param ignoreFieldName 忽略字段名称；以“,”开头、结尾和分隔
     * @return <br>
     */
    public static boolean checkObjAllFieldsIsNull(Object obj, String ignoreFieldName) {
        return checkObjAllFieldsIsNull(obj, true, ignoreFieldName);
    }

    /**
     * Title：checkObjAllFieldsIsNull <br>
     * Description：判断对象中属性值是否全为空；如存在基本数据类型，会影响判断 <br>
     * author：liuyanling <br>
     * date：2021年6月30日 下午1:52:32 <br>
     *
     * @param obj                 :
     * @param ignorePrimitiveType 是否忽略基本类型
     * @param ignoreFieldName     忽略字段名称；以“,”开头、结尾和分隔
     * @return <br>
     */
    public static boolean checkObjAllFieldsIsNull(Object obj, boolean ignorePrimitiveType, String ignoreFieldName) {
        if (null == obj) {
            return true;
        }
        try {
            List<Field> fieldsList = getFieldList(obj.getClass());
            if (!CollectionUtils.isEmpty(fieldsList)) {
                for (Field f : fieldsList) {
                    f.setAccessible(true);

                    if ((ignorePrimitiveType && f.getType().isPrimitive()) || (!ObjectUtils.isEmpty(ignoreFieldName)
                            && ignoreFieldName.contains("," + f.getName() + ","))) {
                        continue;
                    }
                    if (null != f.get(obj) && !ObjectUtils.isEmpty(f.get(obj).toString())) {
                        return false;
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return true;
    }
}

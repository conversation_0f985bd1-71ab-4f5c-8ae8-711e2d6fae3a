package com.swcares.aiot.core.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.swcares.aiot.enums.DeletedEnum;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.aiot.node.safeguards.entity.CpResource <br>
 * Description：资源信息 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="CpResource", description="资源信息")
public class CpResource extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "资源名称")
    private String resourceName;

    @ApiModelProperty(value = "资源地址")
    private String resourceUrl;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "删除标识", hidden = true)
    private DeletedEnum deleted;
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "部门ID", hidden = true)
    private Long deptId;

}

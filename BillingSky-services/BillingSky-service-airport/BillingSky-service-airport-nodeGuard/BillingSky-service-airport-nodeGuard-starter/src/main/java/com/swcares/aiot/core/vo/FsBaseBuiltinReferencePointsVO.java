package com.swcares.aiot.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
/**
 * ClassName：com.swcares.aiot.node.safeguards.vo.FsBaseBuiltinReferencePointsVO <br>
 * Description：系统内置参照点返回展示对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-04 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FsBaseBuiltinReferencePointsVO", description="系统内置参照点")
public class FsBaseBuiltinReferencePointsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;


    @ApiModelProperty(value = "参照点名称")
    private String referencePointName;

    @ApiModelProperty(value = "关联表名称")
    private String relevanceTableName;

    @ApiModelProperty(value = "关联表字段名")
    private String relevanceCloumnName;

    @ApiModelProperty(value = "排序序号")
    private Integer sortNum;



}

package com.swcares.aiot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aiot.core.dto.FlightAndAirParkingDTO;
import com.swcares.aiot.core.dto.FsBusNodeSafeguardsRecordDTO;
import com.swcares.aiot.core.dto.FsBusNodeSafeguardsRecordPagedDTO;
import com.swcares.aiot.core.dto.FsBusNodeSafeguardsRecordQueryDTO;
import com.swcares.aiot.core.entity.FsBusNodeSafeguardsRecord;
import com.swcares.aiot.core.vo.FsBusNodeSafeguardsRecordItemVO;
import com.swcares.aiot.core.vo.FsBusNodeSafeguardsRecordVO;
import com.swcares.aiot.dto.BaseFlightInfoPagedDTO;
import com.swcares.aiot.dto.FlightSafeguardsInfoQueryDTO;
import com.swcares.aiot.vo.FlightSafeguardVO;
import com.swcares.aiot.vo.FlightSafeguardsInfoVO;
import com.swcares.aiot.vo.FsBusAircraftSafeguardsInfoVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.node.safeguards.service.FsBusNodeSafeguardsRecordService <br>
 * Description：节点保障记录 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
public interface FsBusNodeSafeguardsRecordService extends IService<FsBusNodeSafeguardsRecord> {

    /**
     * Title：logicRemoveById <br>
     * Description：逻辑删除 <br>
     * author：code-generator <br>
     * date：2022-03-02 <br>
     *
     * @param id 主键id<br>
     * @return <br>
     */
    boolean logicRemoveById(Long id);

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：code-generator <br>
     * date：2022-03-02 <br>
     *
     * @param dto 查询对象<br>
     * @return <br>
     */
    IPage<FsBusNodeSafeguardsRecordVO> page(FsBusNodeSafeguardsRecordPagedDTO dto);

    /**
     * Title:  saveNoRepeat<br>
     * Description: 保存不重复的数据 <br>
     * author 周扬
     * date：2022-03-02 <br>
     *
     * @param entity 保存对象<br>
     * @return boolean  <br>
     */
    boolean saveNoRepeat(FsBusNodeSafeguardsRecord entity);


    /**
     * Title：page <br>
     * Description：节点保障记录_列表查询 <br>
     * author：code-generator <br>
     * date：2022-03-02 <br>
     *
     * @param dto 查询对象<br>
     * @return <br>
     */
    List<FsBusNodeSafeguardsRecordVO> list(FsBusNodeSafeguardsRecordQueryDTO dto);


    /**
     * Title : getByFlightParkingStatus<br>
     * Author : 李寰宇<br>
     * Description :  根据部门操作权限及远近机位查询节点 <br>
     * date: 2022-03-22 <br>
     *
     * @param FlightId :
     * @return : List
     */
    List<FsBusNodeSafeguardsRecordVO> getByFlightParkingStatus(Long FlightId, Long[] deptIds, Boolean isLoungeBridge, Boolean hasAuthority);

    /**
     * Title : saveDTO<br>
     * Author : 李寰宇<br>
     * Description :  新建节点记录 <br>
     *
     * @param dto date: 2022-03-23 <br>
     */
    void saveDto(FsBusNodeSafeguardsRecordDTO dto);

    /**
     * Title : getSecurityOverview<br>
     * Author : 李寰宇<br>
     * Description :   <br>
     *
     * @param flightNo date: 2022-03-23 <br>
     * @return : List
     */
    List<FsBusNodeSafeguardsRecordVO> getSecurityOverview(Long flightNo, Boolean isBefore);

    /**
     * Title : completeSafeGuard<br>
     * Author : 曹文韬<br>
     * Description :  完成航班节点保障 <br>
     *
     * @param dto date: 2022-03-23 <br>
     * @return : void
     */
    FsBusNodeSafeguardsRecordItemVO completeSafeGuard(FsBusNodeSafeguardsRecordQueryDTO dto);

    /**
     * Title :updateTime <br>
     * Author : 曹文韬<br>
     * Description :  修改实际保障时间 <br>
     *
     * @param dto date: 2022-03-23 <br>
     * @return : void
     */
    FsBusNodeSafeguardsRecordItemVO updateTime(FsBusNodeSafeguardsRecordQueryDTO dto);

    /**
     * Title :updateSafeGuardTimed<br>
     * Author : 曹文韬<br>
     * Description :  撤销保障完成 <br>
     *
     * @param id date: 2022-03-23 <br>
     * @return : void
     */
    FsBusNodeSafeguardsRecordItemVO updateSafeGuardTime(Long id);

    /**
     * Title :deleteNode<br>
     * Author : 曹文韬<br>
     * Description :  删除节点 <br>
     *
     * @param id date: 2022-03-23 <br>
     * @return : void
     */
    FsBusNodeSafeguardsRecordItemVO deleteNode(Long id);


    /**
     * Title : getIsIncomplete<br>
     * Author : 李寰宇<br>
     * Description :  判断数据是否完整 <br>
     *
     * @param id date: 2022-04-06 <br>
     * @return : List
     */
    Boolean getIsIncomplete(Long id);

    /**
     * Title : getInfo<br>
     * Author : 陈宇峰<br>
     * Description :  新建节点记录 <br>
     *
     * @param dto date: 2022-04-07 <br>
     */
    void editOrCreateInfo(FsBusNodeSafeguardsRecordDTO dto);

    FsBusNodeSafeguardsRecordVO getByDetailsId(Long id);

    /**
     * Title : deleted<br>
     * Author : 陈宇峰<br>
     * Description :  删除节点内容数据 <br>
     *
     * @param dto date: 2022-04-18 <br>
     */
    void deletedInfo(FsBusNodeSafeguardsRecordQueryDTO dto);

    IPage<FsBusAircraftSafeguardsInfoVO> page1(BaseFlightInfoPagedDTO dto);

    /**
     * Title :deleteNode<br>
     * Author : 曹文韬<br>
     * Description :  修改廊桥类型 <br>
     * date: 2022-03-23 <br>
     *
     * @param dto :
     * @return : void
     */
    FsBusNodeSafeguardsRecordItemVO updateBridgeType(FsBusNodeSafeguardsRecordQueryDTO dto);

    void export(BaseFlightInfoPagedDTO dto, HttpServletResponse response);

    void getOvernightInfo(FlightSafeguardsInfoQueryDTO dto);

    /**
     * Title：获取航班保障详情页数据 <br>
     * Description： <br>
     * author ：李寰宇 <br>
     * date：2022/3/22 9:37 <br>
     *
     * @param flightNo :
     * @return : FsBusNodeSafeguardsRecordItemVO
     */
    FsBusNodeSafeguardsRecordItemVO getFsBusNodeSafeguardsRecordItemVO1(Long flightNo);

    List<FlightSafeguardsInfoVO> getAppFlightSafeguardsInfo(FlightAndAirParkingDTO dto);

    /**
     * Title：removeOvernightFlight <br>
     * Description：移除过夜航班 <br>
     * author ：朱程宇 <br>
     * date：2022-05-13 <br>
     *
     * @param id <br>
     */
    void removeOvernightFlight(Long id);

    /**
     * Title：remove <br>
     * Description：移除永久过夜航班 <br>
     * author ：朱程宇 <br>
     * date：2022-06-01 <br>
     *
     * @param id <br>
     */
    void remove(Long id);

    void downLoad(BaseFlightInfoPagedDTO dto, HttpServletResponse response) throws Exception;

    void selectCorridorBridge(FsBusNodeSafeguardsRecordDTO dto);

    /**
     * Title：selectSafeguardsById <br>
     * Description：根据id查看保障全览 <br>
     * author ：罗雪锋 <br>
     * date：2022-06-02 <br>
     *
     * @param id <br>
     * @return <br>
     */
    FlightSafeguardVO selectSafeguardsById(String id);

    String checkFlightSafeGuardsUpdatedTime(String id);

    boolean checkSafeguard(String id);

}

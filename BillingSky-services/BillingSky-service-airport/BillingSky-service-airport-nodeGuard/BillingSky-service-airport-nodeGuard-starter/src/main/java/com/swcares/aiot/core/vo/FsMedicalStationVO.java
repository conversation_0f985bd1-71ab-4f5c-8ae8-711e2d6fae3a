package com.swcares.aiot.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ClassName：com.swcares.aiot.node.safeguards.vo.FsMedicalStationVO <br>
 * Description：医疗救护站返回展示对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FsMedicalStationVO", description="医疗救护站")
public class FsMedicalStationVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;


    @ApiModelProperty(value = "救护站名称")
    private String stationName;

    @ApiModelProperty(value = "救护站地址")
    private String adress;

    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    @ApiModelProperty(value = "病床数")
    private Integer bedNumber;

    @ApiModelProperty(value = "医生数量")
    private Integer doctorNumber;

    @ApiModelProperty(value = "护士数")
    private Integer nurseNumber;

    @ApiModelProperty(value = "急救车数量")
    private Integer ambulanceNumber;

    @ApiModelProperty(value = "机场三字码")
    private String airportCode;



}

package com.swcares.aiot.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.core.dto.FsBaseSafeguardsStandardRulePagedDTO;
import com.swcares.aiot.core.entity.FsBaseSafeguardsStandardRule;
import com.swcares.aiot.core.vo.FsBaseSafeguardsStandardRuleVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aiot.node.safeguards.mapper.FsBaseSafeguardsStandardRuleMapper <br>
 * Description：保障标准表 Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-04 <br>
 * @version v1.0 <br>
 */
@Mapper
public interface FsBaseSafeguardsStandardRuleMapper extends BaseMapper<FsBaseSafeguardsStandardRule> {

    /**
     * Title：list1 <br>
     * Description：保障标准表_列表查询 <br>
     * author ：朱程宇 <br>
     * date：2022-03-07 <br>
     * @param dto 查询对象<br>
     * @return <br>
     */
    IPage<FsBaseSafeguardsStandardRuleVO> listPage(@Param("dto") FsBaseSafeguardsStandardRulePagedDTO dto, @Param("page") Page<FsBaseSafeguardsStandardRuleVO> page);

    /**
     * Title：get <br>
     * Description：通过ID查询保障标准表记录 <br>
     * author ：朱程宇 <br>
     * date：2022-03-07 <br>
     * @return <br>
     */
    FsBaseSafeguardsStandardRuleVO selectId(@Param("id") Long id);

    /**
     * Title：listSecurity <br>
     * Description：保障点列表查询 <br>
     * author ：朱程宇 <br>
     * date：2022-03-07 <br>
     * @return <br>
     */
    List<Map<Integer, String>> listSecurity();

    /**
     * Title：listReference <br>
     * Description：查询参照点自带类型列表 <br>
     * author ：朱程宇 <br>
     * date：2022-03-07 <br>
     * @return <br>
     */
    List<Map<Integer, String>> listReference();
    /**
     * Title：listReferenceOne <br>
     * Description：查询参照点节点类型列表 <br>
     * author ：朱程宇 <br>
     * date：2022-03-07 <br>
     * @return <br>
     */
    List<Map<Integer, String>> listReferenceOne();

    /**
     * Title：selectByIdOne <br>
     * Description：查询当前未修改的保障ID是否正在使用 <br>
     * author ：朱程宇 <br>
     * date：2022-04-24 <br>
     * @return <br>
     */
    Long selectByIdOne(@Param("fsBaseSafeguardsStandardRule")FsBaseSafeguardsStandardRule fsBaseSafeguardsStandardRule);

    /**
     * Title：selectByIdTwo <br>
     * Description：查询库里面是否有保障ID是否正在使用 <br>
     * author ：朱程宇 <br>
     * date：2022-03-07 <br>
     * @return <br>
     */
    String selectByIdTwo(@Param("fsBaseSafeguardsStandardRule") FsBaseSafeguardsStandardRule fsBaseSafeguardsStandardRule);



}

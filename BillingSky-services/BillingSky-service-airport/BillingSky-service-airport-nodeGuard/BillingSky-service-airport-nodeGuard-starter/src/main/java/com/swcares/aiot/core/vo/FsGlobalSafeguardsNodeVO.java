package com.swcares.aiot.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
/**
 * ClassName：com.swcares.aiot.node.safeguards.vo.FsGlobalSafeguardsNodeVO <br>
 * Description：全局基础保障节点信息表返回展示对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FsGlobalSafeguardsNodeVO", description="全局基础保障节点信息表")
public class FsGlobalSafeguardsNodeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;


    @ApiModelProperty(value = "节点code")
    private String nodeCode;

    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    @ApiModelProperty(value = "描述")
    private String desction;



}

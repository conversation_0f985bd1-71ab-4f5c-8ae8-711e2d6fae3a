/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ExcelUtils <br>
 * Package：com.swcares.aiot.datacenter.importer.utils <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 */
package com.swcares.aiot.core.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.swcares.aiot.core.dto.ExportExcelSelectDTO;
import com.swcares.aiot.core.exception.DataImportErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.worm.hutool.HttpUtils;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

/**
 * ClassName：com.swcares.aiot.flight.parking.util <br>
 *
 * <AUTHOR> <br>
 * date 2022/3/11 16:16 <br>
 * @version v1.0.0 <br>
 */
@Slf4j
@UtilityClass
public class ExcelUtil {


    /**
     * Title：importExcelForVerify <br>
     * Description： 导入Excel，并进行数据验证<br>
     * author：liuyanling <br>
     * date：2021年6月16日 下午3:26:26 <br>
     *
     * @param <S>             实体类，用于数据库相关操作
     * @param <T>             导入实体类，继承S，用于存储导入数据，比S多rowNum（行号）、errorMsg（错误信息）字段
     * @param file :
     * @param titleRows :
     * @param headerRows :
     * @param pojoImportClass 导入实体类，继承S，用于存储导入数据，比S多rowNum（行号）、errorMsg（错误信息）字段
     * @return ExcelImportResult.map.get(" errorMsg ")：导入错误拼接信息（包括行号和错误信息）<br>
     */
    public static <S, T extends S> ExcelImportResult<S> importExcelForVerify(MultipartFile file, Integer titleRows,
                                                                             Integer headerRows, Class<T> pojoImportClass) {
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        // 是否开启校验
        params.setNeedVerify(true);
        params.setVerifyHandler(new NullRowExcelVerifyHandler<>());
        try {
            ExcelImportResult<T> importResult =
                    ExcelImportUtil.importExcelMore(file.getInputStream(), pojoImportClass, params);
            List<T> failList = importResult.getFailList();

            StringBuilder msg = new StringBuilder();
            ExcelImportResult<S> result = new ExcelImportResult<>();
            BeanUtils.copyProperties(importResult, result);

            if (importResult.isVerifyFail()) {
                // 拼接错误信息
                failList.forEach(entity -> {
                    Object rowNum = ReflexUtil.getFieldValueByFieldName(entity, "rowNum");
                    Object errorMsg = ReflexUtil.getFieldValueByFieldName(entity, "errorMsg");
                    if (Objects.nonNull(rowNum) && !ObjectUtils.isEmpty(errorMsg)) {
                        msg.append("第").append(Integer.parseInt(rowNum.toString()) + 1).append("行的错误信息是：").append(errorMsg).append("\n");
                    }
                });
                // 因整行空数据验证成功，无法覆盖验证结果；故所有错误数据皆无错误信息，则验证成功
                if (ReflexUtil.hasField(failList.get(0).getClass(), "errorMsg") && ObjectUtils.isEmpty(msg.toString())) {
                    result.setVerifyFail(false);
                }
            }

            List<T> sucessList = importResult.getList();
            if (!CollectionUtils.isEmpty(sucessList)) {
                List<S> list = new ArrayList<>();
                sucessList.forEach(list::add);
                result.setList(list);
            }

            // 设置错误信息
            Map<String, Object> map = new HashMap<>();
            map.put("errorMsg", msg);
            result.setMap(map);
            return result;
        } catch (NoSuchElementException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DataImportErrors.UPLOADING_FILE_INCORRECT);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DataImportErrors.CANNOT_RESOLVE_UPLOADING_FILE);
        }
    }

    public static <T> List<T> importExcel(MultipartFile file, Integer titleRows, Integer headerRows,
                                          Class<T> pojoClass) {
        if (file == null) {
            return null;
        }
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        List<T> list = null;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), pojoClass, params);
        } catch (NoSuchElementException e) {
            throw new BusinessException(DataImportErrors.UPLOADING_FILE_INCORRECT);
        } catch (Exception e) {
            throw new BusinessException(DataImportErrors.CANNOT_RESOLVE_UPLOADING_FILE);
        }
        return list;
    }

    public static void exportExcel(List<?> list, Class<?> pojoClass, String fileName, HttpServletResponse response) {
        try {
            ExportParams exportParams = new ExportParams(null, null, ExcelType.XSSF);
            final Workbook workbook = ExcelExportUtil.exportExcel(exportParams, pojoClass, list);
            downLoadExcel(fileName, workbook, response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DataImportErrors.EXPORT_DATA_FAILED, fileName);
        }
    }

    public static void exportExcelWithSelect(List<?> list, Class<?> pojoClass, String fileName, HttpServletResponse response, List<ExportExcelSelectDTO> exportExcelSelectDTOList) {
        try {
            ExportParams exportParams = new ExportParams(null, null, ExcelType.XSSF);
            exportParams.setAutoSize(false);
            final Workbook workbook = ExcelExportUtil.exportExcel(exportParams, pojoClass, list);
            for (int i = 0; i < exportExcelSelectDTOList.size(); i++) {
                ExportExcelSelectDTO exportExcelSelectDTO = exportExcelSelectDTOList.get(i);
                exportExcelSelectDTO.setWorkbook((XSSFWorkbook) workbook);
                setSelectList(exportExcelSelectDTO, i);
            }
            downLoadExcel(fileName, workbook, response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DataImportErrors.EXPORT_DATA_FAILED, fileName);
        }
    }

    /**
     * Title：downLoadExcel <br>
     * Description：下载文件<br>
     *
     * @param fileName :
     * @param workbook :
     * @param response :
     * <AUTHOR> <br>
     * date：2021-06-01 10:53 <br>
     */
    private static void downLoadExcel(String fileName, Workbook workbook, HttpServletResponse response)
            throws IOException {
        ServletOutputStream output = null;
        try {
            final String downloadName = URLEncoder.encode(fileName + ".xlsx", "UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader(HttpUtils.HEADER_CONTENT_DISPOSITION, "attachment;filename=" + downloadName);
            output = response.getOutputStream();
            workbook.write(output);
        } catch (final Exception e) {
            throw new IOException(e.getMessage());
        } finally {
            if (output != null) {
                output.flush();
                output.close();
            }
        }
    }

    public static <T> List<T> importExcelMore(MultipartFile file, Integer titleRows, Integer headerRows,
                                              Class<T> pojoClass) {
        if (file == null) {
            return null;
        }
        ExcelImportResult<T> result = null;
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        // 开启解析字段
        params.setNeedVerify(true);
        List<T> list;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), pojoClass, params);
        } catch (NoSuchElementException e) {
            throw new BusinessException(DataImportErrors.UPLOADING_FILE_INCORRECT);
        } catch (Exception e) {
            throw new BusinessException(DataImportErrors.CANNOT_RESOLVE_UPLOADING_FILE);
        }
        list = result.getList();
        List<T> failList = result.getFailList();
        if (!failList.isEmpty()) {
            return null;
        }
        return list;
    }

    public static void setSelectList(ExportExcelSelectDTO exportExcelSelectDTO, int j) {
        if (ObjectUtils.isEmpty(exportExcelSelectDTO.getStrings())) {
            return;
        }
        XSSFSheet sheet = exportExcelSelectDTO.getWorkbook().getSheetAt(0);
        XSSFSheet hideSheet = exportExcelSelectDTO.getWorkbook().createSheet("hiddenSheet" + j);

        //  生成下拉列表
        for (int i = 0; i < exportExcelSelectDTO.getStrings().length; i++) {
            hideSheet.createRow(i).createCell(0).setCellValue(exportExcelSelectDTO.getStrings()[i]);
        }
        XSSFName category1Name = exportExcelSelectDTO.getWorkbook().createName();
        category1Name.setNameName("hidden" + j);
        category1Name.setRefersToFormula("hiddenSheet" + j + "!" + "$A$1:$A$" + exportExcelSelectDTO.getStrings().length);
        XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper(sheet);
        XSSFDataValidationConstraint dvConstraint = (XSSFDataValidationConstraint) dvHelper.createFormulaListConstraint("hidden" + j);
        CellRangeAddressList addressList = new CellRangeAddressList(exportExcelSelectDTO.getFirstRow(), exportExcelSelectDTO.getLastRow(), exportExcelSelectDTO.getFirstCol(), exportExcelSelectDTO.getLastCol());
        XSSFDataValidation validation = (XSSFDataValidation) dvHelper.createValidation(dvConstraint, addressList);
        validation.setEmptyCellAllowed(false);
        validation.setSuppressDropDownArrow(true);
        validation.setShowErrorBox(true);
        //  对sheet页生效
        sheet.addValidationData(validation);
        exportExcelSelectDTO.getWorkbook().setSheetHidden(j + 1, true);
    }
}

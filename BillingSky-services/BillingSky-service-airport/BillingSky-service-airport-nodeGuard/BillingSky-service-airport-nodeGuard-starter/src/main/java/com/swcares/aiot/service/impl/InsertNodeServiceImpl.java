package com.swcares.aiot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.swcares.aiot.mapper.FsBusSafeguardsNodeMapper;
import com.swcares.aiot.core.cons.SignBusinessExceptionCodeConstant;
import com.swcares.aiot.core.dto.FsBusSafeguardsNodeDTO;
import com.swcares.aiot.core.entity.FsBusSafeguardsNode;
import com.swcares.aiot.core.entity.FsGlobalSafeguardsNode;
import com.swcares.aiot.core.vo.FsBusSafeguardsNodeVO;
import com.swcares.aiot.service.FsBusSafeguardsNodeService;
import com.swcares.aiot.service.FsGlobalSafeguardsNodeService;
import com.swcares.aiot.service.InsertNodeService;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：InsertNodeServiceImpl <br>
 * Package：com.swcares.aiot.node.safeguards.service.impl <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 04月11日 17:55 <br>
 * @version v1.0 <br>
 */
@Service
public class InsertNodeServiceImpl implements InsertNodeService {
    @Resource
    private FsGlobalSafeguardsNodeService fsGlobalSafeguardsNodeService;
    @Resource
    private FsBusSafeguardsNodeService fsBusSafeguardsNodeService;
    @Resource
    private FsBusSafeguardsNodeMapper fsBusSafeguardsNodeMapper;

    @Override
    public void insertNode(FsBusSafeguardsNodeDTO dto) {
        FsGlobalSafeguardsNode byId = fsGlobalSafeguardsNodeService.getById(dto.getGlobalNodeId());
        FsBusSafeguardsNode fsBusSafeguardsNode = new FsBusSafeguardsNode();
        fsBusSafeguardsNode.setNodeName(byId.getNodeName());
        fsBusSafeguardsNode.setNodeCode(byId.getNodeCode());
        if (checkData(byId.getId())) {
            fsBusSafeguardsNodeService.saveDTO(dto, fsBusSafeguardsNode);
        }
    }

    @Override
    public void updateNode(FsBusSafeguardsNodeDTO dto) {
        FsGlobalSafeguardsNode byId = fsGlobalSafeguardsNodeService.getById(dto.getGlobalNodeId());
        dto.setNodeName(byId.getNodeName());
        dto.setNodeCode(byId.getNodeCode());
        QueryWrapper<FsBusSafeguardsNode> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne("id", dto.getId());
        queryWrapper.eq("deleted", 0);
        List<FsBusSafeguardsNode> fsBusSafeguardsNodes = fsBusSafeguardsNodeMapper.selectList(queryWrapper);
        if (ObjectUtils.isNotEmpty(fsBusSafeguardsNodes)) {
            fsBusSafeguardsNodes.forEach(e -> {
                if (Objects.equals(e.getGlobalNodeId(), byId.getId())) {
                    throw new BusinessException(SignBusinessExceptionCodeConstant.DUP_DATA);
                }
            });
        }
        fsBusSafeguardsNodeService.updateByDTO(dto);

    }

    /**
     * Title：checkData <br>
     * Description：校验数据 <br>
     *
     * @param id :
     * @return : Boolean
     * author ：李寰宇 <br>
     * date ：2022/4/21 12:17 <br>
     */
    public Boolean checkData(Long id) {
        List<FsBusSafeguardsNodeVO> list = fsBusSafeguardsNodeService.list("");
        list.forEach(e -> {
            if (Objects.equals(e.getGlobalNodeId(), id)) {
                throw new BusinessException(SignBusinessExceptionCodeConstant.DUP_DATA);
            }
        });
        return true;
    }
}

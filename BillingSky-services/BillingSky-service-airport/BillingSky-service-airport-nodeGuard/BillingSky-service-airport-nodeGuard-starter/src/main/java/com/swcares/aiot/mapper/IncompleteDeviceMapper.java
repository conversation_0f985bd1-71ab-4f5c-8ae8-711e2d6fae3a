package com.swcares.aiot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aiot.core.dto.IncompleteDevicePagedDTO;
import com.swcares.aiot.core.dto.IncompleteDeviceQueryDTO;
import com.swcares.aiot.core.entity.IncompleteDevice;
import com.swcares.aiot.core.vo.IncompleteDeviceVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * ClassName：com.swcares.aiot.node.safeguards.mapper.IncompleteDeviceMapper <br>
 * Description：残缺设备表 Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-04-25 <br>
 * @version v1.0 <br>
 */
@Mapper
public interface IncompleteDeviceMapper extends BaseMapper<IncompleteDevice> {

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：wupengfei <br>
     * date：2022-04-25 <br>
     * @param dto
     * @param page <br>
     * @return <br>
     */
    IPage<IncompleteDeviceVO> page(@Param("dto") IncompleteDevicePagedDTO dto, Page<IncompleteDeviceVO> page);

    /**
    * Title : list<br>
    * Author : wupengfei<br>
    * Description :  残缺设备表_列表查询 <br>
    * @param dto  查询对象
    * date: 2022-04-25 <br>
    * return: List<IncompleteDeviceVO>
     */
     List<IncompleteDeviceVO> list(@Param("dto")IncompleteDeviceQueryDTO dto);
}

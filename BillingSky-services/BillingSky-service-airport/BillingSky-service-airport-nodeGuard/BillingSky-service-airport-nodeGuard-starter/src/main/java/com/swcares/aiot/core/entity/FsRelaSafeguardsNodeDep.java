package com.swcares.aiot.core.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.aiot.node.safeguards.entity.FsRelaSafeguardsNodeDep <br>
 * Description：保障节点和部门关联表（控制记录权限） <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FsRelaSafeguardsNodeDep", description="保障节点和部门关联表（控制记录权限）")
public class FsRelaSafeguardsNodeDep {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "节点ID")
    private Long baseSafeguardsNodeId;

    @ApiModelProperty(value = "部门id")
    private Long deptId;
}

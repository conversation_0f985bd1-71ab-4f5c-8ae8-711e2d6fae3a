package com.swcares.aiot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

import com.swcares.aiot.core.dto.FsAirportFireControlDTO;
import com.swcares.aiot.core.entity.FsAirportFireControl;
import com.swcares.aiot.core.vo.FsAirportFireControlVO;
import com.swcares.aiot.core.dto.FsAirportFireControlQueryDTO;
import com.swcares.aiot.core.dto.FsAirportFireControlPagedDTO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * ClassName：com.swcares.aiot.node.safeguards.service.FsAirportFireControlService <br>
 * Description：机场消防表 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
public interface FsAirportFireControlService extends IService<FsAirportFireControl> {

    /**
     * Title：logicRemoveById <br>
     * Description：逻辑删除 <br>
     * author：code-generator <br>
     * date：2022-03-02 <br>
     * @param id  主键id<br>
     * @return <br>
     */
    boolean logicRemoveById(Long id);

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：code-generator <br>
     * date：2022-03-02 <br>
     * @param dto 查询对象<br>
     * @return <br>
     */
    IPage<FsAirportFireControlVO> page(FsAirportFireControlPagedDTO dto);

    /**
     * Title:  saveNoRepeat<br>
     * Description: 保存不重复的数据 <br>
     * author 周扬
     * date：2022-03-02 <br>
     * @param entity   保存对象<br>
     * @return boolean  <br>
     */
    boolean saveNoRepeat(FsAirportFireControl entity);


    /**
     * Title：page <br>
     * Description：机场消防表_列表查询 <br>
     * author：code-generator <br>
     * date：2022-03-02 <br>
     * @param dto 查询对象<br>
     * @return <br>
     */
    List<FsAirportFireControlVO> list(FsAirportFireControlQueryDTO dto);

    void  updateDTO(FsAirportFireControlDTO dto);

    void saveDTO(FsAirportFireControl fsAirportFireControl);
}

package com.swcares.aiot.core.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.swcares.aiot.enums.DeletedEnum;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * ClassName：com.swcares.aiot.node.safeguards.entity.FsCorridorBridge <br>
 * Description：廊桥表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="FsCorridorBridge", description="廊桥表")
public class FsCorridorBridge extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "编号")
    @NotNull
    @Size(min = 1,max = 10)
    private String bridgeNo;

    @ApiModelProperty(value = "廊桥描述")
    @NotNull
    private String bridgeDescription;

    @ApiModelProperty(value = "廊桥类型")
    @NotNull
    private String bridgeType;

    @ApiModelProperty(value = "航站楼id")
    @NotNull
    private Long fkAirportTerminal;

    @ApiModelProperty(value = "是否启用;0代表启用，1代表停用")
    private Boolean isValid;

    @ApiModelProperty(value = "机场三字码")
    @NotNull
    @Size(min = 1,max = 3)
    private String airportCode;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "删除标识", hidden = true)
    private DeletedEnum deleted;
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "部门ID", hidden = true)
    private Long deptId;


}

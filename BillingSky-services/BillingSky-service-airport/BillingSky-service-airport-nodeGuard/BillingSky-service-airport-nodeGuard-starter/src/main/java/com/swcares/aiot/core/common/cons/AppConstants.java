package com.swcares.aiot.core.common.cons;

import lombok.Data;
import lombok.experimental.UtilityClass;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：AppConstants <br>
 * Package：com.swcares.aiot.node.safeguards.common <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 03月03日 10:50 <br>
 * @version v1.0 <br>
 */
@UtilityClass
@Data
public class AppConstants {
    public static final String SWAGGER_API_VERSION_INFO = "节点保障系统-API";

    public static final String SWAGGER_API_APP_VERSION_INFO = "节点保障系统APP-API";

    public static final String ARRIVE_THIS_AIRPORT = "到达本站";
    //节点属性 对应字典表
    public static final String UNLIMITED = "UNLIMITED";
    public static final String LOUNGE_BRIDGE = "LOUNGE_BRIDGE";
    public static final String NON_LOUNGE_BRIDGE = "NON_LOUNGE_BRIDGE";
}

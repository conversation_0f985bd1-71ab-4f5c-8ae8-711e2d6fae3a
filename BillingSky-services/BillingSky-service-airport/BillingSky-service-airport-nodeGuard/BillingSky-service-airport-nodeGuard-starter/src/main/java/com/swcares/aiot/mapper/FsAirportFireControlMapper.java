package com.swcares.aiot.mapper;

import com.swcares.aiot.core.entity.FsAirportFireControl;
import com.swcares.aiot.core.vo.FsAirportFireControlVO;
import com.swcares.aiot.core.dto.FsAirportFireControlQueryDTO;
import com.swcares.aiot.core.dto.FsAirportFireControlPagedDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
/**
 * ClassName：com.swcares.aiot.node.safeguards.mapper.FsAirportFireControlMapper <br>
 * Description：机场消防表 Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Mapper
public interface FsAirportFireControlMapper extends BaseMapper<FsAirportFireControl> {

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：code-generator <br>
     * date：2022-03-02 <br>
     * @param dto :
     * @param page <br>
     * @return <br>
     */
    IPage<FsAirportFireControlVO> page(@Param("dto") FsAirportFireControlPagedDTO dto, Page<FsAirportFireControlVO> page);

    /**
    * Author: code-generator<br>
    * Description:  机场消防表_列表查询 <br>
    * Date: 2022-03-02 <br>
    * Title : list<br>
    * @param dto  查询对象
    * return: List<FsAirportFireControlVO>
     */
     List<FsAirportFireControlVO> list(@Param("dto")FsAirportFireControlQueryDTO dto);
}

package com.swcares.aiot.core.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.swcares.aiot.enums.DeletedEnum;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.aiot.node.safeguards.entity.FsAirportTerminal <br>
 * Description：航站楼表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="FsAirportTerminal", description="航站楼表")
public class FsAirportTerminal extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "航站楼编号")
    private String terminalNo;

    @ApiModelProperty(value = "航站楼描述")
    private String terminalDescription;

    @ApiModelProperty(value = "航站楼属性")
    private String terminalType;

    @ApiModelProperty(value = "是否启用")
    private Boolean isValid;

    @ApiModelProperty(value = "机场三字码")
    private String airportCode;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "删除标识", hidden = true)
    private DeletedEnum deleted;
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "部门ID", hidden = true)
    private Long deptId;


}

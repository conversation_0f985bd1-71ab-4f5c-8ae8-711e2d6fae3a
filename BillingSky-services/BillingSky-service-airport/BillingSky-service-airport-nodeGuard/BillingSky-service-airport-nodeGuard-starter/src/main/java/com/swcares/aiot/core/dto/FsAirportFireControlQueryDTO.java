package com.swcares.aiot.core.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ClassName：com.swcares.aiot.node.safeguards.dto.FsAirportFireControlQueryDTO <br>
 * Description：机场消防表 查询_数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FsAirportFireControlQueryDTO", description="机场消防表")
public class FsAirportFireControlQueryDTO  implements BaseDTO, Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "消防站名称")
    @NotNull
    @Size(min=1,max=90)
    private String stationName;

    @ApiModelProperty(value = "消防保障等级")
    @NotNull
    private String stationSupportLevel;

    @ApiModelProperty(value = "消防站类型")
    @NotNull
    private String stationType;

    @ApiModelProperty(value = "经度")
    @Digits(integer = 3,fraction = 10)
    @NotNull
    private BigDecimal longitude;

    @ApiModelProperty(value = "纬度")
    @Digits(integer = 3,fraction = 10)
    @NotNull
    private BigDecimal latitude;

    @ApiModelProperty(value = "消防员数量")
    @Max(1000)
    @Min(1)
    @NotNull
    private Integer firemenNumber;

    @ApiModelProperty(value = "消防车数量")
    @Max(1000)
    @Min(1)
    @NotNull
    private Integer fireBusNumber;

    @ApiModelProperty(value = "应急救援网格图")
    @Size(min=1,max=900)
    @NotNull
    private String photo;

    @ApiModelProperty(value = "图片名称")
    @Size(min=1,max=90)
    @NotNull
    private String photoName;

    @ApiModelProperty(value = "机场三字码")
    @NotNull
    private String airportCode;

    @ApiModelProperty(value = "图片id")
    private Long photoId;
}

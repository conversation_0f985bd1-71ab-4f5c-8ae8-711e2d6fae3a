package com.swcares.aiot.core.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * ClassName：com.swcares.aiot.node.safeguards.dto.FsAirportPolicePagedDTO <br>
 * Description：机场公安表 分页数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="FsAirportPolicePagedDTO", description="机场公安表")
public class FsAirportPolicePagedDTO extends PagedDTO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "公安名称")
    @Size(min=1,max=60)
    @NotNull
    private String policeName;

    @ApiModelProperty(value = "公安类型")
    private String policeType;

    @ApiModelProperty(value = "机场三字码")
    @Size(min=1,max=6)
    @NotNull
    private String airportCode;

    @ApiModelProperty(value = "删除标识（1：删除 0：正常)")
    @Size(min=1,max=2)
    @NotNull
    private Long deleted;



}

package com.swcares.aiot.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.node.safeguards.vo.FsBusNodeSafeguardsRecordVO <br>
 * Description：节点保障记录返回展示对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FsBusNodeSafeguardsRecordVO", description="节点保障记录")
public class FsBusNodeSafeguardsRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;


    @ApiModelProperty(value = "飞机保障表ID")
    private Long aircraftSafeguardsInfoId;

    @ApiModelProperty(value = "航前航后")
    private String safeguardsType;

    @ApiModelProperty(value = "节点id")
    private Long nodeId;

    @ApiModelProperty(value = "节点code")
    private String nodeCode;

    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    @ApiModelProperty(value = "节点名称后缀")
    private Integer nodeNameSuffixNum;

    @ApiModelProperty(value = "预计保障时间")
    private LocalDateTime predictDatetime;

    @ApiModelProperty(value = "保障人账号")
    private String safeguardAccount;

    @ApiModelProperty(value = "实际保障时间")
    private LocalDateTime realDatetime;

    @ApiModelProperty(value = "廊桥通道数量")
    private String loungeBridgeChannelCount;

    @ApiModelProperty(value = "基础排序号")
    private Integer baseSortNum;

    @ApiModelProperty(value = "业务自定义排序号")
    private Integer busSortNum;

    @ApiModelProperty(value="是否超时未保障")
    private Boolean isTimeOut=false;

    @ApiModelProperty(value="保障状态")
    private String status;

    @ApiModelProperty(value = "部门id")
    private Long deptId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "是否有权限")
    private Boolean hasAuthority;

    @ApiModelProperty(value = "保障人id")
    private String safeguardId;

    @ApiModelProperty(value = "系统内置廊桥通道数量")
    private String sysLoungeBridgeChannelCount;

    @ApiModelProperty(value = "航前是否有数据")
    private Boolean isDataBeforeFlight;

}

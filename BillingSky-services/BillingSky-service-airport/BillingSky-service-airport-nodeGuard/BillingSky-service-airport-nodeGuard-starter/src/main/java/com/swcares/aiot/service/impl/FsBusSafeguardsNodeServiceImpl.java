package com.swcares.aiot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpStatus;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aiot.client.IAdviceSettingClient;
import com.swcares.aiot.client.IBillItemClient;
import com.swcares.aiot.core.common.cons.AppConstants;
import com.swcares.aiot.core.cons.SignBusinessExceptionCodeConstant;
import com.swcares.aiot.core.dto.FsBaseSafeguardsGroupDTO;
import com.swcares.aiot.core.dto.FsBusSafeguardsNodeDTO;
import com.swcares.aiot.core.dto.FsBusSafeguardsNodePagedDTO;
import com.swcares.aiot.core.dto.FsGlobalSafeguardsNodeQueryDTO;
import com.swcares.aiot.core.entity.FsBaseSafeguardsGroup;
import com.swcares.aiot.core.entity.FsBaseSafeguardsStandardRule;
import com.swcares.aiot.core.entity.FsBusSafeguardsNode;
import com.swcares.aiot.core.entity.FsRelaSafeguardsNodeDep;
import com.swcares.aiot.core.util.DragSort;
import com.swcares.aiot.core.vo.FsBaseSafeguardsGroupVO;
import com.swcares.aiot.core.vo.FsBusSafeguardsNodeVO;
import com.swcares.aiot.core.vo.FsGlobalSafeguardsNodeVO;
import com.swcares.aiot.core.vo.FsSafeguardsNodeListVO;
import com.swcares.aiot.core.vo.bus.DropDownMenuVO;
import com.swcares.aiot.enums.DeletedEnum;
import com.swcares.aiot.mapper.*;
import com.swcares.aiot.service.FsBusSafeguardsNodeService;
import com.swcares.aiot.service.FsGlobalSafeguardsNodeService;
import com.swcares.aiot.utils.QueryWrapperUtil;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.tenant.DataSource;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.swcares.aiot.utils.AssertUtil.assertNull;

/**
 * ClassName：com.swcares.aiot.node.safeguards.service.impl.FsBusSafeguardsNodeServiceImpl <br>
 * Description：保障节点名称 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class FsBusSafeguardsNodeServiceImpl extends ServiceImpl<FsBusSafeguardsNodeMapper, FsBusSafeguardsNode> implements FsBusSafeguardsNodeService {
    @Resource
    private FsBusSafeguardsNodeMapper fsBusSafeguardsNodeMapper;

    @Resource
    private FsRelaSafeguardsNodeDepMapper fsRelaSafeguardsNodeDepMapper;

    @Resource
    private FsBaseSafeguardsGroupMapper fsBaseSafeguardsGroupMapper;

    @Resource
    private IAdviceSettingClient iAdviceSettingClient;

    @Resource
    private FsGlobalSafeguardsNodeMapper globalSafeguardsNodeMapper;

    @Resource
    private FsGlobalSafeguardsNodeService fsGlobalSafeguardsNodeService;

    @Resource
    private IBillItemClient iBillItemClient;

    @Resource
    FsBaseSafeguardsStandardRuleMapper fsBaseSafeguardsStandardRuleMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean logicRemoveById(Long id) {
        //判断当前节点是否已被关联
        LambdaQueryWrapper<FsBaseSafeguardsGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FsBaseSafeguardsGroup::getDeleted, DeletedEnum.NORMAL.getValue())
                .and(fsBaseSafeguardsGroupQueryWrapper -> fsBaseSafeguardsGroupQueryWrapper.eq(FsBaseSafeguardsGroup::getStartSafeguardsNodeId, id)
                        .or()
                        .eq(FsBaseSafeguardsGroup::getEdnSafeguardsNodeId, id));
        List<FsBaseSafeguardsGroup> fsBaseSafeguardsGroups = fsBaseSafeguardsGroupMapper.selectList(queryWrapper);
        if (!fsBaseSafeguardsGroups.isEmpty()) {
            throw new BusinessException(SignBusinessExceptionCodeConstant.NODE_IS_REAL);
        }
        //判断保障标准是否引用节点
        LambdaQueryWrapper<FsBaseSafeguardsStandardRule> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(FsBaseSafeguardsStandardRule::getDeleted, DeletedEnum.NORMAL.getValue());
        queryWrapper1.apply(" (safeguards_node_id = " + id + " OR refer_node_id = " + id.toString() + ")");
        List<FsBaseSafeguardsStandardRule> fsBaseSafeguardsStandardRules = fsBaseSafeguardsStandardRuleMapper.selectList(queryWrapper1);
        if (ObjectUtils.isNotEmpty(fsBaseSafeguardsStandardRules)) {
            throw new BusinessException(SignBusinessExceptionCodeConstant.RULE_REAL);
        }
        //调用签单内容 检查节点是否有引用
        BaseResult<Boolean> booleanBaseResult = iBillItemClient.getByNodeId(id);
        //判断是否调用成功
        if (booleanBaseResult.getCode() == HttpStatus.HTTP_OK) {
            Boolean data = booleanBaseResult.getData();
            //判断是否正在引用
            if (Boolean.FALSE.equals(data)) {
                throw new BusinessException(SignBusinessExceptionCodeConstant.BILL_REAL);
            }
        }
        //删除操作权限相关信息
        fsRelaSafeguardsNodeDepMapper.deleteByBaseSafeguardsNodeId(id);
        FsBusSafeguardsNode entity = new FsBusSafeguardsNode();
        BaseResult<Object> objectBaseResult = iAdviceSettingClient.deleteAdviceSetting(id);
        if (objectBaseResult.getCode() != HttpStatus.HTTP_OK) {
            throw new BusinessException(SignBusinessExceptionCodeConstant.ACCESS_SERVER_ERROR);
        }
        entity.setId(id);
        entity.setDeleted(DeletedEnum.DELETED);
        return updateById(entity);
    }

    @Override
    public IPage<FsBusSafeguardsNodeVO> page(FsBusSafeguardsNodePagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

    @Override
    public List<FsBusSafeguardsNodeVO> list(String nodeName) {
        List<FsBusSafeguardsNodeVO> list = fsBusSafeguardsNodeMapper.list(nodeName);
        list.forEach(e -> {
            if (e.getPreNodeId() != null) {
                FsBusSafeguardsNodeVO one = fsBusSafeguardsNodeMapper.getOne(e.getPreNodeId());
                if (one != null) {
                    String nodeName1 = one.getNodeName();
                    e.setPreNodeName(nodeName1);
                }
            }
        });
        return list;
    }

    @Override
    public FsBusSafeguardsNodeVO getOne(Long id) {
        FsBusSafeguardsNodeVO one = fsBusSafeguardsNodeMapper.getOne(id);
        if (one.getPreNodeId() != null) {
            one.setPreNodeName(fsBusSafeguardsNodeMapper.getOne(one.getPreNodeId()).getNodeName());
        }
        return one;

    }

    @Override
    public boolean saveNoRepeat(FsBusSafeguardsNode entity) {
        QueryWrapper<FsBusSafeguardsNode> queryWrapper = QueryWrapperUtil.createQueryWrapperAddColumnDel();
        //添加去重复条件
        List<FsBusSafeguardsNode> repeatList = baseMapper.selectList(queryWrapper);
        assertNull(repeatList, SignBusinessExceptionCodeConstant.SIGN_REPEAT_DATA_CODE);
        return save(entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDTO(FsBusSafeguardsNodeDTO dto, FsBusSafeguardsNode fsBusSafeguardsNode) {
        List<FsRelaSafeguardsNodeDep> deptList = dto.getDeptList();
        String nodeName = fsBusSafeguardsNode.getNodeName();
        String nodeCode = fsBusSafeguardsNode.getNodeCode();
        //查询当前序号最大值
        Integer maxSortNum = fsBusSafeguardsNodeMapper.getMaxSortNum();
        try {
            ObjectUtils.copyProperties(dto, fsBusSafeguardsNode);
        } catch (Exception e) {
            throw new BusinessException(SignBusinessExceptionCodeConstant.CREATE_ERROR);
        }
        fsBusSafeguardsNode.setSortNum(maxSortNum + 1);
        fsBusSafeguardsNode.setStatus(true);
        fsBusSafeguardsNode.setNodeName(nodeName);
        fsBusSafeguardsNode.setNodeCode(nodeCode);
        baseMapper.insert(fsBusSafeguardsNode);
        //存操作权限信息至节点部门中间表
        deptList.forEach(e -> {
            FsRelaSafeguardsNodeDep fsRelaSafeguardsNodeDep = new FsRelaSafeguardsNodeDep();
            fsRelaSafeguardsNodeDep.setBaseSafeguardsNodeId(fsBusSafeguardsNode.getId());
            fsRelaSafeguardsNodeDep.setDeptId(e.getDeptId());
            fsRelaSafeguardsNodeDepMapper.insert(fsRelaSafeguardsNodeDep);
        });
        List<Long> longList = deptList.stream().map(FsRelaSafeguardsNodeDep::getDeptId).collect(Collectors.toList());
        BaseResult<Object> objectBaseResult = iAdviceSettingClient.saveSetting(fsBusSafeguardsNode.getId(), longList);
        if (objectBaseResult.getCode() != HttpStatus.HTTP_OK) {
            log.error(" add remote message  to messageCenter service error , result = {}", objectBaseResult);
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
    }

    @Override
    public void changeStatus(Long id) {
        FsBusSafeguardsNode fsBusSafeguardsNode = fsBusSafeguardsNodeMapper.selectById(id);
        if (Boolean.TRUE.equals(fsBusSafeguardsNode.getStatus())) {
            //判断当前节点是否已被关联
            LambdaQueryWrapper<FsBaseSafeguardsGroup> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FsBaseSafeguardsGroup::getDeleted, DeletedEnum.NORMAL.getValue())
                    .and(fsBaseSafeguardsGroupQueryWrapper -> fsBaseSafeguardsGroupQueryWrapper.eq(FsBaseSafeguardsGroup::getStartSafeguardsNodeId, fsBusSafeguardsNode.getId()).or()
                            .eq(FsBaseSafeguardsGroup::getEdnSafeguardsNodeId, fsBusSafeguardsNode.getId()));
            List<FsBaseSafeguardsGroup> fsBaseSafeguardsGroups = fsBaseSafeguardsGroupMapper.selectList(queryWrapper);
            if (!fsBaseSafeguardsGroups.isEmpty()) {
                throw new BusinessException(SignBusinessExceptionCodeConstant.NODE_IS_REAL);
            }
            fsBusSafeguardsNode.setStatus(false);
        } else {
            fsBusSafeguardsNode.setStatus(true);
        }
        fsBusSafeguardsNodeMapper.updateById(fsBusSafeguardsNode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByDTO(FsBusSafeguardsNodeDTO dto) {
        Long id = dto.getId();
        //清除之前的权限，新增当前存在的
        fsRelaSafeguardsNodeDepMapper.deleteByBaseSafeguardsNodeId(id);
        List<FsRelaSafeguardsNodeDep> deptList = dto.getDeptList();
        deptList.forEach(e -> {
            FsRelaSafeguardsNodeDep fsRelaSafeguardsNodeDep = new FsRelaSafeguardsNodeDep();
            fsRelaSafeguardsNodeDep.setBaseSafeguardsNodeId(dto.getId());
            fsRelaSafeguardsNodeDep.setDeptId(e.getDeptId());
            fsRelaSafeguardsNodeDepMapper.insert(fsRelaSafeguardsNodeDep);
        });
        FsBusSafeguardsNode fsBusSafeguardsNode = new FsBusSafeguardsNode();
        try {
            ObjectUtils.copyProperties(dto, fsBusSafeguardsNode);
        } catch (Exception e) {
            throw new BusinessException(SignBusinessExceptionCodeConstant.UPDATE_ERROR);
        }
        fsBusSafeguardsNodeMapper.updateById(fsBusSafeguardsNode);

    }

    @Override
    public List<FsBaseSafeguardsGroupVO> safeGuardsGroupList() {
        List<FsBaseSafeguardsGroup> fsBaseSafeguardsGroups = fsBaseSafeguardsGroupMapper.list();
        List<FsBaseSafeguardsGroupVO> volist = new ArrayList<>();
        fsBaseSafeguardsGroups.forEach(fsBaseSafeguardsGroup -> {
            FsBusSafeguardsNodeVO start = fsBusSafeguardsNodeMapper.getOne(fsBaseSafeguardsGroup.getStartSafeguardsNodeId());
            String nodeStartName = "";
            String nodeEndName = "";
            if (start != null) {
                nodeStartName = start.getNodeName();
            }
            FsBusSafeguardsNodeVO end = fsBusSafeguardsNodeMapper.getOne(fsBaseSafeguardsGroup.getEdnSafeguardsNodeId());
            if (end != null) {
                nodeEndName = end.getNodeName();
            }
            FsBaseSafeguardsGroupVO fsBaseSafeguardsGroupVo = getFsBaseSafeguardsGroupVO(fsBaseSafeguardsGroup, nodeStartName, nodeEndName);
            volist.add(fsBaseSafeguardsGroupVo);
        });
        return volist;
    }

    private FsBaseSafeguardsGroupVO getFsBaseSafeguardsGroupVO(FsBaseSafeguardsGroup fsBaseSafeguardsGroup, String nodeStartName, String nodeEndName) {
        FsBaseSafeguardsGroupVO fsBaseSafeguardsGroupVo = new FsBaseSafeguardsGroupVO();
        fsBaseSafeguardsGroupVo.setStartSafeguardsNodeName(nodeStartName);
        fsBaseSafeguardsGroupVo.setEdnSafeguardsNodeName(nodeEndName);
        fsBaseSafeguardsGroupVo.setEdnSafeguardsNodeId(fsBaseSafeguardsGroup.getEdnSafeguardsNodeId());
        fsBaseSafeguardsGroupVo.setStartSafeguardsNodeId(fsBaseSafeguardsGroup.getStartSafeguardsNodeId());
        fsBaseSafeguardsGroupVo.setId(fsBaseSafeguardsGroup.getId());
        fsBaseSafeguardsGroupVo.setName(fsBaseSafeguardsGroup.getName());
        return fsBaseSafeguardsGroupVo;
    }

    @Override
    public void saveGroup(FsBaseSafeguardsGroupDTO fsBaseSafeguardsGroupDTO) {
        FsBaseSafeguardsGroup fsBaseSafeguardsGroup = new FsBaseSafeguardsGroup();
        try {
            ObjectUtils.copyProperties(fsBaseSafeguardsGroupDTO, fsBaseSafeguardsGroup);
        } catch (Exception e) {
            throw new BusinessException(CommonErrors.PARAM_VALUE_INVALID_ERROR, "后台出错");
        }
        //数据校验
        String name = fsBaseSafeguardsGroupDTO.getName();
        List<FsBaseSafeguardsGroupVO> fsBaseSafeguardsGroupVos = safeGuardsGroupList();
        if (ObjectUtils.isNotEmpty(fsBaseSafeguardsGroupVos)) {
            fsBaseSafeguardsGroupVos.forEach(e -> {
                if (e.getName().equals(name)) {
                    throw new BusinessException(SignBusinessExceptionCodeConstant.NODE_REAL_NAME_IS_EXIST);
                }
                if (e.getStartSafeguardsNodeId().equals(fsBaseSafeguardsGroupDTO.getStartSafeguardsNodeId()) ||
                        e.getStartSafeguardsNodeId().equals(fsBaseSafeguardsGroupDTO.getEdnSafeguardsNodeId())
                        || e.getEdnSafeguardsNodeId().equals(fsBaseSafeguardsGroupDTO.getEdnSafeguardsNodeId())
                        || e.getEdnSafeguardsNodeId().equals(fsBaseSafeguardsGroupDTO.getStartSafeguardsNodeId())) {
                    throw new BusinessException(SignBusinessExceptionCodeConstant.NODE_REAL_IS_EXIST);
                }

            });
        }
        //判断修改的节点id是否有问题 一个廊桥一个非廊桥
        Long startSafeguardsNodeId = fsBaseSafeguardsGroupDTO.getStartSafeguardsNodeId();
        Long ednSafeguardsNodeId = fsBaseSafeguardsGroupDTO.getEdnSafeguardsNodeId();
        FsBusSafeguardsNode startFsBusSafeguardsNode = fsBusSafeguardsNodeMapper.selectById(startSafeguardsNodeId);
        FsBusSafeguardsNode endFsBusSafeguardsNode = fsBusSafeguardsNodeMapper.selectById(ednSafeguardsNodeId);
        if ((startFsBusSafeguardsNode.getLoungeBridgeAtrrDictVal().equals(AppConstants.LOUNGE_BRIDGE) &&
                endFsBusSafeguardsNode.getLoungeBridgeAtrrDictVal().equals(AppConstants.NON_LOUNGE_BRIDGE))
                || startFsBusSafeguardsNode.getLoungeBridgeAtrrDictVal().equals(AppConstants.NON_LOUNGE_BRIDGE) &&
                endFsBusSafeguardsNode.getLoungeBridgeAtrrDictVal().equals(AppConstants.LOUNGE_BRIDGE)) {
            throw new BusinessException(SignBusinessExceptionCodeConstant.CREATE_ERROR);
        }
        fsBaseSafeguardsGroupMapper.insert(fsBaseSafeguardsGroup);
    }

    @Override
    public void deleteGroup(Long id) {
        //调用签单内容 检查节点是否有引用
        BaseResult<Boolean> booleanBaseResult = iBillItemClient.getByNodeId(id);
        //判断是否调用成功
        if (booleanBaseResult.getCode() == HttpStatus.HTTP_OK) {
            Boolean data = booleanBaseResult.getData();
            //判断是否正在引用
            if (Boolean.FALSE.equals(data)) {
                throw new BusinessException(SignBusinessExceptionCodeConstant.BILL_REAL);
            }
        }
        FsBaseSafeguardsGroup fsBaseSafeguardsGroup = new FsBaseSafeguardsGroup();
        fsBaseSafeguardsGroup.setId(id);
        fsBaseSafeguardsGroup.setDeleted(DeletedEnum.DELETED);
        fsBaseSafeguardsGroupMapper.updateById(fsBaseSafeguardsGroup);
    }


    @Override
    public void updateGroup(FsBaseSafeguardsGroupDTO fsBaseSafeguardsGroupDTO) {
        FsBaseSafeguardsGroup fsBaseSafeguardsGroup = new FsBaseSafeguardsGroup();
        try {
            ObjectUtils.copyProperties(fsBaseSafeguardsGroupDTO, fsBaseSafeguardsGroup);
        } catch (Exception e) {
            throw new BusinessException(CommonErrors.PARAM_VALUE_INVALID_ERROR, "后台出错");
        }
        //数据校验
        String name = fsBaseSafeguardsGroupDTO.getName();

        LambdaQueryWrapper<FsBaseSafeguardsGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FsBaseSafeguardsGroup::getDeleted, DeletedEnum.NORMAL.getValue());
        queryWrapper.ne( BaseEntity::getId, fsBaseSafeguardsGroupDTO.getId());
        List<FsBaseSafeguardsGroup> fsBaseSafeguardsGroupVos = fsBaseSafeguardsGroupMapper.selectList(queryWrapper);
        List<FsBaseSafeguardsGroupVO> fsBaseSafeguardsGroupVos1 = ObjectUtils.copyBeans(fsBaseSafeguardsGroupVos, FsBaseSafeguardsGroupVO.class);

        if (ObjectUtils.isNotEmpty(fsBaseSafeguardsGroupVos1)) {
            fsBaseSafeguardsGroupVos1.forEach(e -> {
                if (e.getName().equals(name)) {
                    throw new BusinessException(SignBusinessExceptionCodeConstant.NODE_REAL_NAME_IS_EXIST);
                }
                if (e.getStartSafeguardsNodeId().equals(fsBaseSafeguardsGroupDTO.getStartSafeguardsNodeId()) ||
                        e.getStartSafeguardsNodeId().equals(fsBaseSafeguardsGroupDTO.getEdnSafeguardsNodeId())
                        || e.getEdnSafeguardsNodeId().equals(fsBaseSafeguardsGroupDTO.getEdnSafeguardsNodeId())
                        || e.getEdnSafeguardsNodeId().equals(fsBaseSafeguardsGroupDTO.getStartSafeguardsNodeId())) {
                    throw new BusinessException(SignBusinessExceptionCodeConstant.NODE_REAL_IS_EXIST);
                }

            });
        }
        //判断修改的节点id是否有问题 一个廊桥一个非廊桥
        Long startSafeguardsNodeId = fsBaseSafeguardsGroupDTO.getStartSafeguardsNodeId();
        Long ednSafeguardsNodeId = fsBaseSafeguardsGroupDTO.getEdnSafeguardsNodeId();
        FsBusSafeguardsNode startFsBusSafeguardsNode = fsBusSafeguardsNodeMapper.selectById(startSafeguardsNodeId);
        FsBusSafeguardsNode endFsBusSafeguardsNode = fsBusSafeguardsNodeMapper.selectById(ednSafeguardsNodeId);
        if ((startFsBusSafeguardsNode.getLoungeBridgeAtrrDictVal().equals(AppConstants.LOUNGE_BRIDGE) &&
                endFsBusSafeguardsNode.getLoungeBridgeAtrrDictVal().equals(AppConstants.NON_LOUNGE_BRIDGE))
                || startFsBusSafeguardsNode.getLoungeBridgeAtrrDictVal().equals(AppConstants.NON_LOUNGE_BRIDGE) &&
                endFsBusSafeguardsNode.getLoungeBridgeAtrrDictVal().equals(AppConstants.LOUNGE_BRIDGE)) {
            throw new BusinessException(SignBusinessExceptionCodeConstant.UPDATE_ERROR);
        }
        fsBaseSafeguardsGroupMapper.updateById(fsBaseSafeguardsGroup);
        //签单同步关联节点数据
        iBillItemClient.updateItemByNode(fsBaseSafeguardsGroupDTO.getId(),
                fsBaseSafeguardsGroupDTO.getStartSafeguardsNodeId(),
                fsBaseSafeguardsGroupDTO.getEdnSafeguardsNodeId());
    }

    @Resource
    private DragSort<FsBusSafeguardsNode, FsBusSafeguardsNodeMapper> dragSort;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sort(List<FsBusSafeguardsNode> vos) {
        dragSort.sequentialRemake(vos, fsBusSafeguardsNodeMapper, "getSortNum", "setSortNum");
    }

    @Override
    public FsSafeguardsNodeListVO listBaseNodes() {
        List<FsGlobalSafeguardsNodeVO> globalList = fsGlobalSafeguardsNodeService.list(new FsGlobalSafeguardsNodeQueryDTO());
        List<FsBusSafeguardsNodeVO> busList = fsBusSafeguardsNodeMapper.list("");
        FsSafeguardsNodeListVO fsSafeguardsNodeListVO = new FsSafeguardsNodeListVO();
        fsSafeguardsNodeListVO.setBusList(busList);
        fsSafeguardsNodeListVO.setGlobalList(globalList);
        return fsSafeguardsNodeListVO;
    }

    @Override
    @DataSource
    public List<DropDownMenuVO> listSystemNodeDropDown(String queryStr) {
        return globalSafeguardsNodeMapper.listSystemNodeDropDown(queryStr);
    }

    @Override
    public List<FsBusSafeguardsNodeVO> listByStatus() {
        LambdaQueryWrapper<FsBusSafeguardsNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FsBusSafeguardsNode::getStatus, 1);
        queryWrapper.eq(FsBusSafeguardsNode::getDeleted ,DeletedEnum.NORMAL.getValue() );
        List<FsBusSafeguardsNode> fsBusSafeguardsNodes = fsBusSafeguardsNodeMapper.selectList(queryWrapper);
        return ObjectUtils.copyBeans(fsBusSafeguardsNodes, FsBusSafeguardsNodeVO.class);
    }

    @Override
    public String getNodeName(Long id) {
        QueryWrapper<FsBusSafeguardsNode> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0).and(fsBusSafeguardsNodeQueryWrapper -> fsBusSafeguardsNodeQueryWrapper.eq("id", id));
        FsBusSafeguardsNode fsBusSafeguardsNode = baseMapper.selectOne(queryWrapper);
        if (ObjectUtils.isEmpty(fsBusSafeguardsNode)) {
            throw new BusinessException(CommonErrors.SERVICE_UNAVAILABLE);
        }
        return fsBusSafeguardsNode.getNodeName();
    }

    @Override
    public Map<Long, String> getNodeNameAll() {
        QueryWrapper<FsBusSafeguardsNode> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0);
        List<FsBusSafeguardsNode> fsBusSafeguardsNodes = baseMapper.selectList(queryWrapper);
        HashMap<Long, String> map = new HashMap<>();
        if (CollUtil.isNotEmpty(fsBusSafeguardsNodes)) {
            fsBusSafeguardsNodes.forEach(fsBusSafeguardsNode -> map.put(fsBusSafeguardsNode.getId(), fsBusSafeguardsNode.getNodeName()));
        }
        return map;
    }
}

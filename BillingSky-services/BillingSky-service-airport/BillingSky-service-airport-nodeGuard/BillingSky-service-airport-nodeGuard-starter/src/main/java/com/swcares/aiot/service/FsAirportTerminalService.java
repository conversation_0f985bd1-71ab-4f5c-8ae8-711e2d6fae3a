package com.swcares.aiot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aiot.core.dto.FsAirportTerminalPagedDTO;
import com.swcares.aiot.core.dto.FsAirportTerminalQueryDTO;
import com.swcares.aiot.core.entity.FsAirportTerminal;
import com.swcares.aiot.core.vo.FsAirportTerminalVO;

import java.util.List;

/**
 * ClassName：com.swcares.aiot.node.safeguards.service.FsAirportTerminalService <br>
 * Description：航站楼表 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
public interface FsAirportTerminalService extends IService<FsAirportTerminal> {

    /**
     * Title：logicRemoveById <br>
     * Description：逻辑删除 <br>
     * author：code-generator <br>
     * date：2022-03-02 <br>
     *
     * @param id 主键id<br>
     * @return <br>
     */
    boolean logicRemoveById(Long id);

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：code-generator <br>
     * date：2022-03-02 <br>
     *
     * @param dto 查询对象<br>
     * @return <br>
     */
    IPage<FsAirportTerminalVO> page(FsAirportTerminalPagedDTO dto);

    /**
     * Title:  saveNoRepeat<br>
     * Description: 保存不重复的数据 <br>
     * author 周扬
     * date：2022-03-02 <br>
     *
     * @param entity 保存对象<br>
     * @return boolean  <br>
     */
    boolean saveNoRepeat(FsAirportTerminal entity);


    /**
     * Title：page <br>
     * Description：航站楼表_列表查询 <br>
     * author：code-generator <br>
     * date：2022-03-02 <br>
     *
     * @param dto 查询对象<br>
     * @return <br>
     */
    List<FsAirportTerminalVO> list(FsAirportTerminalQueryDTO dto);


    String getNo(Long id);

    /**
     * Title：selectAll <br>
     * Description：查询所有航站楼 <br>
     * author ：巫鹏飞 <br>
     * date：2022/3/25 17:29 <br>
     *
     * @return : null
     */
    List<FsAirportTerminalVO> selectAll();

    /**
     * Title: updateNoRepeat <br>
     * Description:  更新航站楼，出现重复后提示 <br>
     * author 周扬  <br>
     * date 2022/4/22 9:52<br>
     *
     * @param fsAirportTerminal 航站楼对象
     * @return java.lang.Boolean
     */
    Boolean updateNoRepeat(FsAirportTerminal fsAirportTerminal);
}

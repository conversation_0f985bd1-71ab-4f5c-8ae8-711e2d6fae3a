package com.swcares.aiot.core.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.swcares.aiot.enums.DeletedEnum;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * ClassName：com.swcares.base.flight.api.entity.AtcSafeguardsData <br>
 * Description：atc保障数据表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-09-06 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="AtcSafeguardsData", description="atc保障数据表")
public class AtcSafeguardsData extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "航班id")
    private Long flightId;

    @ApiModelProperty(value = "航班号")
    private String flightNum;

    @ApiModelProperty(value = "航班日期")
    private String exeDate;

    @ApiModelProperty(value = "起飞机场")
    private String depAirport;

    @ApiModelProperty(value = "目的地机场")
    private String destinationAirport;

    @ApiModelProperty(value = "节点代码（英文名）")
    private String nodeCode;

    @ApiModelProperty(value = "节点中文名")
    private String nodeNameCn;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "推送次数")
    private Integer pushNumber;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "删除标识", hidden = true)
    private DeletedEnum deleted;
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "部门ID", hidden = true)
    private Long deptId;

}

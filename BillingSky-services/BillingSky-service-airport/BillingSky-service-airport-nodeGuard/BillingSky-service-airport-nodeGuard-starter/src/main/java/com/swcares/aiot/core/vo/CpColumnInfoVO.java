package com.swcares.aiot.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Value;
import org.springframework.validation.annotation.Validated;

import java.io.Serializable;
/**
 * ClassName：com.swcares.aiot.node.safeguards.vo.CpColumnInfoVO <br>
 * Description：展示列返回展示对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CpColumnInfoVO", description="展示列")
public class CpColumnInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "显示名")
    private String name;

    @ApiModelProperty(value = "属性名")
    private String attrName;

    @ApiModelProperty(value = "初始默认列(1：取消 0：默认)")
    private Long initialDefaultColumn;

    @ApiModelProperty("禁止取消列(1：可以取消 0：禁止取消)")
    private Long forbidCancellingColumns;

    @ApiModelProperty(value = "中间表id")
    private Long relaId;

    @ApiModelProperty(value="用户ID")
    private Long userId;

    @ApiModelProperty(value = "资源地址")
    private String resourceUrl;

}

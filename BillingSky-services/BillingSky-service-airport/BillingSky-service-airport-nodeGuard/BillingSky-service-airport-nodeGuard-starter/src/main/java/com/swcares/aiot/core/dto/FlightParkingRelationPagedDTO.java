package com.swcares.aiot.core.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：dto.FlightParkingPagedDTO <br>
 * Description：机位表 分页数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="FlightParkingPagedDTO", description="机位表")
public class FlightParkingRelationPagedDTO extends PagedDTO{

    private static final long serialVersionUID = 1L;
    /**
     * Description：机位号 <br>
     */
    private Integer aircraftNumber;

}

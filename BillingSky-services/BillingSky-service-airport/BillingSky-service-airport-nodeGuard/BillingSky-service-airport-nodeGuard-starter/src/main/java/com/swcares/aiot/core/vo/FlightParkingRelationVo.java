package com.swcares.aiot.core.vo;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FlightParkingRelationVo <br>
 * Package：dto <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 * 机位对应关系vo
 * <AUTHOR> <br>
 * date 2022年 03月06日 17:03 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FlightParkingRelationVo", description="机位表")
public class FlightParkingRelationVo implements BaseDTO, Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "机场三字码")
    private String airportCode;

    @ApiModelProperty(value = "机位号")
    private String aircraftNumber;

    @ApiModelProperty(value = "机位类型")
    private String fkAircraftType;

    @ApiModelProperty(value = "廊桥id")
    private Long fkCorridorBridge;

    @ApiModelProperty(value = "廊桥编号")
    private String fkCorridorBridgeNo;

    @ApiModelProperty(value = "行李转盘id")
    private Long fkBaggageTurntable;

    @ApiModelProperty(value = "行李转盘编号")
    private String fkBaggageTurntableNo;

    @ApiModelProperty(value = "登机口id")
    private String fkBoardingGate;

    @ApiModelProperty(value = "登机口编号")
    private String fkBoardingGateNo;


}

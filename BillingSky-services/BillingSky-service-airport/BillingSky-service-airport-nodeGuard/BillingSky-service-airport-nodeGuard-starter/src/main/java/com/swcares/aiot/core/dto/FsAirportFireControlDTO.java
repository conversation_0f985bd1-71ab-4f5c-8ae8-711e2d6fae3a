package com.swcares.aiot.core.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ClassName：com.swcares.aiot.node.safeguards.dto.FsAirportFireControlDTO <br>
 * Description：机场消防表 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FsAirportFireControlDTO", description="机场消防表")
public class FsAirportFireControlDTO  implements BaseDTO, Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "消防站名称")
    @NotNull
    @Size(min=1,max=50)
    private String stationName;

    @ApiModelProperty(value = "消防保障等级")

    private String stationSupportLevel;

    @ApiModelProperty(value = "消防站类型")

    private String stationType;

    @ApiModelProperty(value = "经度")

    private BigDecimal longitude;

    @ApiModelProperty(value = "纬度")

    private BigDecimal latitude;

    @ApiModelProperty(value = "消防员数量")

    private Integer firemenNumber;

    @ApiModelProperty(value = "消防车数量")

    private Integer fireBusNumber;

    @ApiModelProperty(value = "应急救援网格图")

    private String photo;

    @ApiModelProperty(value = "图片名称")

    private String photoName;

    @ApiModelProperty(value = "机场三字码")
    private String airportCode;

    @ApiModelProperty(value = "图片id")
    private Long photoId;

}

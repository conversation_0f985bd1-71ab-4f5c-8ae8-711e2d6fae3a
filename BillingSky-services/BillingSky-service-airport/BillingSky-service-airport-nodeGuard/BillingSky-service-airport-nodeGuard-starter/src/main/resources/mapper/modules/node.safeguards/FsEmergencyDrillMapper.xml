<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.mapper.FsEmergencyDrillMapper">

    <select id="page" resultType="com.swcares.aiot.core.vo.FsEmergencyDrillVO">
        select * from fs_emergency_drill
        <where>
            deleted = 0
            <if test="dto.drillName != null">
                and drill_name like concat('%',#{dto.drillName},'%')
            </if>
        </where>
    </select>

    <select id="list" resultType="com.swcares.aiot.core.vo.FsEmergencyDrillVO">
        select * from fs_emergency_drill
        <where>
            and deleted = 0
        </where>
    </select>

</mapper>

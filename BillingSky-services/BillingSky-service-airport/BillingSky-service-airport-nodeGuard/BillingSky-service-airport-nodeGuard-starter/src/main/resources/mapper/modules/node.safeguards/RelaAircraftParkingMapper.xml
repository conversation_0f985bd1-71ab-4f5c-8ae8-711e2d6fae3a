<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.mapper.RelaAircraftParkingMapper">

    <update id="updateByAircraftSafeguardsId">
        update fs_rela_aircraft_parking set aircraft_parking = #{dto.aircraftParking} , aircraft_far = #{dto.aircraftFar}
        where
        aircraft_safeguards_id = #{dto.aircraftSafeguardsId}
    </update>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>BillingSky-service-airport-signature</artifactId>
        <groupId>com.swcares.aiot</groupId>
        <version>2.30.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>BillingSky-service-airport-signature-core</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.swcares.baseframe</groupId>
            <artifactId>common-base</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aiot</groupId>
            <artifactId>BillingSky-common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.worm</groupId>
            <artifactId>worm-toolkit-mybatisplusGen</artifactId>
        </dependency>
    </dependencies>
</project>

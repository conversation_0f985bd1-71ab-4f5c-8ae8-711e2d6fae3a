package com.swcares.aiot;


import com.worm.MybatisPlusGenProperties;
import com.worm.MybatisPlusGenUtils;

import java.util.Collections;

/**
 * <AUTHOR>
 */
public class GenDbCode {
    public static void main(String[] args) {
        MybatisPlusGenProperties mybatisPlusGenProperties = new MybatisPlusGenProperties();
        // 开启自动生成
        mybatisPlusGenProperties.setEnable(true);
        // 输出目录
        mybatisPlusGenProperties.setOutputPath("./BillingSky-services/BillingSky-service-airport/BillingSky-service-airport-flight/BillingSky-service-airport-flight-core");
        // 数据库名
        mybatisPlusGenProperties.setDbName("bc-flight-system-test");
        // 要排除的表结构
//        mybatisPlusGenProperties.setExcludeTableNames(new String[]{
//                "flyway_schema_history",
//                "QRTZ_BLOB_TRIGGERS",
//                "QRTZ_CALENDARS",
//                "QRTZ_CRON_TRIGGERS",
//                "QRTZ_FIRED_TRIGGERS",
//                "QRTZ_JOB_DETAILS",
//                "QRTZ_LOCKS",
//                "QRTZ_PAUSED_TRIGGER_GRPS",
//                "QRTZ_SCHEDULER_STATE",
//                "QRTZ_SIMPLE_TRIGGERS",
//                "QRTZ_SIMPROP_TRIGGERS",
//                "QRTZ_TRIGGERS"
//
//        });
        mybatisPlusGenProperties.setIncludeTableNames(Collections.singletonList("p_flight_record"));
        MybatisPlusGenUtils.rebuild(mybatisPlusGenProperties);
    }
}

package com.swcares.aiot.core.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName：com.swcares.base.flight.api.dto.BusSelectorItemDTO <br>
 * Description：签单数据项选择器内容表 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-10-08 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="BusSelectorItemDTO", description="签单数据项选择器内容表")
public class BusSelectorItemDTO  implements BaseDTO, Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "选项名称")
    private String selectorItemName;

    @ApiModelProperty(value = "是否选择;(1-已选择,0-未选择)")
    private Boolean status;

    @ApiModelProperty(value = "签单数据项详情id")
    private Long busBillItemId;

}

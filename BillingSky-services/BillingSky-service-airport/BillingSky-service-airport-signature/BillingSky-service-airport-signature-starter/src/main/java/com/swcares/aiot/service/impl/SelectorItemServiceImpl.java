package com.swcares.aiot.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aiot.core.entity.SelectorItem;
import com.swcares.aiot.mapper.SelectorItemMapper;
import com.swcares.aiot.service.SelectorItemService;
import org.springframework.stereotype.Service;


/**
 * ClassName：com.swcares.base.flight.api.service.impl.SelectorItemServiceImpl <br>
 * Description：选择器内容表 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-10-08 <br>
 * @version v1.0 <br>
 */
@Service
public class SelectorItemServiceImpl extends ServiceImpl<SelectorItemMapper, SelectorItem> implements SelectorItemService {


}

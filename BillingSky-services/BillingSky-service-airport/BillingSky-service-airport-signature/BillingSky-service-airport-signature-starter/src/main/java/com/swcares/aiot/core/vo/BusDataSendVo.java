package com.swcares.aiot.core.vo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.swcares.aiot.enums.DeletedEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.sign.bill.vo.BillBusDataSendVo
 * Description：
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2023/11/13 15:58
 * @version v1.0
 */
@Data
public class BusDataSendVo {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty(value = "起降标识(ARR,DEP,ARR-DEP)")
    @TableField(updateStrategy = FieldStrategy.IGNORED) // 解决更新过滤掉null字段
    private String departureArriveStatus;

    @ApiModelProperty(value = "进港航班日期")
    @TableField(updateStrategy = FieldStrategy.IGNORED) // 解决更新过滤掉null字段
    private LocalDateTime arriveFlightDate;

    @ApiModelProperty(value = "出港航班日期")
    @TableField(updateStrategy = FieldStrategy.IGNORED) // 解决更新过滤掉null字段
    private LocalDateTime departureFlightDate;

    @ApiModelProperty(value = "航班号;航班号")
    private String flightNo;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "总金额")
    private Double sumSettlementAmount;

    @ApiModelProperty(value = "删除标识", hidden = true)
    private DeletedEnum deleted;

    @ApiModelProperty(value = "机场三字码")
    private String airportCode;

    @ApiModelProperty(value = "订单创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "机号")
    private String airplaneNumber;

    @ApiModelProperty(value = "说明;其他补充说明")
    private String remark;

    @ApiModelProperty(value = "签单pdf文件服务器地址")
    private String signPdfUrl;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private Long tenantId;

    @ApiModelProperty(value = "航空公司结算代码")
    private String settleCode;

    @ApiModelProperty(value = "航空公司二字码")
    private String airlineCode;

    @ApiModelProperty(value = "航空公司简称")
    private String airlineShortName;

    @ApiModelProperty(value = "航段")
    private String airline;

    List<BillBusDataItemVehicleVO> billBusDataItemVehicleVOList;
}

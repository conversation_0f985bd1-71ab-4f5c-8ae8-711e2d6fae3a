package com.swcares.aiot.core.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：BillItemSaveVO <br>
 * Package：com.swcares.aiot.sign.bill.vo <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 04月02日 10:02 <br>
 * @version v1.0 <br>
 */
@Data
public class BillItemSaveVO implements Serializable {
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "数据项ID")
    private Long itemId;

    @ApiModelProperty(value = "数据项名称;数据项名称")
    private String itemName;

    @ApiModelProperty(value = "数据格式;数据格式类型")
    private String dataFormat;

    @ApiModelProperty(value = "数据项分类;数据项分类")
    private String itemType;

    @ApiModelProperty(value = "模板id")
    private Long templateId;
}

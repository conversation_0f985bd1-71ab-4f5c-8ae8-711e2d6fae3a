package com.swcares.aiot.mapper;

import com.swcares.aiot.core.dto.BusFormulaDTO;
import com.swcares.aiot.core.entity.BusFormula;
import com.swcares.aiot.core.dto.BusFormulaQueryDTO;
import com.swcares.aiot.core.dto.BusFormulaPagedDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
/**
 * ClassName：com.swcares.aiot.sign.bill.mapper.BusFormulaMapper <br>
 * Description： Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2023-11-29 <br>
 * @version v1.0 <br>
 */
@Mapper
public interface BusFormulaMapper extends BaseMapper<BusFormula> {

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：liuzhiheng <br>
     * date：2023-11-29 <br>
     * @param dto
     * @param page <br>
     * @return <br>
     */
    IPage<BusFormula> page(@Param("dto") BusFormulaPagedDTO dto, Page<BusFormula> page);

    /**
    * Title : list<br>
    * Author : liuzhiheng<br>
    * Description :  _列表查询 <br>
    * @param dto  查询对象
    * date: 2023-11-29 <br>
    * return: List<BusFormulaVO>
     */
     List<BusFormula> list(@Param("dto")BusFormulaQueryDTO dto);

     Long selectFormulaIdByBusItem(@Param("busId")Long busId ,@Param("count")Integer count);

     List<BusFormula> listRepeatBusFormula(@Param("dto")BusFormulaDTO dto);
}

package com.swcares.aiot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aiot.core.dto.BillBusDataItemPagedDTO;
import com.swcares.aiot.core.dto.BillBusDataItemQueryDTO;
import com.swcares.aiot.core.entity.BillBusDataItem;
import com.swcares.aiot.core.vo.BillBusDataItemDetailsVO;
import com.swcares.aiot.core.vo.BillBusDataItemVO;
import com.swcares.aiot.core.vo.SignBusDataSettlementVO;

import java.util.List;

/**
 * ClassName：com.swcares.aiot.sign.bill.service.BillBusDataItemService <br>
 * Description：签单数据项详情表 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022-03-09 <br>
 * @version v1.0 <br>
 */
public interface BillBusDataItemService extends IService<BillBusDataItem> {

    /**
     * Title：logicRemoveById <br>
     * Description：逻辑删除 <br>
     * author：code-generator <br>
     * date：2022-03-09 <br>
     *
     * @param id 主键id<br>
     * @return <br>
     */
    boolean logicRemoveById(Long id);

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：code-generator <br>
     * date：2022-03-09 <br>
     *
     * @param dto 查询对象<br>
     * @return <br>
     */
    IPage<BillBusDataItemDetailsVO> page(BillBusDataItemPagedDTO dto);

    /**
     * Title:  saveNoRepeat<br>
     * Description: 保存不重复的数据 <br>
     *
     * @param entity 保存对象<br>
     * @return boolean  <br>
     * author 周扬
     * date：2022-03-09 <br>
     */
    boolean saveNoRepeat(BillBusDataItem entity);


    /**
     * Title：page <br>
     * Description：签单数据项详情表_列表查询 <br>
     * author：code-generator <br>
     * date：2022-03-09 <br>
     *
     * @param dto 查询对象<br>
     * @return <br>
     */
    List<BillBusDataItemVO> list(BillBusDataItemQueryDTO dto);

    /**
     * Title：selectSignItemByUpdateTime <br>
     * Description： 根据更新时间查询签单数据项详情<br>
     *
     * @param updateStartTime : 更新时间
     * author ：罗雪锋 <br>
     * date：2022/6/20 11：05 <br>
     * @return : List<SignBusDataSettlementVO>
     */
    List<SignBusDataSettlementVO> selectSignItemByUpdateTime(String updateStartTime, String updateEndTime);

    /**
     * Title: getItemName <br>
     * Description: 获取数据项名称 <br>
     * author wupengfei  <br>
     * date 2022/8/28 15:04<br>
     *
     * @return null
     */
    List<String> getItemName();

    /**
     * Title: getServiceDept <br>
     * Description: 获取服务部门 <br>
     * author wupengfei  <br>
     * date 2022/8/28 15:19<br>
     *
     * @return null
     */
    List<String> getServiceDept();

    /**
     * Title: getInputPerson <br>
     * Description: 获取录入人 <br>
     * author wupengfei  <br>
     * date 2022/8/28 15:50<br>
     *
     * @return null
     */
    List<String> getInputPerson();

    /**
     * Title: getBillType <br>
     * Description: 获取正在启用的中的签单类型。 <br>
     * author wupengfei  <br>
     * date 2022/10/9 14:30<br>
     *
     * @return null
     */
    List<String> getBillType();

}

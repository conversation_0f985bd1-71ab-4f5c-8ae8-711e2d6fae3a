package com.swcares.aiot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;
import com.swcares.aiot.core.entity.BillRelaTemplateItem;
import com.swcares.aiot.core.vo.BillRelaTemplateItemVO;
import com.swcares.aiot.core.dto.BillRelaTemplateItemQueryDTO;
import com.swcares.aiot.core.dto.BillRelaTemplateItemPagedDTO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * ClassName：com.swcares.aiot.sign.bill.service.BillRelaTemplateItemService <br>
 * Description：签单模板数据项表 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-09 <br>
 * @version v1.0 <br>
 */
public interface BillRelaTemplateItemService extends IService<BillRelaTemplateItem> {

    /**
     * Title：logicRemoveById <br>
     * Description：逻辑删除 <br>
     * author：code-generator <br>
     * date：2022-03-09 <br>
     * @param id  主键id<br>
     * @return <br>
     */
    boolean logicRemoveById(Long id);

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：code-generator <br>
     * date：2022-03-09 <br>
     * @param dto 查询对象<br>
     * @return <br>
     */
    IPage<BillRelaTemplateItemVO> page(BillRelaTemplateItemPagedDTO dto);

    /**
     * Title:  saveNoRepeat<br>
     * Description: 保存不重复的数据 <br>
     * author 周扬
     * date：2022-03-09 <br>
     * @param entity   保存对象<br>
     * @return boolean  <br>
     */
    boolean saveNoRepeat(BillRelaTemplateItem entity);


    /**
     * Title：page <br>
     * Description：签单模板数据项表_列表查询 <br>
     * author：code-generator <br>
     * date：2022-03-09 <br>
     * @param dto 查询对象<br>
     * @return <br>
     */
    List<BillRelaTemplateItemVO> list(BillRelaTemplateItemQueryDTO dto);
}

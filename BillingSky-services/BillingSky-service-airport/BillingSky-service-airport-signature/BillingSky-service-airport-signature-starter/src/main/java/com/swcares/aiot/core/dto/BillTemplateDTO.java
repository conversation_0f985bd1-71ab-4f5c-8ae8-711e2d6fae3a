package com.swcares.aiot.core.dto;

import com.swcares.aiot.core.AppRegexpConstants;
import com.swcares.aiot.core.vo.BillItemSaveVO;
import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.sign.bill.dto.BillTemplateDTO <br>
 * Description：签单模板表 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022-03-09 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "BillTemplateDTO", description = "签单模板表")
public class BillTemplateDTO implements BaseDTO, Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "签单名称;签单名称")
    @NotNull
    @Length(min = 1, max = 10)
    @Pattern(regexp = AppRegexpConstants.ENGLISH_NUMBER_CHINESE)
    private String signName;

    @ApiModelProperty(value = "签单类型;签单类型")
    @NotNull
    private String signType;

    @ApiModelProperty(value = "签字（1-是机长 0-是乘务长签字 2-航司人员签字）")
    private String sign;

    @ApiModelProperty(value = "状态;状态（0-启用，1-禁用）")
    private String status;

    @ApiModelProperty(value = "数据项")
    private List<BillItemSaveVO> billItemSaveVos;

    @ApiModelProperty(value = "归属部门ID")
    private Long deptId;

}

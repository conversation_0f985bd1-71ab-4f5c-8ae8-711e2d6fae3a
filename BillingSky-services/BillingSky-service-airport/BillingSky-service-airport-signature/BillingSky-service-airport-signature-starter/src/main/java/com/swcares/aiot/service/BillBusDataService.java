package com.swcares.aiot.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aiot.core.dto.*;
import com.swcares.aiot.core.entity.AircraftAirlineInfoVo;
import com.swcares.aiot.core.entity.BillBusData;
import com.swcares.aiot.core.vo.BillBusDataItemVO;
import com.swcares.aiot.core.vo.BillBusDataVO;
import com.swcares.aiot.core.vo.BillBusDataVehicleVO;
import com.swcares.aiot.core.vo.SignBusDataSettlementVO;
import com.swcares.aiot.dto.MqAircraftSafeguardsInfoDTO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.sign.bill.service.BillBusDataService <br>
 * Description：签单数据表 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Date 2022-03-09 <br>
 *
 * <AUTHOR> <br>
 * @version v1.0 <br>
 */
public interface BillBusDataService extends IService<BillBusData> {

    /**
     * Title：logicRemoveById <br>
     * Description：逻辑删除 <br>
     * author：code-generator <br>
     * date：2022-03-09 <br>
     *
     * @param id 主键id<br>
     * @return <br>
     */
    boolean logicRemoveById(Long id);

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：code-generator <br>
     * date：2022-03-09 <br>
     *
     * @param dto 查询对象<br>
     * @return <br>
     */
    IPage<BillBusDataVO> page(BillBusDataPagedDTO dto);

    /**
     * Title：vehiclePage <br>
     * Description： 车辆签单分页查询<br>
     * author：dengping <br>
     * date：2022年10月12日  <br>
     *
     * @param dto :
     * @return <br>
     */
    IPage<BillBusDataVehicleVO> vehiclePage(BillBusDataVehiclePagedDTO dto);


    /**
     * Title：page <br>
     * Description：签单数据表_列表查询 <br>
     * author：code-generator <br>
     * date：2022-03-09 <br>
     *
     * @param dto 查询对象<br>
     * @return <br>
     */
    List<BillBusDataVO> list(BillBusDataQueryDTO dto);

    /**
     * Title：saveData <br>
     * Description： 新增签单数据<br>
     * author ：巫鹏飞 <br>
     * date：2022/3/18 16:07 <br>
     *
     * @param dto 航班数据
     * @return : null
     */
    boolean saveData(SignFsBusAircraftSafeguardsInfoDTO dto);


    BillBusDataVO selectId(Long id);

    /**
     * Title：selectSignBillData <br>
     * Description： 查询签单数据<br>
     * author ：巫鹏飞 <br>
     * date：2022/3/22 13:23 <br>
     *
     * @param billBusDataDTO : dto
     * @return : null
     */
    BillBusDataVO selectSignBillData(BillBusDataEntryDTO billBusDataDTO);

    /**
     * Title：updateSignBillDataItem <br>
     * Description： 修改数据项<br>
     * author ：巫鹏飞 <br>
     * date：2022/3/22 16:17 <br>
     *
     * @param billBusDataDTO :dto
     */
    void updateSignBillDataItem(BillBusDataEntryDTO billBusDataDTO);

    /**
     * Title：updateOrSave <br>
     * Description： 修改或新增签单数据<br>
     * author ：巫鹏飞 <br>
     * date：2022/3/24 15:13 <br>
     *
     * @param dto :
     */
    void updateOrSave(BillBusDataDTO dto);

    /**
     * Title：saveItem <br>
     * Description：保障节点新增数据项 <br>
     * author ：巫鹏飞 <br>
     * date：2022/3/28 14:01 <br>
     *
     * @param dto :
     */
    void saveItem(BillBusDataItemInsertDTO dto);

    /**
     * Title：saveSign <br>
     * Description： 新增签字记录<br>
     * author ：朱程宇 <br>
     * date：2022/3/28 10:35 <br>
     *
     * @param billBusDataDTO :
     */
    void saveSign(BillBusDataDTO billBusDataDTO);

    /**
     * Title：deleteItem <br>
     * Description：保障节点删除数据项 <br>
     * author ：巫鹏飞 <br>
     * date：2022/3/28 18:34 <br>
     *
     * @param dto :
     */
    void deleteItem(BillBusDataItemInsertDTO dto);

    /**
     * Title：updateFinishItem <br>
     * Description： 签到完成数据补充<br>
     * author ：巫鹏飞 <br>
     * date：2022/3/28 19:12 <br>
     *
     * @param dto :
     */
    void updateFinishItem(Long id, BillBusDataItemDTO dto);


    BillBusDataVO getData(Long id);

    /**
     * Title：selectCout <br>
     * Description：查找签单数量 <br>
     * author ：巫鹏飞 <br>
     * date：2022/4/6 16:32 <br>
     *
     * @param dto :
     * @return : null
     */
    Integer selectCount(UserCenterBillBusDataDTO dto);

    /**
     * Title：vehicleExportPdf <br>
     * Description：车辆签单导出pdf <br>
     * author：赵子骄 <br>
     * date：2022年11月28日 下午2:38:09 <br>
     *
     * @param dto ID集合-导出选择数据，无ID集合-导出查询数据
     */
    void vehicleExportPdf(BillBusDataVehiclePagedDTO dto);

    /**
     * Title：select <br>
     * Description：根据航班日期查找对应的电子签单数据 <br>
     * author ：巫鹏飞 <br>
     * date：2022/4/19 14:44 <br>
     *
     * @param dto :
     * @return : null
     */
    List<BillBusDataVO> select(UserCenterBillBusDataDTO dto);

    /**
     * Title:select<br>
     * Description：根据航班号模糊匹配 <br>
     * author ：曹文韬 <br>
     *
     * @param dto :
     */
    List<BillBusDataVO> getLikeNo(BillBusDataPagedDTO dto);

    /**
     * Title：selectStatus <br>
     * Description： 查找签单状态<br>
     * author ：巫鹏飞 <br>
     * date：2022/4/26 14:23 <br>
     *
     * @param billBusDataDTO :
     * @return : null
     */
    String selectStatus(BillBusDataEntryDTO billBusDataDTO);

    /**
     * Title：checkNodeBySign <br>
     * Description：检查该节点是否被引用 <br>
     * author ：巫鹏飞 <br>
     * date：2022/5/5 20:17 <br>
     *
     * @param nodeId :
     * @return : null
     */
    Boolean checkNodeBySign(Long nodeId);

    /**
     * Title：selectSignBillDataByStatus <br>
     * Description： 签字页查询签单记录<br>
     * author ：巫鹏飞 <br>
     * date：2022/5/7 15:57 <br>
     *
     * @param billBusDataDTO :
     * @return : null
     */
    BillBusDataVO selectSignBillDataByStatus(BillBusDataEntryDTO billBusDataDTO);

    /**
     * Title：updateSignByChangeAircraft <br>
     * Description： 改变飞机保障事件改变签单<br>
     * author ：巫鹏飞 <br>
     * date：2022/5/17 19:48 <br>
     *
     * @param aircraftSafeguardsInfoDTO : 飞机保障信息
     */
    void updateSignByChangeAircraft(MqAircraftSafeguardsInfoDTO aircraftSafeguardsInfoDTO);


    /**
     * 罗雪锋
     * 根据传入的两个时间判断航班日期在这两个时间之间对应签单数据
     *
     * @param updateStartTime :
     * @param updateEndTime   :
     * @param itemId          :
     * @return List<SignBusDataSettlementVO>
     */
    List<SignBusDataSettlementVO> selectSignItemByFlightDate(String updateStartTime, String updateEndTime, Long itemId);

    /**
     * Title: getAircraftNo <br>
     * Description: 获取机号 <br>
     * author wupengfei  <br>
     * date 2022/8/28 14:57<br>
     *
     * @return null
     */
    List<String> getAircraftNo();

    /**
     * Title: signTypeNewSign <br>
     * Description: 根据签单类型新建签单 <br>
     * author wupengfei  <br>
     * date 2022/10/10 11:18<br>
     *
     * @param signType 签单类型
     * @return null
     */
    BillBusDataVO signTypeNewSign(String signType);

    /**
     * Title: moneyReplenish <br>
     * Description: 金额补充 <br>
     * author wupengfei  <br>
     * date 2022/10/11 10:49<br>
     *
     * @param money : 金额补充
     */
    void moneyReplenish(Long id, Double money);

    /**
     * Title: deletedSignData <br>
     * Description:  <br>
     * author wupengfei  <br>
     * date 2022/10/11 11:46<br>
     *
     * @param ids id集合
     * @return null
     */
    List<String> deletedSignData(List<Long> ids);

    /**
     * Title: getBillBusDataVOById <br>
     * Description: 根据签单id获取签单数据 <br>
     * author wupengfei  <br>
     * date 2022/10/11 15:35<br>
     *
     * @param id :
     * @return null
     */
    BillBusDataVO getBillBusDataVoById(Long id, String status);

    /**
     * Title: getBillBusDataVOByPermission <br>
     * Description: 根据签单id和用户权限获取签单数据 <br>
     * author wupengfei  <br>
     * date 2022/10/12 14:53<br>
     *
     * @param id 用户id
     * @return null
     */
    BillBusDataVO getBillBusDataVoByPermission(Long id);

    /**
     * Description：权限校验，判断是否有新建删除签单权限 <br>
     */
    Boolean permissionToCheck(String signType);

    /**
     * Title: downloadPdf <br>
     * Description: 保障签单导出pdf <br>
     * author wupengfei  <br>
     * date 2022/11/25 10:50<br>
     *
     * @param dto :
     */
    void downloadPdf(BillBusDataDTO dto);

    /**
     * Title: getSumSettlementAmount <br>
     * Description: 获取签单总金额 <br>
     * author wupengfei  <br>
     * date 2022/12/19 9:33<br>
     *
     * @param dto :
     * @return null
     */
    Double getSumSettlementAmount(BillBusDataVehiclePagedDTO dto);

    /**
     * Title: getBillBusDataByFlightId <br>
     * Description: 根据航班id及航前航后标识查找签单数据 <br>
     * author wupengfei  <br>
     * date 2022/12/21 11:09<br>
     *
     * @param billBusDataDTO :
     * @return null
     */
    BillBusDataVO getBillBusDataByFlightId(BillBusDataEntryDTO billBusDataDTO);

    BillBusDataVO selectSignBillDataById(Long id);

    /**
     * Title: updateSignBillDataItemById <br>
     * Description: 修改签单数据详情 <br>
     * author wupengfei  <br>
     * date 2023/2/23 10:08<br>
     *
     * @param dto 签单数据详情
     * @return null
     */
    BillBusDataItemVO updateSignBillDataItemById(BillBusDataItemDTO dto);

    /**
     * Title: saveSignBillDataItemByTime <br>
     * Description: 新增签单数据项详情数据 <br>
     * author wupengfei  <br>
     * date 2023/2/23 11:33<br>
     *
     * @param dto 需要新增的数据
     * @return null
     */
    BillBusDataVO saveSignBillDataItemByTime(BillBusDataItemDTO dto);

    /**
     * Title: deletedSignBillDataItemByTime <br>
     * Description: 新增签单数据项详情数据 <br>
     * author wupengfei  <br>
     * date 2023/2/23 13:58<br>
     *
     * @param id 主键id
     * @return null
     */
    BillBusDataVO deletedSignBillDataItemByTime(Long id);

    /**
     * Title: updateSignBillDataRemark <br>
     * Description: 修改补充说明 <br>
     * author wupengfei  <br>
     * date 2023/2/23 14:21<br>
     *
     * @param dto :
     */
    void updateSignBillDataRemark(BillBusDataDTO dto);

    /**
     * Title: signatureAirport <br>
     * Description: 机场人员签字 <br>
     * author wupengfei  <br>
     * date 2023/2/24 9:24<br>
     *
     * @param dto :
     * @return null
     */
    LocalDateTime signatureAirport(BillBusSignatureDTO dto);

    /**
     * Title: signatureAirline <br>
     * Description: 航司人员签字 <br>
     * author wupengfei  <br>
     * date 2023/2/24 10:45<br>
     *
     * @param dto :
     * @return null
     */
    LocalDateTime signatureAirline(BillBusSignatureDTO dto);

    Long getFlightByBillId(Long id);

    String uploadPdfToMinio(Long pdfFlightId);


    AircraftAirlineInfoVo listAirlineInfoByRegno(String regNo, LocalDate effectiveDate);

    List<AircraftAirlineInfoVo> listAirlineInfoByAirlineShortName(String airlineShortName);

}

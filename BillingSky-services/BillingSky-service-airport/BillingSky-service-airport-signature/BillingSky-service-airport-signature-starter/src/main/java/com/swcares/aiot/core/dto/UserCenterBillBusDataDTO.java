package com.swcares.aiot.core.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：UserCenterBillBusDataDTO <br>
 * Package：com.swcares.aiot.sign.bill.dto <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 04月19日 14:52 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="UserCenterBillBusDataDTO", description="用户中心电子签单dto")
public class UserCenterBillBusDataDTO implements BaseDTO, Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "航班日期;航班日期")
    private LocalDateTime flightDate;

    @ApiModelProperty(hidden = true)
    private LocalDateTime startTime;

    @ApiModelProperty(hidden = true)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "签单状态;签单状态（1-保障信息录入，2-确认并签字）")
    private String status;

    @ApiModelProperty(value = "签单类型")
    @NotNull
    private String signType;

    @ApiModelProperty(value = "航空公司")
    private String airline;

}

package com.swcares.aiot.core.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：BillTemolateListVO <br>
 * Package：com.swcares.aiot.sign.bill.vo <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 04月22日 14:52 <br>
 * @version v1.0 <br>
 */
@Data
public class BillTemolateListVO {

    @ApiModelProperty(value = "中间表主键ID")
    private Long id;

    @ApiModelProperty(value = "数据项名称;数据项名称")
    private String itemName;

    @ApiModelProperty(value = "数据格式;数据格式类型")
    private String dataFormat;

    @ApiModelProperty(value = "分类排序序号;分类排序序号")
    private Integer typeSortNumber;

    @ApiModelProperty(value = "数据排序序号;数据排序序号")
    private Integer dataSortNumber;

    @ApiModelProperty(value = "签单模板ID;签单模板ID")
    private Long templateId;

    @ApiModelProperty(value = "签单内容项ID;签单内容项ID")
    private String itemId;
}

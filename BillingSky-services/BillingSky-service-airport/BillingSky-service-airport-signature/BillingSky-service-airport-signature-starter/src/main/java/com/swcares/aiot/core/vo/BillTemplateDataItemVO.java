package com.swcares.aiot.core.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：BillTemplateDataItemVO <br>
 * Package：com.swcares.aiot.sign.bill.vo <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 03月23日 9:41 <br>
 * @version v1.0 <br>
 */
@Data
public class BillTemplateDataItemVO implements Serializable {

    @ApiModelProperty(value = "中间表主键ID")
    private Long id;

    @ApiModelProperty(value = "数据项分类;数据项分类")
    @NotNull
    private String itemType;

    @ApiModelProperty(value = "数据项分类;数据项分类")
    @NotNull
    private Long typeSortNumber;

    @ApiModelProperty(value = "详情")
    private List<BillTemolateListVO> list;

}

package com.swcares.aiot.core.comm.config;

import com.swcares.aiot.core.cons.SignBillBusinessExceptionCode;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：ExecutorCofig <br>
 * Package：com.swcares.aiot.sign.bill.comm.config <br>
 * Copyright 2023 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * date 2023年 02月24日 10:21 <br>
 * @version v1.0 <br>
 */
@Configuration
public class ExecutorConfig {

    @Bean("sendMsgExecutor")// 自定义线程池1
    public Executor customExecutor1() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //核心池大小
        executor.setCorePoolSize(3);
        //最大线程数
        executor.setMaxPoolSize(6);
        //线程空闲时间
        executor.setKeepAliveSeconds(60);
        //队列程度
        executor.setQueueCapacity(10);
        //线程前缀名称
        executor.setThreadNamePrefix("sendMsgExecutor-1-");
        //配置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        // 允许销毁核心线程
        executor.setAllowCoreThreadTimeOut(true);
        executor.initialize();
        return executor;
    }

    @Bean("pdfDownloadExecutor")
    public Executor pdfDownloadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //核心池大小
        executor.setCorePoolSize(5);
        //最大线程数
        executor.setMaxPoolSize(10);
        //线程空闲时间
        executor.setKeepAliveSeconds(60);
        //队列程度
        executor.setQueueCapacity(30);
        //线程前缀名称
        executor.setThreadNamePrefix("pdfDownloadExecutor-1-");
        //配置拒绝策略
        executor.setRejectedExecutionHandler(new CusRejectedExecutionHandler());
        // 允许销毁核心线程
        executor.setAllowCoreThreadTimeOut(true);
        executor.initialize();
        return executor;
    }

    @NoArgsConstructor
    @Slf4j
    public static class CusRejectedExecutionHandler implements RejectedExecutionHandler {

        /**
         * Always throws RejectedExecutionException.
         *
         * @param r the runnable task requested to be executed
         * @param e the executor attempting to execute this task
         * @throws RejectedExecutionException always
         */
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
            log.error("线程池繁忙");
            throw new BusinessException(SignBillBusinessExceptionCode.SYSTEM_BUSY);
        }
    }

}

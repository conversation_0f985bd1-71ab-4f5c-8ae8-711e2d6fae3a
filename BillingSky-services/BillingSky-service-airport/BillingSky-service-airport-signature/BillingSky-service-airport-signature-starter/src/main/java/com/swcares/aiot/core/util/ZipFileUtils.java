package com.swcares.aiot.core.util;

import com.worm.hutool.HttpUtils;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * ClassName：com.swcares.aiot.sign.bill.util.ZipFileUtils <br>
 * Description：压缩 工具类 <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年11月28日 上午10:07:43 <br>
 * @version v1.0 <br>
 */
@Slf4j
public class ZipFileUtils {

    private ZipFileUtils() {
    }

    public static void zipFiles(List<File> srcfile, File zipFile) {
        byte[] buf = new byte[1024];
        // 获取输出流
        BufferedOutputStream bos = null;
        try {
            bos = new BufferedOutputStream(Files.newOutputStream(Paths.get(zipFile.getPath())));
        } catch (Exception e) {
            log.error("文件转换为输出流失败，请检查！ zipFile = {}", zipFile, e);
        }
        FileInputStream in = null;
        ZipOutputStream out = null;
        try {
            // ZipOutputStream类：完成文件或文件夹的压缩
            out = new ZipOutputStream(bos);
            for (File file : srcfile) {
                in = new FileInputStream(file);
                // 给列表中的文件单独命名
                out.putNextEntry(new ZipEntry(file.getName()));
                int len;
                while ((len = in.read(buf)) != -1) {
                    out.write(buf, 0, len);
                }
            }
            out.close();
            bos.close();
            log.info("压缩完成.");
        } catch (Exception e) {
            log.error(" 压缩文件时出现异常，请检查！srcfile = {} ", srcfile, e);
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (Exception e) {
                log.error("压缩失败! srcfile = {} ", srcfile, e);
            }
        }
    }

    /**
     * 功能:压缩多个文件，输出压缩后的zip文件流
     *
     * @param srcfile     ：源文件列表
     * @param zipFileName ：压缩后的文件名
     * @param response    :           Http响应
     */
    public static void zipFiles(List<File> srcfile, String zipFileName, HttpServletResponse response) throws IOException {
        byte[] buf = new byte[1024];
        // 获取输出流
        BufferedOutputStream bos = null;
        try {
            bos = new BufferedOutputStream(response.getOutputStream());
        } catch (IOException e) {
            log.error("获取response的输出流出现异常，请检查！", e);
        }
        FileInputStream in = null;
        ZipOutputStream out = null;
        try {
            response.reset(); // 重点突出
            // 不同类型的文件对应不同的MIME类型
            response.setContentType("application/x-msdownload");
            response.setCharacterEncoding("utf-8");
            response.setHeader(HttpUtils.HEADER_CONTENT_DISPOSITION, "attachment;filename=" + zipFileName + ".zip");

            // ZipOutputStream类：完成文件或文件夹的压缩
            out = new ZipOutputStream(bos);
            for (File file : srcfile) {
                in = new FileInputStream(file);
                // 给列表中的文件单独命名
                out.putNextEntry(new ZipEntry(file.getName()));
                int len;
                while ((len = in.read(buf)) != -1) {
                    out.write(buf, 0, len);
                }
            }
            out.close();
            bos.close();
            log.info("压缩完成.");
        } catch (Exception e) {
            log.error("压缩文件出现异常，请检查", e);
        } finally {
            if (in != null) {
                in.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }
}

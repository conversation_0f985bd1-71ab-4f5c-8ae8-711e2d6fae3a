package com.swcares.aiot.core.vo;

import com.swcares.aiot.core.entity.BillBusDataItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel(value = "SignBusDataSettlementVO")
public class SignBusDataSettlementVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "航班日期")
    private LocalDateTime flightDate;

    @ApiModelProperty(value = "飞机计划到达时间")
    private LocalDateTime planLandingDatetime;

    @ApiModelProperty(value = "飞机计划起飞时间")
    private LocalDateTime planTakeOffDatetime;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航前航后;航前航后标识")
    private String safeguardsType;

    @ApiModelProperty(value = "签单数据项")
    private List<BillBusDataItem> list;

    @ApiModelProperty(value = "是否过夜")
    private Boolean isStayOvernight;

    @ApiModelProperty(value = "机场三字码")
    private String airportCode;

    @ApiModelProperty(value = "签单pdf文件服务器地址")
    private String signPdfUrl;

    @ApiModelProperty(value = "到达航班Id")
    private Long arriveFlightId;

    @ApiModelProperty(value = "起飞航班ID")
    private Long takeOffFlightId;

    @ApiModelProperty(value = "旧到达航班Id")
    private Long oldArriveFlightId;

    @ApiModelProperty(value = "旧起飞航班ID")
    private Long oldTakeOffFlightId;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private Long tenantId;


}

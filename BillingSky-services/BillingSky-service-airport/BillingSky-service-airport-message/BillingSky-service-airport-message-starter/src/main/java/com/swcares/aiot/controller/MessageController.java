package com.swcares.aiot.controller;

import com.swcares.aiot.client.IMessageClient;
import com.swcares.aiot.common.cons.AppConstants;
import com.swcares.aiot.dto.MessageDTO;
import com.swcares.aiot.service.MessageService;
import com.swcares.aiot.dto.MessageNoSysDTO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * ClassName：com.swcares.aiot.message.center.controller.MessageController <br>
 * Description：消息表(待发送) 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-15 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/message")
@Api(tags = "消息表(待发送)接口")
@ApiVersion(AppConstants.SWAGGER_API_VERSION_INFO)
public class MessageController extends BaseController implements IMessageClient {
    @Resource
    private MessageService messageService;

    @PostMapping("/sys/save")
    @ApiOperation(value = "保存并发送内置消息")
    public BaseResult<Object> saveAndSendMsg(@RequestBody MessageDTO dto){
        boolean created = messageService.saveAndSendMsg(dto);
        if (!created) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }
    @PostMapping("/noSys/save")
    @ApiOperation(value = "保存并发送非内置消息")
    public BaseResult<Object> saveAndSendNoSysMsg(@RequestBody MessageNoSysDTO dto){
        messageService.saveAndSendNoSysMsg(dto);
        return ok();
    }
}

package com.swcares.aiot;

import cn.hutool.core.net.NetUtil;
import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：MessageCenterServiceApplication <br>
 * Package：com.swcares.aiot <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 03月02日 10:16 <br>
 * @version v1.0 <br>
 */
@Slf4j
@SpringBootApplication(scanBasePackages = "com.swcares")
@EnableCreateCacheAnnotation
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.swcares.common.api","com.swcares.aiot.**.client","com.swcares.components.api"})
public class AirportMessageServiceStarter {
    public static void main(String[] args) {
        SpringApplication.run(AirportMessageServiceStarter.class,args);
    }
}

package com.swcares.aiot.common.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.aiot.cons.MessageBusinessExceptionCode;
import com.swcares.aiot.message.center.vo.WebSocketMessageVo;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：WebSocketHandler <br>
 * Package：com.swcares.aiot.message.center.common.handler <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 04月10日 15:10 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class MyWebSocketHandler extends TextWebSocketHandler {
    /**
     * Description：本地session池，用于保存已经登录websocket的session <br>
     */
    private static final ConcurrentHashMap<String, WebSocketSession> MAP = new ConcurrentHashMap<>();

    /**
     * socket连接后触发，类似于@OnOpen
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        String userId = (String) session.getAttributes().get("userId");
        if (CharSequenceUtil.isNotEmpty(userId)) {
            MAP.put(userId, session);
            log.info("用户id为：{}，成功建立连接", session.getAttributes().get("userId"));
        }
    }

    /**
     * Description：接受socket消息，客户端发送消息是触发，通@OnMessage <br>
     */
    @Override
    public void handleMessage(@NotNull WebSocketSession session, WebSocketMessage<?> message) {
        log.info("收到来之客户端的消息：{}", message.getPayload());
    }

    @Override
    public void handleTransportError(WebSocketSession session, @NotNull Throwable exception) throws Exception {
        if (session.isOpen()) {
            session.close();
        }
        log.error("--------------------连接出错-----------------");
        MAP.remove(getClientId(session));
    }

    @Override
    public void afterConnectionClosed(@NotNull WebSocketSession session, @NotNull CloseStatus closeStatus) {
        log.info("连接已关闭：{}", closeStatus);
        MAP.remove(getClientId(session));
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * Title：getClientId <br>
     * Description：获取用户id <br>
     *
     * @param session:
     * @return : null
     * <AUTHOR> <br>
     * date：2022/4/10 16:10 <br>
     */
    private String getClientId(WebSocketSession session) {
        try {
            return (String) session.getAttributes().get("userId");
        } catch (Exception e) {
            throw new BusinessException(CommonErrors.SERVICE_UNAVAILABLE);
        }

    }

    /**
     * Title：sendMessageToUser <br>
     * Description： 发送消息给指定用户<br>
     * author ：巫鹏飞 <br>
     * date：2022/4/10 16:24 <br>
     *
     * @param messageVo :消息内容
     */
    public void sendMessageToUser(Set<Long> userId, WebSocketMessageVo messageVo) {
        if (CollUtil.isNotEmpty(userId)) {
            List<String> collect = userId.stream().map(aLong -> aLong + "").collect(Collectors.toList());
            collect.forEach(id -> {
                if (MAP.containsKey(id)) {
                    WebSocketSession webSocketSession = MAP.get(id);
                    if (webSocketSession.isOpen()) {
                        try {
                            log.info("发送给客户端的消息：{}", messageVo);
                            webSocketSession.sendMessage(new TextMessage(JSON.toJSONString(messageVo)));
                        } catch (IOException e) {
                            log.error("消息推送失败");
                            throw new BusinessException(MessageBusinessExceptionCode.SEND_MESSAGE_FAIL);
                        }
                    } else {
                        log.error("socket连接失败");
                    }
                }
            });
        }
    }
}

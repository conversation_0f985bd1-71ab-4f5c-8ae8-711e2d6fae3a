package com.swcares.aiot.client;

import com.swcares.aiot.fallback.AdviceSettingFallbackFactory;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.feign.FeignClientInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：MessageCenterServiceFeign <br>
 * Package：com.swcares.aiot.node.safeguards.feign <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 03月10日 16:08 <br>
 * @version v1.0 <br>
 */
@FeignClient(value = "message", contextId = "iAdviceSettingClient", fallbackFactory = AdviceSettingFallbackFactory.class, path = "/message/advice/setting", configuration = FeignClientInterceptor.class)
public interface IAdviceSettingClient {
    @PostMapping("/save/{id}/{deptIds}")
    BaseResult<Object> saveSetting(@PathVariable("id") Long ensureNodeId, @PathVariable("deptIds") List<Long> deptIds);

    @DeleteMapping("delete/adviceSetting/{id}")
    BaseResult<Object> deleteAdviceSetting(@PathVariable("id") Long id);
}

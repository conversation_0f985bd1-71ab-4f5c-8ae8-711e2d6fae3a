package com.swcares.aiot.message.center.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * ClassName：com.swcares.aiot.message.center.dto.RelaMessageUserDTO <br>
 * Description：消息用户中间表 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="RelaMessageUserDTO", description="消息用户中间表")
public class RelaMessageUserDTO  implements BaseDTO, Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "消息id")
    private Long msgId;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "是否阅读;0-已读，1-未读，默认为未读")
    private Integer isRead;

    @ApiModelProperty(value = "阅读时间")
    private LocalDateTime readTime;

}

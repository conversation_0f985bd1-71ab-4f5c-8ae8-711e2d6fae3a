package com.swcares.aiot.message.center.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.aiot.enums.DeletedEnum;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.aiot.message.center.entity.RelaAdviceRole <br>
 * Description：通知角色中间表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-02 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mc_rela_advice_role")
@ApiModel(value="RelaAdviceRole", description="通知角色中间表")
public class RelaAdviceRole extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "通知设置id")
    private Long adviceId;

    @ApiModelProperty(value = "角色id")
    private Long roleId;
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "删除标识", hidden = true)
    private DeletedEnum deleted;
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "部门ID", hidden = true)
    private Long deptId;


}

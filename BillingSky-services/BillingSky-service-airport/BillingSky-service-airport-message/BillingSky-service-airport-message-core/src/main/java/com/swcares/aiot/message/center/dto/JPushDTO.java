package com.swcares.aiot.message.center.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：JPushDTO <br>
 * Package：com.swcares.aiot.message.center.dto <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2022年 04月07日 11:06 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="JPushDTO", description="极光推送设备信息")
public class JPushDTO implements BaseDTO, Serializable {
    @ApiModelProperty(value = "设备注册id")
    private String registrationID;

    @ApiModelProperty(value = "用户id")
    private Long userId;


}

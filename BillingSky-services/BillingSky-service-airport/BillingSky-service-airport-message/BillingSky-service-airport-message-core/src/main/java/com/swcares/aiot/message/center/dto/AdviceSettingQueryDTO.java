package com.swcares.aiot.message.center.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName：com.swcares.aiot.message.center.dto.AdviceSettingQueryDTO <br>
 * Description：消息通知设置表 查询_数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-04-02 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="AdviceSettingQueryDTO", description="消息通知设置表")
public class AdviceSettingQueryDTO  implements BaseDTO, Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "保障节点id;保障节点根据基础配置自动增加，且都有：提前准备通知、延误预警通知、实际完成通知")
    private Long ensureNodeId;

    @ApiModelProperty(value = "固定提醒类型")
    private String remindType;

    @ApiModelProperty(value = "通知节点")
    private String adviceNode;

    @ApiModelProperty(value = "通知时间;提醒类型为非固定通知项目时")
    private Integer adviceTime;

    @ApiModelProperty(value = "一级排序序号;排序")
    private Integer oneSortNum;

    @ApiModelProperty(value = "二级排序序号;排序")
    private Integer twoSortNum;

    @ApiModelProperty(value = "是否开启通知;提醒类型为固定通知项目时0-开启，1-关闭，默认为关闭")
    private Boolean isAdvice;

    @ApiModelProperty(value = "是否开启弹窗;0-开启，1-关闭,默认为关闭")
    private Boolean isPop;

    @ApiModelProperty(value = "是否内置; 1-内置，0-不是内置")
    private Boolean isSys;

    @ApiModelProperty(value = "通知人类型;1-部门，2-角色，3-无")
    private Integer adviceUserType;

}

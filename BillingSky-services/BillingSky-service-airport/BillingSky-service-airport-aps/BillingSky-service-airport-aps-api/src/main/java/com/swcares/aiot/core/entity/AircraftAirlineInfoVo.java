package com.swcares.aiot.core.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * ClassName：com.swcares.aiot.core.entity.AircraftAirlineInfoVo
 * Description：飞机信息表中的航司信息vo
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/9/2 10:18
 * @version v1.0
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class AircraftAirlineInfoVo {
    @ApiModelProperty(name = "settleCode", value = "航空公司结算代码")
    private String settleCode;
    /**
     * 航空公司二字码
     */
    @ApiModelProperty(name = "airlineCode", value = "航空公司二字码")
    private String airlineCode;
    /**
     * 航空公司简称
     */
    @ApiModelProperty(name = "airlineShortName", value = "航空公司简称")
    private String airlineShortName;
}

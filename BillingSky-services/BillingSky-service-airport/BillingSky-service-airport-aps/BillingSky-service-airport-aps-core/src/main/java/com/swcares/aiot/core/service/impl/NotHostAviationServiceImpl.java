package com.swcares.aiot.core.service.impl;

import com.swcares.aiot.core.entity.NotHostAviation;
import com.swcares.aiot.core.mapper.NotHostAviationMapper;
import com.swcares.aiot.core.service.INotHostAviationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 非HOST航 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-01
 */
@Service
public class NotHostAviationServiceImpl extends ServiceImpl<NotHostAviationMapper, NotHostAviation> implements INotHostAviationService {

}

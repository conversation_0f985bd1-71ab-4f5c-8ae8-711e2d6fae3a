package com.swcares.aiot.core.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "FlightInfoDataVO", description = "航班信息数据修改和缺失统计")
public class FlightInfoDataVO {

    @ApiModelProperty(name = "modifiedTotal", value = "修改总数")
    private Integer modifiedTotal;

    @ApiModelProperty(name = "lostTotal", value = "缺失总数")
    private Integer lostTotal;

}

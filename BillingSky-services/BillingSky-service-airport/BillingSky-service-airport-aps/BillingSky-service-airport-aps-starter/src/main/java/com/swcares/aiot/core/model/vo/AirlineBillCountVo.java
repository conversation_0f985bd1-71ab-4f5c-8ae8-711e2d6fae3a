package com.swcares.aiot.core.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * ClassName：AirlineBillCountVo <br>
 * Description：(航司账单金额总计结果)<br>
 * Copyright © 2020/5/28 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020/5/28 10:57<br>
 * @version v1.0 <br>
 */
@ApiModel(value = "AirlineBillCountVo", description = "航司账单金额总计结果")
public interface AirlineBillCountVo {

    @ApiModelProperty(name = "settleAmountTotal", value = "总结算金额")
    BigDecimal getSettleAmountTotal();

    @ApiModelProperty(name = "adjustAmountTotal", value = "总调整金额")
    BigDecimal getAdjustAmountTotal();

    @ApiModelProperty(name = "refuseAmountTotal", value = "总拒付金额")
    BigDecimal getRefuseAmountTotal();

    @ApiModelProperty(name = "actualAmountTotal", value = "总实际结算金额")
    BigDecimal getActualAmountTotal();

    @ApiModelProperty(name = "totalSettleAmountTotal", value = "总结算金额之和")
    BigDecimal getTotalSettleAmountTotal();

    @ApiModelProperty(name = "taxRate", value = "税率")
    Integer getTaxRate();
}

package com.swcares.aiot.core.common;

import com.swcares.aiot.core.common.enums.HttpCodeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * <p>
 * ClassName：ResultBuilder <br>
 * Description：基本响应结果(建造者模式)<br>
 * Copyright © 2020-5-18 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020-5-18 9:52<br>
 * @version v1.0 <br>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@ApiModel(value = "ResultBuilder", description = "返回对象")
public class ResultBuilder<T> {
    /**
     * field：消息代码<br>
     *
     * @since v1.0<br>
     */
    @ApiModelProperty(name = "code", value = "代码")
    private Integer code;

    /**
     * field：消息<br>
     *
     * @since v1.0<br>
     */
    @ApiModelProperty(name = "msg", value = " 消息")
    private String msg;

    /**
     * field：实际返回数据<br>
     *
     * @since v1.0<br>
     */
    @ApiModelProperty(name = "data", value = "实际返回数据")
    private T data;

    public static class Builder<T> {
        /**
         * field：必填字段，付默认值，响应代码<br>
         *
         * @since v1.0<br>
         */
        private Integer code = HttpCodeEnum.SUCCESS.getCode();
        /**
         * field：响应信息<br>
         *
         * @since v1.0<br>
         */
        private String msg = HttpCodeEnum.SUCCESS.getMsg();
        /**
         * field：实际响应数据<br>
         *
         * @since v1.0<br>
         */
        private T data = null;

        /**
         * Title: Builder<br>
         * Author: 李龙<br>
         * Description: 创建一个新的实例 Builder <br>
         * Date:  10:00 <br>
         */
        public Builder() {
            super();
        }

        /**
         * Title: code<br>
         * Author: 李龙<br>
         * Description: 创建code值<br>
         * Date:  10:01 <br>
         *
         * @param code return: ResultBuilder.Builder
         */
        public Builder<T> code(Integer code) {
            this.code = code;
            return this;
        }

        /**
         * Title: msg<br>
         * Author: 李龙<br>
         * Description: 创建msg值<br>
         * Date:  10:02 <br>
         *
         * @param msg return: ResultBuilder.Builder
         */
        public Builder<T> msg(String msg) {
            this.msg = msg;
            return this;
        }

        /**
         * Title: data<br>
         * Author: 李龙<br>
         * Description: 创建data值<br>
         * Date:  10:03 <br>
         *
         * @param data return: ResultBuilder.Builder
         */
        public Builder<T> data(T data) {
            this.data = data;
            return this;
        }

        /**
         * Title: <br>
         * Author: 李龙<br>
         * Description: 返回ResultBuilder对象<br>
         * Date:  10:04 <br>
         */
        public ResultBuilder<T> builder() {
            return new ResultBuilder<>(this);
        }
    }

    /**
     * Title: <br>
     * Author: 李龙<br>
     * Description: 创建一个实例<br>
     * Date:  10:08 <br>
     */
    private ResultBuilder(Builder<T> builder) {
        this.code = builder.code;
        this.msg = builder.msg;
        this.data = builder.data;
    }
}

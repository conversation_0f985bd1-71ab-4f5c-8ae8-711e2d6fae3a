package com.swcares.aiot.core.model.entity;

import com.swcares.aiot.core.common.entity.AirportBaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/*
 *
 * ClassName：FeeRulesTemplate <br>
 * Description：费用信息<br>
 * Copyright © 2020-5-18 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020-5-18 11:19<br>
 * @version v1.0 <br>
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="t_fee_info")
public class FeeInfo extends AirportBaseEntity implements Serializable,Cloneable {

    /** 关联航司ID */
    @ApiModelProperty(name = "airlineId",value = "关联航司ID")
    @Column(name = "airline_id",columnDefinition = "VARCHAR(128)    COMMENT '关联航司ID'")
    private String airlineId ;
    /**
     * 费用名称
     */
    @ApiModelProperty(name = "feeName",value = "费用名称")
    @Column(name = "fee_name",columnDefinition = "VARCHAR(64)    COMMENT '费用名称'")
    private String feeName;
    /**
     * 费用代码
     */
    @ApiModelProperty(name = "feeCode",value = "费用代码")
    @Column(name = "fee_code",columnDefinition = "VARCHAR(32)    COMMENT '费用代码'")
    private String feeCode;
    /**
     * 费用类别;S:民航标准基准价|C:自定义费用
     */
    @ApiModelProperty(name = "feeType",value = "费用类别;S:民航标准基准价|C:自定义费用")
    @Column(name = "fee_type",columnDefinition = "CHAR(1)    COMMENT '费用类别 S:民航标准基准价 C:自定义费用'")
    private String feeType;
    /**
     * 数据有效;0:有效|1:无效
     */
    @ApiModelProperty(name = "invalid",value = "数据有效;0:有效|1:无效")
    @Column(name = "invalid",columnDefinition = "CHAR(1)    COMMENT '数据有效 1:有效|0:无效'")
    private String invalid = "1";
    /**
     * 数据优先级;0:后计算|1:先计算
     */
    @ApiModelProperty(name = "feePriority",value = "数据优先级;0:后计算|1:先计算")
    @Column(name = "fee_priority",columnDefinition = "INT    COMMENT '数据优先级;0:后计算|1:先计算'")
    private Integer feePriority = 0;

    @ApiModelProperty(name = "isServiceFee",value = "是否机务类费用；0：否|1：是")
    @Column(name = "is_service_fee",columnDefinition = "INT COMMENT '是否机务类费用；0：否|1：是'")
    private Integer isServiceFee = 0;

    @ApiModelProperty(name = "isExpired",value = "是否过期：0为否，1为是")
    @Column(name = "is_expired",columnDefinition = "CHAR(1)    COMMENT '是否过期：0为否，1为是'")
    private String isExpired = "0";

}

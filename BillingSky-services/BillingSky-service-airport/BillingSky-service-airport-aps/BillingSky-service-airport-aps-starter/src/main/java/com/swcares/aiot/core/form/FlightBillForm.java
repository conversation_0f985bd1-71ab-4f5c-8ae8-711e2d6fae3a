package com.swcares.aiot.core.form;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;


/**
 *
 * ClassName：FlightBillForm <br>
 * Description：(航班明细账单查询条件)<br>
 * Copyright © 2020/6/17 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020/6/17 18:39<br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "FlightBillForm", description = "航班明细账单查询条件")
public class FlightBillForm {


    @NotBlank(message = "airportCode不能为空！")
    @Size(min=3,max=3,message = "airportCode必须为3个长度！")
    @ApiModelProperty(name = "airportCode", value = "机场三字码",required = true)
    private String airportCode;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "startDate", value = "航班日期开始日期")
    private Date startDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "endDate", value = "航班日期结束日期")
    private Date endDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty(name = "flightTimeStartDate", value = "起降时间开始日期")
    private Date flightTimeStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty(name = "flightTimeEndDate", value = "起降时间结束日期")
    private Date flightTimeEndDate;

    @Size(max=3,message = "fromAirportCode最大为3个字符长度")
    @ApiModelProperty(name = "fromAirportCode", value = "起飞航站三字码")
    private String fromAirportCode;

    @Size(max=3,message = "toAirportCode最大为3个字符长度")
    @ApiModelProperty(name = "toAirportCode", value = "到达航站三字码")
    private String toAirportCode;

    @Size(max=32,message = "flightNo最大为32个字符长度")
    @ApiModelProperty(name = "flightNo", value = "航班号")
    private String flightNo;

    @ApiModelProperty(name = "choosedFeeInfo", value = "勾选的费用信息")
    private String  choosedFeeInfo;

    @ApiModelProperty(name = "settleCode",value = "结算费用代码")
    private String settleCode;

    @ApiModelProperty(name ="flightFlag",value = "起降标识")
    private String flightFlag;

    @ApiModelProperty(name ="regNo",value = "飞机号")
    private String regNo;

    @ApiModelProperty(name = "submit", value = "是否提交对账（0未提交 1已确认 2有争议 3待审核 4拒绝处理）")
    private String submit;

    @ApiModelProperty(name = "revocation", value = "撤销状态(1申请撤销 2拒绝撤销 3同意撤销)")
    private Integer revocation;

    @ApiModelProperty(name = "dateType", value = "日期类型(1航班日期 2起降日期)")
    private Integer dateType;

    public void setFlightTimeEndDate(Date flightTimeEndDate) {
        if (flightTimeEndDate != null){
            this.flightTimeEndDate = new Date(DateUtil.endOfDay(flightTimeEndDate).getTime());
        }
    }
}

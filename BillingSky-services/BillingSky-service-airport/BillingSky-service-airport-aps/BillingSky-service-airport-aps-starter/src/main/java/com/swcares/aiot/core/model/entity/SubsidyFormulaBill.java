package com.swcares.aiot.core.model.entity;


import com.swcares.aiot.core.common.entity.AirportBaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/** 
 * <AUTHOR> 
 * date 2022-08-22 15:28:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "t_subsidy_formula_bill")
@ApiModel(value = "Entity Of t_subsidy_formula_bill")
public class SubsidyFormulaBill extends AirportBaseEntity implements Serializable, Cloneable{
    /**
     * id
     */
    @Id
    @ApiModelProperty(value = "id", name = "id", required = false)
    private String id;
    /**
     * 补贴公式id
     */
    @ApiModelProperty(value = "补贴公式id", name = "subsidyFormulaId", required = false)
    private String subsidyFormulaId;
    /**
     * 结算开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结算开始时间", name = "startTime", required = false)
    private Date startDate;
    /**
     * 结算结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结算结束时间", name = "endTime", required = false)
    private Date endDate;
    /**
     * 保底收入
     */
    @ApiModelProperty(value = "保底收入", name = "guaranteedIncome", required = false)
    private BigDecimal guaranteedIncome;
    /**
     * 结算金额
     */
    @ApiModelProperty(value = "结算金额", name = "settleResult", required = false)
    private BigDecimal settleResult;

    /**
     * 数据有效 有效:1|无效:0
     */
    @ApiModelProperty(value = "数据有效 有效:1|无效:0", name = "invalid", required = false)
    private String invalid="1";
}

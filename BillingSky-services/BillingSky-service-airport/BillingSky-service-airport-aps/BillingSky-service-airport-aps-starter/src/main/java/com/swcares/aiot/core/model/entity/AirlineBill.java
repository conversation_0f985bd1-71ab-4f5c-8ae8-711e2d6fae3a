package com.swcares.aiot.core.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.aiot.core.common.entity.AirportBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ClassName：AirlineBill <br>
 * Description：(航司账单实体类)<br>
 * Copyright © 2020/5/27 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/5/27 17:55<br>
 * @version v1.0 <br>
 */

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "t_airline_bill")
@ApiModel(value = "AirlineBill", description = "航司账单实体类")
public class AirlineBill extends AirportBaseEntity implements Serializable, Cloneable {

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(name = "settleMonth", value = "结算月份")
    @Column(name = "settle_month", columnDefinition = "DATE    COMMENT '结算月份'")
    private Date settleMonth;

    @ApiModelProperty(name = "airlineShortName", value = "航司简称")
    @Column(name = "airline_short_name", columnDefinition = "VARCHAR(32)    COMMENT '航司简称'")
    private String airlineShortName;

    @ApiModelProperty(name = "settleCode", value = "结算代码")
    @Column(name = "settle_code", columnDefinition = "VARCHAR(3)    COMMENT '结算代码'")
    private String settleCode;

    @ApiModelProperty(name = "settleAmount", value = "结算金额")
    @Column(name = "settle_amount", columnDefinition = "DECIMAL(32,4)    COMMENT '结算金额'")
    private BigDecimal settleAmount;

    @ApiModelProperty(name = "adjustAmount", value = "调整金额")
    @Column(name = "adjust_amount", columnDefinition = "DECIMAL(32,4)    COMMENT '调整金额'")
    private BigDecimal adjustAmount=BigDecimal.ZERO;

    @ApiModelProperty(name = "refuseAmount", value = "拒付金额")
    @Column(name = "refuse_amount", columnDefinition = "DECIMAL(32,4)    COMMENT '拒付金额'")
    private BigDecimal refuseAmount=BigDecimal.ZERO;

    @ApiModelProperty(name = "invalid", value = "数据有效 1:有效|0:无效")
    @Column(name = "invalid", columnDefinition = "VARCHAR(1)    COMMENT '数据有效 1:有效|0:无效'")
    private String invalid = "1";

    @ApiModelProperty(name = "taxRate", value = "税率")
    @Column(name = "taxRate", columnDefinition = "CHAR(1)    COMMENT '税率'")
    private Integer taxRate=6;

    @ApiModelProperty(name = "submit", value = "是否提交（0未提交 1已确认 2有争议 3待审核 4拒绝处理）")
    @Column(name = "submit", columnDefinition = "CHAR(1)    COMMENT '是否提交（0未提交 1已确认 2有争议 3待审核 4拒绝处理）")
    private String submit="0";

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(name = "flightDate", value = "航班日期")
    @Column(name = "flight_date", columnDefinition = "DATE    COMMENT '航班日期'")
    private Date flightDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "flightTime", value = "起降时间")
    @Column(name = "flight_time", columnDefinition = "DATETIME    COMMENT '起降时间'")
    private Date flightTime;

    @ApiModelProperty(name = "dateType", value = "日期类型(1:航班日期 2:起降日期)")
    @Column(name = "date_type", columnDefinition = "Integer    COMMENT '日期类型(1:航班日期 2:起降日期)'")
    private Integer dateType;
}

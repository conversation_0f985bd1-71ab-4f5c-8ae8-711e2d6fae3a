package com.swcares.aiot.core.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * ClassName：AirlineBillCountVo <br>
 * Description：(航司账单金额总计结果)<br>
 * Copyright © 2020/5/28 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/5/28 10:57<br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "AirlineBillCountVoNew", description = "航司账单金额总计结果新")
public class AirlineBillCountVoNew {

    @ApiModelProperty(name = "settleAmountTotal", value = "总结算金额")
    private BigDecimal settleAmountTotal = new BigDecimal("0");

    @ApiModelProperty(name = "adjustAmountTotal", value = "总调整金额")
    private BigDecimal adjustAmountTotal = new BigDecimal("0");

    @ApiModelProperty(name = "refuseAmountTotal", value = "总拒付金额")
    private BigDecimal refuseAmountTotal = new BigDecimal("0");

    @ApiModelProperty(name = "actualAmountTotal", value = "总实际结算金额")
    private BigDecimal actualAmountTotal = new BigDecimal("0");

    @ApiModelProperty(name = "totalSettleAmountTotal", value = "总结算金额之和")
    private BigDecimal totalSettleAmountTotal = new BigDecimal("0");

    @ApiModelProperty(name = "totalTaxFee", value = "总税额")
    private BigDecimal taxFee = new BigDecimal("0");

    @ApiModelProperty(name = "totalRealTotalSettleAmount", value = "总结算金额(不含税)")
    private BigDecimal realTotalSettleAmount = new BigDecimal("0");

    @ApiModelProperty(name = "taxRate", value = "税率")
    private BigDecimal taxRate = new BigDecimal("0");
}

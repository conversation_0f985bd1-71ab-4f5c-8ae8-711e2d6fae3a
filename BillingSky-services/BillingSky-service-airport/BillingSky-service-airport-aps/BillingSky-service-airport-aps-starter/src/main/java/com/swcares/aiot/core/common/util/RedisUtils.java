package com.swcares.aiot.core.common.util;


import com.swcares.baseframe.common.core.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * ClassName：com.swcares.common.util.RedisUtils
 * Description：redis 工具类
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/9/21 14:02
 * @version v1.0
 */
@Component
@Slf4j
public class RedisUtils extends RedisUtil {

    @Resource
    private RedisTemplate<Object,Object> redisTemplate;

    /**
     * lua 脚本
     */
    public static final String SETNX_SCRIPT = "return redis.call('setnx',KEYS[1], ARGV[1])";
    public static final String DEL_SCRIPT = "if redis.call('get',KEYS[1]) == ARGV[1] then "
            + "   return redis.call('del',KEYS[1]) " + "else " + "   return 0 " + "end";

    /**
     * redis实现分布式锁
     *
     * @param key
     * @return :
     */
    public boolean setNx(String key, int time) {
        // 自定义脚本
        DefaultRedisScript script = new DefaultRedisScript<>(SETNX_SCRIPT, List.class);
        // 执行脚本,传入参数,由于value没啥用,这里随便写死的"1"
        List rst = (List) redisTemplate.execute(script, Collections.singletonList(key), "1");
        // 返回1,表示设置成功,拿到锁
        if ((Long) rst.get(0) == 1) {
            log.info("{}成功拿到锁", key);
            // 设置过期时间
            expire(key, time);
            log.info("{}已成功设置过期时间:{} 秒", key, time);
            return true;
        } else {
            long expire = getExpire(key);
            log.info("{}未拿到到锁,还有{}释放", key, expire);
            return false;
        }
    }

    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     */
    @Override
    public boolean expire(String key, long time) {
        try {
            if (time > 0L) {
                this.redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }

            return true;
        } catch (Exception var5) {
            log.error(var5.getMessage());
            return false;
        }
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    @Override
    public long getExpire(String key) {
        if(StringUtils.isEmpty(key)){
            return 0;
        }
        Long expireTime=redisTemplate.getExpire(key, TimeUnit.SECONDS);
        return expireTime==null?0:expireTime;
    }


    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    @SuppressWarnings("unchecked")
    public void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                redisTemplate.delete(key[0]);
            } else {
                redisTemplate.delete((Collection) CollectionUtils.arrayToList(key));
            }
        }
    }


}

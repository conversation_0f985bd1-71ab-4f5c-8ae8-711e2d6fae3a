package com.swcares.aiot.core.common.util;

import lombok.experimental.UtilityClass;

import javax.validation.ConstraintViolation;
import javax.validation.Valid;
import javax.validation.Validation;
import java.util.Set;

/**
 * 参数校验工具类
 * <AUTHOR>
 */
@UtilityClass
public class ValidationUtils {

    public static String valid(@Valid Object obj){
        Set<ConstraintViolation<@Valid Object>> validateSet = Validation.buildDefaultValidatorFactory().getValidator().validate(obj);
        if (!validateSet.isEmpty()){
            return validateSet.stream().map(ConstraintViolation::getMessage).reduce((m1, m2) -> m1 + "; " + m2).orElse("参数错误！");
        }
        return null;
    }

}
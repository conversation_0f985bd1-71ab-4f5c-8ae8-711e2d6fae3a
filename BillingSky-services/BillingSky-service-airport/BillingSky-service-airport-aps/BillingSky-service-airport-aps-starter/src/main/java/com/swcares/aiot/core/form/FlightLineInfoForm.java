package com.swcares.aiot.core.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * ClassName：com.swcares.form.FlightLineInfoForm
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/8/2 15:18
 * @version v1.0
 */
@Getter
@Setter
@ToString
@ApiModel(value = "FlightLineInfoForm" ,description = "补贴航线信息")
public class FlightLineInfoForm {

    /**
     * 航空公司
     */
    @ApiModelProperty(value = "航空公司", name = "airlineCode", required = false)
    private String airlineCode;
    /**
     * 航司简称
     */
    @ApiModelProperty(value = "航司简称", name = "airlineShortName", required = false)
    private String airlineShortName;
    /**
     * 航线
     */
    @ApiModelProperty(value = "航线", name = "flightLine", required = false)
    private String flightLine;
    /**
     * 预算金额
     */

    @ApiModelProperty(value = "预算金额", name = "budget", required = false)
    private String budget;

    /**
     * 机场三字码
     */
    @ApiModelProperty(value = "机场三字码", name = "airportCode", required = false)
    private String airportCode;
}

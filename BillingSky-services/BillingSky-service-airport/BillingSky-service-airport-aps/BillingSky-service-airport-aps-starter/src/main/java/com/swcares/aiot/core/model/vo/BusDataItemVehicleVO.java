package com.swcares.aiot.core.model.vo;

import com.swcares.aiot.core.entity.BillBusDataItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "BillBusDataItemVehicleVO", description = "车辆签单数据项详情表")
public class BusDataItemVehicleVO extends BillBusDataItem {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "选择器主键ID")
    private Long busSelectorItemId;

    @ApiModelProperty(value = "选项名称")
    private String selectorItemName;

    @ApiModelProperty(value = "是否选择;(1-已选择,0-未选择)")
    private Boolean status;

    @ApiModelProperty(value = "签单数据项详情id")
    private Long busBillItemId;

}

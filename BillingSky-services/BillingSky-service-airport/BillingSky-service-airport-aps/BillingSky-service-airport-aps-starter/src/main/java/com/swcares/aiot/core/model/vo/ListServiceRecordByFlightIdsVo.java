package com.swcares.aiot.core.model.vo;

import com.swcares.aiot.core.model.entity.ServiceRecord;
import lombok.Data;

import java.util.List;

/**
 * ClassName：com.swcares.vo.listServiceRecordByFlightIdsVo
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/10/10 16:52
 * @version v1.0
 */
@Data
public class ListServiceRecordByFlightIdsVo {

    private String flightId;

    private List<ServiceRecord> serviceRecordList;

    public ListServiceRecordByFlightIdsVo(String flightId){
        this.flightId=flightId;
    }
}

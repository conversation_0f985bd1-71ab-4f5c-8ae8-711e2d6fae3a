package com.swcares.aiot.core.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * ClassName：HostAviationDownloadDto
 * Description：HOST航下载Dto
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2024/8/30 21:01
 * Version v1.0
 */
@Accessors(chain = true)
@Data
public class HostAviationDownloadDto {

    @ApiModelProperty(value = "账单存证id", example = "")
    @NotBlank(message = "[账单存证id]不能为空")
    private String estimateBillProveId;

    @ApiModelProperty(value = "下载数据type(1=非host-app 2=非host-多主机 3=host)", example = "1")
    @NotNull(message = "[下载数据类型]不能为空")
    private Integer dataType;

}

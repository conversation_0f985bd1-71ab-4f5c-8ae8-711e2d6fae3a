package com.swcares.aiot.dao;

import com.swcares.aiot.core.common.dao.JpaBaseDao;
import com.swcares.aiot.core.model.entity.VariableGuarantee;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * ClassName：com.swcares.dao.VariableGuaranteeDao
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/5/5 15:43
 * @version v1.0
 */
@Repository
public interface VariableGuaranteeDao extends JpaBaseDao<VariableGuarantee,String> {

    @Query(nativeQuery = true,value = "SELECT " +
            " tri.variable_name, " +
            "CASE " +
            "   " +
            "  WHEN tri.variable_unit = 'H' THEN " +
            "   '时间范围' ELSE '数值'  " +
            "  END Data_Format, " +
            " trgbi.id, " +
            " tri.id AS variable_id, " +
            " trgbi.item_id, " +
            " trgbi.land_flag, " +//5
            " trgbi.data_update, " +
            " trgbi.conversion_rules, " +
            " trgbi.remark, " +
            " trgbi.invalid_date, " +
            " trgbi.airport_code, " +//10
            " trgbi.item_name, " +
            " tri.variable, " +
            " tri.variable_unit," +
            " trgbi.type " +
            "FROM " +
            " t_variable_record tri " +
            " LEFT JOIN ( " +
            " SELECT " +
            "  trg.id, " +
            "  trg.variable_id, " +
            "  trg.item_id, " +
            "  trg.land_flag, " +
            "  trg.data_update, " +
            "  trg.conversion_rules, " +
            "  trg.remark, " +
            "  trg.invalid_date, " +
            "  trg.airport_code, " +
            "  tbi.item_name , " +
            "  trg.modified_time," +
            "  trg.type " +
            " FROM " +
            "  t_variable_guarantee trg, " +
            "  t_bill_item tbi  " +
            " WHERE " +
            "  tbi.id = trg.item_id  " +
            "  AND tbi.deleted = '0' " +
            "  AND trg.invalid = '1' " +
            "  AND trg.type='1' " +
            " ) trgbi ON tri.id = trgbi.variable_id  " +
            "WHERE if(:variableId!='',tri.id=:variableId,1=1) " +
            "and tri.airport_code=:airportCode " +
            "ORDER BY " +
            " trgbi.modified_time DESC")
    List<Object[]> getVariableGuaranteeList(@Param("variableId")String variableId,
                                            @Param("airportCode")String airportCode);



    @Query(nativeQuery = true,value = "SELECT " +
            " tri.variable_name, " +
            "CASE " +
            "   " +
            "  WHEN tri.variable_unit = 'H' THEN " +
            "   '时间范围' ELSE '数值'  " +
            "  END Data_Format, " +
            " trgbi.id, " +
            " tri.id AS variable_id, " +
            " trgbi.item_code, " +
            " trgbi.land_flag, " +//5
            " trgbi.data_update, " +
            " trgbi.conversion_rules, " +
            " trgbi.remark, " +
            " trgbi.invalid_date, " +
            " trgbi.airport_code, " +//10
            " trgbi.item_name, " +
            " tri.variable, " +
            " tri.variable_unit, " +
            " trgbi.type  " +
            "FROM " +
            " t_variable_record tri " +
            " INNER JOIN ( " +
            " SELECT " +
            "  trg.id, " +
            "  trg.variable_id, " +
            "  trg.item_code, " +
            "  trg.land_flag, " +
            "  trg.data_update, " +
            "  trg.conversion_rules, " +
            "  trg.remark, " +
            "  trg.invalid_date, " +
            "  trg.airport_code, " +
            "  trg.item_name , " +
            "  trg.modified_time," +
            "  trg.aps_service_code," +
            "  trg.type " +
            " FROM " +
            "  t_variable_guarantee trg " +
            " WHERE trg.invalid = '1' " +
            "  AND trg.type='2' " +
            " ) trgbi ON tri.variable = trgbi.aps_service_code  " +
            "WHERE if(:variableId!='',tri.id=:variableId,1=1) " +
            "and tri.airport_code=:airportCode " +
            "ORDER BY " +
            " trgbi.modified_time DESC")
    List<Object[]> getThirdPartyVariableGuaranteeList(@Param("variableId")String variableId,
                                            @Param("airportCode")String airportCode);


    @Query(nativeQuery = true,value = "select " +
            "trg.* " +
            "from t_variable_guarantee trg " +
            "where trg.id=:id " +
            "AND trg.invalid = '1' ")
    VariableGuarantee getVariableGuaranteeById(@Param("id") String id);

    @Query(nativeQuery = true,value = "select " +
            "trg.* " +
            "from t_variable_guarantee trg " +
            "inner JOIN t_bill_item tbi on tbi.id=trg.item_id " +
            "where trg.variable_id=:variableId " +
            "and tbi.deleted='0' " +
            "AND trg.invalid = '1' ")
    List<VariableGuarantee> getVariableGuaranteeByVariableId(@Param("variableId") String variableId);


    @Modifying
    @Query(nativeQuery = true,value = "update t_variable_guarantee set invalid='0' where item_id in :list ")
    Integer deleteAllByItemIdList(@Param("list")List<String> idList);

    @Modifying
    @Query(nativeQuery = true,value = "update t_variable_guarantee set invalid='0' where item_id =:itemId ")
    Integer deleteAllByItemId(@Param("itemId")Long itemId);

    @Query(nativeQuery = true,value = "select " +
            "trg.* " +
            "from t_variable_guarantee trg " +
            "where trg.item_code=:itemCode " +
            "AND trg.invalid = '1' " +
            "AND trg.type='2' ")
    List<VariableGuarantee> getVariableGuaranteeByItemCode(@Param("itemCode") String itemCode);

    @Query(nativeQuery = true,value = "select " +
            "trg.* " +
            "from t_variable_guarantee trg " +
            "where trg.aps_service_code=:apsServiceCode " +
            "AND trg.invalid = '1' " +
            "AND trg.type='2' ")
    VariableGuarantee getVariableGuaranteeByApsServiceCode(@Param("apsServiceCode") String apsServiceCode);

}

package com.swcares.aiot.core.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.core.model.dto.ThirdPartyImportFlightDto
 * Description：
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/9/23 14:22
 * @version v1.0
 */
@Data
@ApiModel(value = "ThirdPartyImportFlightDto", description = "第三方导入业务数据-航班对象")
public class ThirdPartyImportFlightDto {

    @NotBlank
    @ApiModelProperty(name = "flightNo", value = "拼接航班号（例如：3U1111/3U2222，3U1111/，/3U2222",required = true)
    private String flightNo;
    @NotNull
    @ApiModelProperty(name = "flightDate", value = "航班日期",required = true)
    private LocalDateTime flightDate;
    @NotBlank
    @ApiModelProperty(name = "airportCode", value = "机场三字码",required = true)
    private String airportCode;
    @ApiModelProperty(name = "serviceList", value = "服务列表")
    private List<ThirdPartyImportServiceDto> serviceList;
}

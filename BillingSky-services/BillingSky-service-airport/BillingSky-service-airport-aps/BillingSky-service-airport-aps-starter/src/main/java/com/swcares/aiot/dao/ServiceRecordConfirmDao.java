package com.swcares.aiot.dao;

import java.util.List;

import com.swcares.aiot.core.common.dao.JpaBaseDao;
import com.swcares.aiot.core.model.entity.ServiceRecordConfirm;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * ClassName：ServiceRecordDao <br>
 * Description： 特车/设备数据dao <br>
 *
 * <AUTHOR> <br>
 * date 2020/6/11 11:09<br>
 * @version v1.0 <br>
 */
@Repository
public interface ServiceRecordConfirmDao extends JpaBaseDao<ServiceRecordConfirm, String> {

    /**
     * Title: listServiceRecordByCondition<br>
     * Author: 刘志恒<br>
     * Description: 查看某个航班的指定特车/设备的所有数据<br>
     * Date:  2023/10/18 16:17 <br>
     */
    @Query(nativeQuery = true,
            value = "select tsr.* from t_service_record_confirm as tsr "
                    + "where tsr.flight_id=:flightId and tsr.airport_code=:airportCode "
                    + "and tsr.service_code=:indexDataFlag and tsr.invalid='1'")
    List<ServiceRecordConfirm> listServiceRecordByCondition(@Param("flightId") String flightId,
            @Param("airportCode") String airportCode, @Param("indexDataFlag") String indexDataFlag);

    /**
     * Title: listServiceRecordByFlightId<br>
     * Author: 刘志恒<br>
     * Description: 查看某个航班的指定特车/设备的所有数据<br>
     * Date:  2023/10/23 14:17 <br>
     */
    @Query(nativeQuery = true, value = "select tsr.* from t_service_record_confirm as tsr "
            + "where tsr.flight_id=:flightId  and tsr.invalid='1'")
    List<ServiceRecordConfirm> listServiceRecordByFlightId(@Param("flightId") String flightId);


    @Query(nativeQuery = true, value = "select tsr.* from t_service_record_confirm as tsr "
            + "where tsr.flight_id=:flightId  " +
            "and tsr.bill_bus_data_id=:signId " +
            "and tsr.invalid='1'")
    List<ServiceRecordConfirm> findServiceRecordByFlightIdAndSignId(@Param("flightId") String flightId,
                                                                    @Param("signId")String signId);

    /**
     * Title: deleteServiceRecordByIds<br>
     * Author: 刘志恒<br>
     * Description: 通过id批量删除<br>
     * Date:  2023/10/18 16:17 <br>
     */
    @Modifying
    @Query(nativeQuery = true,
            value = "update   t_service_record_confirm set invalid='0' where id in (:listId)")
    Integer deleteServiceRecordByIds(@Param("listId") List<String> listId);

    /**
     * Title: deleteServiceRecordByDlightId<br>
     * Author: 刘志恒<br>
     * Description: 通过flightid批量删除<br>
     * Date:  2023/10/18 16:17 <br>
     */
    @Modifying
    @Query(nativeQuery = true,
            value = "update   t_service_record_confirm set invalid='0' where flight_id=:flightId")
    Integer deleteServiceRecordByDlightId(@Param("flightId") String flightId);

    /**
     * Title: deleteServiceRecordByDlightIds<br>
     * Author: 刘志恒<br>
     * Description: 通过flightids批量删除<br>
     * Date:  2023/10/18 16:17 <br>
     */
    @Modifying
    @Query(nativeQuery = true,
            value = "update   t_service_record_confirm set invalid='0' where flight_id in (:flightIds)")
    Integer deleteServiceRecordByDlightIds(@Param("flightIds") List<String> flightIds);

    /**
     * Title: listCountServiceRecord<br>
     * Author: 刘志恒<br>
     * Description: 查询<br>
     * Date:  2023/10/18 16:17 <br>
     */
    @Query(nativeQuery = true,
            value = "select tsr.service_code,tsr.start_time,tsr.end_time,tsr.used_number,tsr.service_name from t_service_record_confirm as tsr "
                    + "where tsr.flight_id=:flightId and tsr.invalid='1' "
                    + "order by tsr.start_time,tsr.end_time ")
    List<Object[]> listCountServiceRecord(@Param("flightId") String flightId);

    /**
     * Title: getChangeNum<br>
     * Author: 刘志恒<br>
     * Description: 获取确认后修改航班条数<br>
     * Date:  2023/10/27 16:41 <br>
     */
    @Query(nativeQuery = true,value = " select count(DISTINCT flight_id) from t_service_record_confirm " +
            "where invalid='1' " +
            "and flight_id in (:flightIds)")
    Integer getChangeNum(@Param("flightIds")List<String> flightIds);

    @Query(nativeQuery = true,
            value = "select tsr.* from t_service_record_confirm as tsr "
                    + "where tsr.flight_id in(:flightIdList)  and tsr.invalid='1'")
    List<ServiceRecordConfirm> listServiceRecordByFlightIdList(@Param("flightIdList")List<String> flightIdList);
}

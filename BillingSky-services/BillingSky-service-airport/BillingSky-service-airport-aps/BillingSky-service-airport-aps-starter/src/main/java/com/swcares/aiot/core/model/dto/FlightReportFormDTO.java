package com.swcares.aiot.core.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * ClassName：FlightReportFormDTO
 * Description：航班客座率报表 DTO
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2024/6/25 13:59
 * Version v1.0
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "FlightReportFormDTO", description = "航班客座率报表DTO")
public class FlightReportFormDTO {

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @NotNull(message = "航班日期不能为空")
    @ApiModelProperty(name = "startFlightDate", value = "航班日期-开始时间", required = true)
    private Date startFlightDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @NotNull(message = "航班日期不能为空")
    @ApiModelProperty(name = "endFlightDate", value = "航班日期-结束时间", required = true)
    private Date endFlightDate;

    @ApiModelProperty(name = "flightNo",value = "航班号")
    private String flightNo;

    @ApiModelProperty(name = "flightFlag",value = "起降标识：A进港、D出港")
    private String flightFlag;

}

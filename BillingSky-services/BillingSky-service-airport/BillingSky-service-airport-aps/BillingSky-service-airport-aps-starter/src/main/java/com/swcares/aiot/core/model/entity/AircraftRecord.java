package com.swcares.aiot.core.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name="p_aircraft_record")
@Entity
public class AircraftRecord implements Serializable, Cloneable {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator="system-uuid",strategy = GenerationType.IDENTITY)
    @GenericGenerator(name="system-uuid", strategy = "uuid")
    @ApiModelProperty(name = "id",value = "主键")
    @Column(name = "ID",columnDefinition = "VARCHAR(128) NOT NULL   COMMENT '主键'")
    private String id;


    /**
     * 机号
     */
    @ApiModelProperty(name = "regNo", value = "机号")
    @Column(name = "reg_no", columnDefinition = "VARCHAR(32)    COMMENT '机号'")
    private String regNo;

    /**
     * 机型
     */
    @ApiModelProperty(name = "flightModel", value = "机型")
    @Column(name = "flight_model", columnDefinition = "VARCHAR(32)    COMMENT '机型'")
    private String flightModel;

    /**
     * 机型名称
     */
    @ApiModelProperty(name = "flightModelName", value = "机型名称")
    @Column(name = "flight_model_name", columnDefinition = "VARCHAR(32)    COMMENT '机型名称'")
    private String flightModelName;

    /**
     * 航司二字码
     */
    @ApiModelProperty(name = "airlineCode", value = "航司二字码")
    @Column(name = "airline_code", columnDefinition = "VARCHAR(32)    COMMENT '航司二字码'")
    private String airlineCode;

    /**
     * 航司名称
     */
    @ApiModelProperty(name = "airlineName", value = "航司名称")
    @Column(name = "airline_name", columnDefinition = "VARCHAR(32)    COMMENT '航司名称'")
    private String airlineName;

    /**
     * 最大座位数
     */
    @ApiModelProperty(name = "maxSeat", value = "最大座位数")
    @Column(name = "max_seat", columnDefinition = "INT(10)    COMMENT '最大座位数'")
    private Integer maxSeat;

    /**
     * 可供座位数
     */
    @ApiModelProperty(name = "availableSeat", value = "可供座位数")
    @Column(name = "available_seat", columnDefinition = "INT(10)    COMMENT '可供座位数'")
    private Integer availableSeat;

    /**
     * 最大业载
     */
    @ApiModelProperty(name = "maxPayload", value = "最大业载")
    @Column(name = "max_payload", columnDefinition = "INT(10)    COMMENT '最大业载'")
    private Integer maxPayload;

    /**
     * 可供业载
     */
    @ApiModelProperty(name = "availablePayload", value = "可供业载")
    @Column(name = "available_payload", columnDefinition = "INT(10)    COMMENT '可供业载'")
    private Integer availablePayload;

    /**
     * 最大起飞重量
     */
    @ApiModelProperty(name = "maxWeight", value = "最大起飞重量")
    @Column(name = "max_weight", columnDefinition = "INT(10)    COMMENT '最大起飞重量'")
    private Integer maxWeight;

    /**
     * 国家
     */
    @ApiModelProperty(name = "country", value = "国家")
    @Column(name = "country", columnDefinition = "VARCHAR(32)    COMMENT '国家'")
    private String country;

    /**
     * 国际标识
     */
    @ApiModelProperty(name = "flag", value = "国际标识")
    @Column(name = "flag", columnDefinition = "VARCHAR(2)    COMMENT '国际标识'")
    private String flag;

    /**
     * 有效性
     */
    @ApiModelProperty(name = "invalid", value = "有效性")
    @Column(name = "invalid", columnDefinition = "VARCHAR(1)    COMMENT '有效性'")
    private String invalid;

    /**
     * 有效时间
     */
    @ApiModelProperty(name = "effectiveDate", value = "有效时间")
    @Column(name = "effective_date", columnDefinition = "VARCHAR(32)    COMMENT '有效时间'")
    private String effectiveDate;

    /**
     * 失效时间
     */
    @ApiModelProperty(name = "expirationDate", value = "失效时间")
    @Column(name = "expiration_date", columnDefinition = "VARCHAR(32)    COMMENT '失效时间'")
    private String expirationDate;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "createName", value = "创建人")
    @Column(name = "create_name", columnDefinition = "VARCHAR(32)    COMMENT '创建人'")
    private String createName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    @Column(name = "create_time", columnDefinition = "VARCHAR(32)    COMMENT '创建时间'")
    private String createTime;

    /**
     * 修改人
     */
    @ApiModelProperty(name = "modifyName", value = "修改人")
    @Column(name = "modify_name", columnDefinition = "VARCHAR(32)    COMMENT '修改人'")
    private String modifyName;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    @Column(name = "modify_time", columnDefinition = "VARCHAR(32)    COMMENT '修改时间'")
    private String modifyTime;
}

package com.swcares.aiot.core.importer.entity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * ClassName：FlightInfo <br>
 * Description：(航班信息实体类)<br>
 * Copyright © 2020-5-14 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR>
 * date 2021-1-14 15:50<br>
 * @version v1.0 <br>
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_flight_info")
public class TFlightInfo implements Serializable, Cloneable {

    @ApiModelProperty(name = "id")
    private String id;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(name = "flightDate", value = "航班日期")
    private String flightDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "flightTime", value = "起降时间")
    private Date flightTime;

    /**
     * Description :  航班航线信息  <br/>
     */
    @ApiModelProperty(name = "flightNo", value = "航班号")
    private String flightNo;

    @ApiModelProperty(name = "flightLine", value = "航线")
    private String flightLine;

    @ApiModelProperty(name = "flightSegment", value = "航段")
    private String flightSegment;

    @ApiModelProperty(name = "flightType", value = "航班性质 (国际:I|国内:D) ")
    private String flightType;

    @ApiModelProperty(name = "flightLineType", value = "航线性质 (国际:I|国内:D)")
    private String flightLineType;

    @ApiModelProperty(name = "flightSegmentType", value = "航段性质 (国际:I|国内:D)")
    private String flightSegmentType;

    @ApiModelProperty(name = "airlineCode", value = "航空公司")
    private String airlineCode;

    @ApiModelProperty(name = "regNo", value = "飞机注册号")
    private String regNo;

    @ApiModelProperty(name = "flightModel", value = "机型")
    private String flightModel;

    @ApiModelProperty(name = "fromAirportCode", value = "出发航站三字码")
    private String fromAirportCode;

    @ApiModelProperty(name = "toAirportCode", value = "到达航站三字码")
    private String toAirportCode;

    /**
     * Description : 航班旅客信息<br/>
     */
    @ApiModelProperty(name = "psgNumber", value = "进出港人数")
    private Integer psgNumber;

    @ApiModelProperty(name = "firstClassNumber", value = "头等舱人数")
    private Integer firstClassNumber;

    @ApiModelProperty(name = "clubClassNumber", value = "商务舱人数")
    private Integer clubClassNumber;

    @ApiModelProperty(name = "economyClassNumber", value = "经济舱人数")
    private Integer economyClassNumber;

    @ApiModelProperty(name = "adultNumber", value = "成人数")
    private Integer adultNumber;

    @ApiModelProperty(name = "childNumber", value = "儿童数")
    private Integer childNumber;

    @ApiModelProperty(name = "infantNumber", value = "婴儿数")
    private Integer infantNumber;

    @ApiModelProperty(name = "transitAdultNumber", value = "过站成人数")
    private Integer transitAdultNumber;

    @ApiModelProperty(name = "transitChildNumber", value = "过站儿童数")
    private Integer transitChildNumber;

    @ApiModelProperty(name = "transitInfantNumber", value = "过站婴儿数")
    private Integer transitInfantNumber;

    @ApiModelProperty(name = "diplomaticPassportNumber", value = "持外交护照人数")
    private Integer diplomaticPassportNumber;

    @ApiModelProperty(name = "cardHolderNumber", value = "持卡旅客人数")
    private Integer cardHolderNumber;

    @ApiModelProperty(name = "accompanyingCardHolderNumber", value = "持卡旅客随行人数")
    private Integer accompanyingCardHolderNumber;

    @ApiModelProperty(name = "importantNumber", value = "重要旅客人数")
    private Integer importantNumber;

    @ApiModelProperty(name = "accompanyingImportantNumber", value = "重要旅客随行人数")
    private Integer accompanyingImportantNumber;

    /**
     * Description : 行李货邮信息 <br/>
     */
    @ApiModelProperty(name = "plf", value = "客坐率")
    private Double plf;

    @ApiModelProperty(name = "cargo", value = "货物重量")
    private Double cargo;

    @ApiModelProperty(name = "mail", value = "邮件重量")
    private Double mail;

    @ApiModelProperty(name = "bag", value = "行李重量")
    private Double bag;

    @ApiModelProperty(name = "bagNumber", value = "行李件数")
    private Integer bagNumber;

    @ApiModelProperty(name = "weightUints", value = "重量单位")
    private String weightUints;

    @ApiModelProperty(name = "flightFlag", value = "起降标识 (起飞:D|到达:A)")
    private String flightFlag;

    @ApiModelProperty(name = "taskFlag", value = "任务标识")
    private String taskFlag;

    @ApiModelProperty(name = "daAirportCode", value = "采集机场三字码")
    private String daAirportCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "daTime", value = "采集时间")
    private Date daTime;

    @ApiModelProperty(name = "flightFee", value = "起降费用标识 (起飞费用:1|降落费用:0)")
    private String flightFee;

    @ApiModelProperty(name = "groundFee", value = "地面服务费用标识")
    private String groundFee;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "stayStartTime", value = "停场开始时间")
    private Date stayStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "stayEndTime", value = "停场结束时间")
    private Date stayEndTime;

    @ApiModelProperty(name = "stayTime", value = "停场时间")
    private Integer stayTime;

    @ApiModelProperty(name = "invalid", value = "数据有效 (0:有效|1:无效)")
    private String invalid = "1";

    @ApiModelProperty(name = "confirmCode", value = "数据确认代码 (0:有效|1:无效)")
    private String confirmCode = "1";

    @ApiModelProperty(name = "currentStationCargo",value = "本站货物")
    private BigDecimal currentStationCargo;

    @ApiModelProperty(name = "currentStationMail",value = "本站邮件")
    private BigDecimal currentStationMail;

    @ApiModelProperty(name = "currentStationBag",value = "本站行李")
    private BigDecimal currentStationBag;

    @ApiModelProperty(name="manualAdultNumber",value = "手动录入本站成人数")
    private Integer manualAdultNumber;
    @ApiModelProperty(name="manualChildNumber",value = "手动录入本站儿童数")
    private Integer manualChildNumber;
    @ApiModelProperty(name="manualAdultNumber",value = "手动录入本站婴儿数")
    private Integer manualInfantNumber;
    @ApiModelProperty(name="manualAdultNumber",value = "手动录入过站成人数")
    private Integer manualTransitAdultNumber;
    @ApiModelProperty(name="manualAdultNumber",value = "手动录入过站儿童数")
    private Integer manualTransitChildNumber;
    @ApiModelProperty(name="manualAdultNumber",value = "手动录入过站婴儿数")
    private Integer manualTransitInfantNumber;
    @ApiModelProperty(name="passengerDeleted" ,value="旅客删除标识")
    private Integer passengerDeleted;
    @ApiModelProperty(name="cmb_Deleted" ,value="货邮行删除标识")
    private Integer cmbDeleted;
    @ApiModelProperty(name="manualCargo",value = "手动录入货物")
    private BigDecimal manualCargo;
    @ApiModelProperty(name="manualMail",value = "手动录入邮件")
    private BigDecimal manualMail;
    @ApiModelProperty(name="manualBag",value = "手动录入行李")
    private BigDecimal manualBag;
    @ApiModelProperty(name="createBy",value = "创建人")
    private String createBy;
    @ApiModelProperty(name="createTime",value = "创建时间")
    private String createTime;
    @ApiModelProperty(name="ModifiedBy",value = "修改人")
    private String ModifiedBy;
    @ApiModelProperty(name="ModifiedTime",value = "修改时间")
    private String ModifiedTime;
    @ApiModelProperty(name="manualFlag",value = "旅客手动录入标识")
    private Integer manualFlag;
    @ApiModelProperty(name="manualCmbFlag",value = "货邮行手动录入标识")
    private Integer manualCmbFlag;
    @ApiModelProperty(name="updatedTime",value = "最后修改时间")
    private LocalDateTime updatedTime;
}

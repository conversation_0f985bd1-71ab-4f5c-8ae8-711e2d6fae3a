package com.swcares.aiot.core.common.util;

import cn.hutool.core.text.CharSequenceUtil;
import com.google.common.collect.Lists;
import com.swcares.aiot.core.common.constant.CommonConstants;
import com.swcares.aiot.core.common.constant.Constants;
import com.swcares.aiot.core.common.enums.BusinessMessageEnum;
import com.swcares.aiot.core.exception.GenericException;
import com.swcares.aiot.core.model.entity.AircraftRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ClassName：ExcelFlightLineUtils <br>
 * Description： 航线excel解析工具类 <br>
 *
 * <AUTHOR> <br>
 * date 2020/8/6 11:03<br>
 * @version v1.0 <br>
 */
@Slf4j
public class ExcelAircraftUtils {

    private ExcelAircraftUtils() {

    }

    /**
     * Title: readExcel<br>
     * Author: wangxiong<br>
     * Description: 读取文档数据 <br>
     * Date:  13:07 <br>
     *
     * @param file :
     * @return : java.util.List<com.swcares.modules.settlement.vo.FlightLineRecord>
     */
    public static List<AircraftRecord> readExcel(MultipartFile file) {

        Workbook workbook;
        // 验证表头
        Object[] header = FileUtils.getExcelHeader(file);
        try {
            if (!header[0].equals(Constants.AircraftExcelHeaderEnum.REGNO_CELL_CODE.getValue())
                    && !header[1].equals(Constants.AircraftExcelHeaderEnum.FLIGHTMODEL_CELL_CODE.getValue())
                    && !header[2].equals(Constants.AircraftExcelHeaderEnum.FLIGHTMODEL_NAME_CELL_CODE.getValue())
                    && !header[3].equals(Constants.AircraftExcelHeaderEnum.AIRLINE_CELL_CODE.getValue())
                    && !header[4].equals(Constants.AircraftExcelHeaderEnum.AIRLINE_NAME_CELL_CODE.getValue())
                    && !header[5].equals(Constants.AircraftExcelHeaderEnum.MAXSEAT_CELL_CODE.getValue())
                    && !header[6].equals(Constants.AircraftExcelHeaderEnum.AVAILABLESEAT_CELL_CODE.getValue())
                    && !header[7].equals(Constants.AircraftExcelHeaderEnum.MAXPAYLOAD_CELL_CODE.getValue())
                    && !header[8].equals(Constants.AircraftExcelHeaderEnum.AVAILABLEPAYLOAD_CELL_CODE.getValue())
                    && !header[9].equals(Constants.AircraftExcelHeaderEnum.MAXWEIGHT_CELL_CODE.getValue())
                    && !header[10].equals(Constants.AircraftExcelHeaderEnum.COUNTRY_CELL_CODE.getValue())
                    && !header[11].equals(Constants.AircraftExcelHeaderEnum.FLAG_CELL_CODE.getValue())
                    && !header[12].equals(Constants.AircraftExcelHeaderEnum.INVALID_CELL_CODE.getValue())
                    && !header[13].equals(Constants.AircraftExcelHeaderEnum.EFFECTIVE_DATE_CELL_CODE.getValue())
                    && !header[14].equals(Constants.AircraftExcelHeaderEnum.EXPIRATION_DATE_CELL_CODE.getValue())
                    && !header[15].equals(Constants.AircraftExcelHeaderEnum.CREATENAME_CELL_CODE.getValue())
                    && !header[16].equals(Constants.AircraftExcelHeaderEnum.CREATETIME_CELL_CODE.getValue())
                    && !header[17].equals(Constants.AircraftExcelHeaderEnum.MODIFYNAME_CELL_CODE.getValue())
                    && !header[18].equals(Constants.AircraftExcelHeaderEnum.MIDIFYTIME_CELL_CODE.getValue())
            ) {
                throw new GenericException(BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getCode(), BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getMsg());
            }
        } catch (Exception e) {
            throw new GenericException(BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getCode(), BusinessMessageEnum.DATA_FILE_HEADER_ERROR.getMsg());
        }
        // 获取目标文件名
        String fileName = file.getOriginalFilename();
        if (Strings.isBlank(fileName) || !fileName.contains(".")) {
            throw new GenericException(BusinessMessageEnum.DATA_FILE_ERROR.getCode(),
                    BusinessMessageEnum.DATA_FILE_ERROR.getMsg());
        }
        // 获取文件后缀
        String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
        try {
            // 通过文件后缀生成对应excel
            workbook = ExcelUtils.getWorkbook(file.getInputStream(), fileType);
        } catch (Exception e) {
            throw new GenericException(BusinessMessageEnum.GENERATE_EXCEL_DOCUMENT_ERROR.getCode(),
                    BusinessMessageEnum.GENERATE_EXCEL_DOCUMENT_ERROR.getMsg());
        }

        List<AircraftRecord> resultDataList;
        try (AutoCloseableUtils closeableUtils = new AutoCloseableUtils()) {
            // 解析excel数据
            resultDataList = parseExcel(workbook);
            log.info("待上传有效数据量：{}", resultDataList.size());
        } catch (Exception e) {
            throw new GenericException(BusinessMessageEnum.PARSE_EXCEL_ERROR.getCode(),
                    BusinessMessageEnum.PARSE_EXCEL_ERROR.getMsg());
        }

        // 解决为空异常
        if (!CollectionUtils.isEmpty(resultDataList)) {
            return resultDataList;
        } else {
            return Lists.newArrayList();
        }
    }


    /**
     * Title: parseExcel<br>
     * Author: wangxiong<br>
     * Description: 解析 <br>
     * Date:  13:07 <br>
     *
     * @param workbook :
     * @return : java.util.List<com.swcares.modules.settlement.vo.FlightLineRecord>
     */
    private static List<AircraftRecord> parseExcel(Workbook workbook) {
        List<AircraftRecord> resultDataList = new ArrayList<>();
        // 解析sheet
        for (int sheetNum = 0; sheetNum < workbook.getNumberOfSheets(); sheetNum++) {
            Sheet sheet = workbook.getSheetAt(sheetNum);

            // 校验sheet是否合法
            if (sheet == null) {
                continue;
            }

            // 验证第一行数据合法性
            int firstRowNum = sheet.getFirstRowNum();
            Row firstRow = sheet.getRow(firstRowNum);
            if (null == firstRow) {
                log.info("解析Excel失败，在第一行没有读取到任何数据！");
            }

            // 解析每一行的数据，构造数据对象
            for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row row = sheet.getRow(rowNum);
                // 判断当前行数据为空，则继续解析下一行
                if (null == row) {
                    continue;
                }
                // excel 转data
                AircraftRecord resultData = convertRowToData(row);
                if (Objects.isNull(resultData)) {
                    log.info("第 {}行数据不合法，已忽略！", row.getRowNum());
                    continue;
                }
                resultDataList.add(resultData);
            }
        }
        return resultDataList;
    }


    /**
     * Title: convertRowToData<br>
     * Author: wangxiong<br>
     * Description: 数据转换 <br>
     * Date:  13:08 <br>
     *
     * @param row :
     * @return : AircraftRecord
     */
    private static AircraftRecord convertRowToData(Row row) {
        AircraftRecord resultData = new AircraftRecord();
        Cell cell;
        int cellNum = 0;
        // 机号
        cell = row.getCell(cellNum++);
        String regeNo = ExcelUtils.convertCellValueToString(cell);
        resultData.setRegNo(null == regeNo ? "" : regeNo);


        // 机型
        cell = row.getCell(cellNum++);
        String flightModel = ExcelUtils.convertCellValueToString(cell);
        resultData.setFlightModel(null == flightModel ? "" : flightModel);

        // 机型名称
        cell = row.getCell(cellNum++);
        String flightModelName = ExcelUtils.convertCellValueToString(cell);
        resultData.setFlightModelName(null == flightModelName ? "" : flightModelName);

        // 航司二字码
        cell = row.getCell(cellNum++);
        String airlineCode = ExcelUtils.convertCellValueToString(cell);
        resultData.setAirlineCode(null == airlineCode ? "" : airlineCode);

        // 使用单位
        cell = row.getCell(cellNum++);
        String airlinName = ExcelUtils.convertCellValueToString(cell);
        resultData.setAirlineName(null == airlinName ? "" : airlinName);

        // 最大座位
        cell = row.getCell(cellNum++);
        String maxSeat = ExcelUtils.convertCellValueToString(cell);
        resultData.setMaxSeat(null == maxSeat ? null : Integer.parseInt(maxSeat));

        // 可供座位
        cell = row.getCell(cellNum++);
        String availableSeat = ExcelUtils.convertCellValueToString(cell);
        resultData.setAvailableSeat(null == availableSeat ? null : Integer.parseInt(availableSeat));

        // 最大业载
        cell = row.getCell(cellNum++);
        String maxPayload = ExcelUtils.convertCellValueToString(cell);
        resultData.setMaxPayload(null == maxPayload ? null : Integer.parseInt(maxPayload));

        // 可供业载
        cell = row.getCell(cellNum++);
        String availablePayload = ExcelUtils.convertCellValueToString(cell);
        resultData.setAvailablePayload(null == availablePayload ? null : Integer.parseInt(availablePayload));

        // 最大起飞重量
        cell = row.getCell(cellNum++);
        String maxWeight = ExcelUtils.convertCellValueToString(cell);
        resultData.setMaxWeight(null == maxWeight ? null : Integer.parseInt(maxWeight));

        // 国家
        cell = row.getCell(cellNum++);
        String country = ExcelUtils.convertCellValueToString(cell);
        resultData.setCountry(null == country ? "" : country);

        // 国内外标识
        cell = row.getCell(cellNum++);
        String flag = ExcelUtils.convertCellValueToString(cell);
        resultData.setFlag(null == flag ? "" : flag);

        // 有效性
        cell = row.getCell(cellNum++);
        String invalid = ExcelUtils.convertCellValueToString(cell);
        /* 有效性标识转换 */
        if (CharSequenceUtil.isNotBlank(invalid) && invalid.equals(CommonConstants.INVALID_YES_CN)) {
            resultData.setInvalid(CommonConstants.INVALID_YES_TAG);
        } else {
            resultData.setInvalid(CommonConstants.INVALID_NO_TAG);
        }

        // 生效日期
        cell = row.getCell(cellNum++);
        String effectiveDate = ExcelUtils.convertCellValueToString(cell);
        if (CharSequenceUtil.isNotBlank(effectiveDate)) {
            String year = effectiveDate.replace("年", "-");
            String month = year.replace("月", "-");
            String day = month.replace("日", "");
            resultData.setEffectiveDate(day);
        }

        // 失效日期
        cell = row.getCell(cellNum++);
        String expirationDate = ExcelUtils.convertCellValueToString(cell);
        if (CharSequenceUtil.isNotBlank(expirationDate)) {
            String year = expirationDate.replace("年", "-");
            String month = year.replace("月", "-");
            String day = month.replace("日", "");
            resultData.setExpirationDate(day);
        }

        // 创建人
        cell = row.getCell(cellNum++);
        String createName = ExcelUtils.convertCellValueToString(cell);
        resultData.setCreateName(null == createName ? "" : createName);

        // 创建时间
        cell = row.getCell(cellNum++);
        String createTime = ExcelUtils.convertCellValueToString(cell);
        resultData.setCreateTime(null == createTime ? "" : createTime);

        // 修改人
        cell = row.getCell(cellNum++);
        String modifyName = ExcelUtils.convertCellValueToString(cell);
        resultData.setModifyName(null == modifyName ? "" : modifyName);

        // 修改时间
        cell = row.getCell(cellNum++);
        String modifyTime = ExcelUtils.convertCellValueToString(cell);
        resultData.setModifyTime(null == modifyTime ? "" : modifyTime);

        if (invalid.equals(Constants.Indicators.INVALID_FLAG.getValue())) {
            return resultData;
        }
        return null;
    }

}

package com.swcares.aiot.dao;

import com.swcares.aiot.core.common.dao.JpaBaseDao;
import com.swcares.aiot.core.model.vo.MServiceRecordVo;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.dao.MServiceRecord
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/11/28 11:38
 * @version v1.0
 */
@Repository
public interface MServiceRecordDao extends JpaBaseDao<MServiceRecordVo, String> {
    @Query(nativeQuery = true,value = "SELECT " +
            " tsr1.flight_id, " +
            " sum( CASE tsr1.service_code WHEN 'CWN' THEN tsr1.used_number ELSE 0 END ) AS 'CWN', " +
            " sum( CASE tsr1.service_code WHEN 'CET' THEN tsr1.used_number ELSE 0 END ) AS 'CET', " +
            " sum( CASE tsr1.service_code WHEN 'CLT' THEN tsr1.used_number ELSE 0 END ) AS 'CLT', " +
            " sum( CASE tsr1.service_code WHEN 'CPSF' THEN tsr1.used_number ELSE 0 END ) AS 'CPSF', " +
            " sum( CASE tsr1.service_code WHEN 'CCSF' THEN tsr1.used_number ELSE 0 END ) AS 'CCSF', " +
            " sum( CASE tsr1.service_code WHEN 'CDSF' THEN tsr1.used_number ELSE 0 END ) AS 'CDSF', " +
            " sum( CASE tsr1.service_code WHEN 'CUM' THEN tsr1.used_number ELSE 0 END ) AS 'CUM', " +
            " sum( CASE tsr1.service_code WHEN 'CM' THEN tsr1.used_number ELSE 0 END ) AS 'CM', " +
            " sum( CASE tsr1.service_code WHEN 'CNS' THEN tsr1.used_number ELSE 0 END ) AS 'CNS', " +
            " sum( CASE tsr1.service_code WHEN 'CMUF' THEN tsr1.used_number ELSE 0 END ) AS 'CMUF', " +
            " sum( CASE tsr1.service_code WHEN 'CIUF' THEN tsr1.used_number ELSE 0 END ) AS 'CIUF', " +
            " sum( CASE tsr1.service_code WHEN 'CPUF' THEN tsr1.used_number ELSE 0 END ) AS 'CPUF', " +
            " sum( CASE tsr1.service_code WHEN 'CSUF' THEN tsr1.used_number ELSE 0 END ) AS 'CSUF', " +
            " sum( CASE tsr1.service_code WHEN 'CDUF' THEN tsr1.used_number ELSE 0 END ) AS 'CDUF', " +
            " sum( CASE tsr1.service_code WHEN 'CAUF' THEN tsr1.used_number ELSE 0 END ) AS 'CAUF', " +
            " sum( CASE tsr1.service_code WHEN 'CTUF' THEN tsr1.used_number ELSE 0 END ) AS 'CTUF', " +
            " sum( CASE tsr1.service_code WHEN 'CWUF' THEN tsr1.used_number ELSE 0 END ) AS 'CWUF', " +
            " sum( CASE tsr1.service_code WHEN 'CEUF' THEN tsr1.used_number ELSE 0 END ) AS 'CEUF', " +
            " sum( CASE tsr1.service_code WHEN 'CBAT' THEN tsr1.used_number ELSE 0 END ) AS 'CBAT', " +
            " sum( CASE tsr1.service_code WHEN 'CBET' THEN tsr1.used_number ELSE 0 END ) AS 'CBET', " +
            " sum( CASE tsr1.service_code WHEN 'CBAN' THEN tsr1.used_number ELSE 0 END ) AS 'CBAN', " +
            " sum( CASE tsr1.service_code WHEN 'CBEN' THEN tsr1.used_number ELSE 0 END ) AS 'CBEN', " +
            " sum( CASE tsr1.service_code WHEN 'CADF' THEN tsr1.used_number ELSE 0 END ) AS 'CADF', " +
            " sum( CASE tsr1.service_code WHEN 'COP' THEN tsr1.used_number ELSE 0 END ) AS 'COP'," +
            " sum( CASE tsr1.service_code WHEN 'CNF' THEN tsr1.used_number ELSE 0 END ) AS 'CNF'," +
            " sum( CASE tsr1.service_code WHEN 'CSUT' THEN tsr1.used_number ELSE 0 END ) AS 'CSUT'," +

            " sum( CASE tsr1.service_code WHEN 'CDP' THEN tsr1.used_number ELSE 0 END ) AS 'CDP'," +
            " sum( CASE tsr1.service_code WHEN 'CPN' THEN tsr1.used_number ELSE 0 END ) AS 'CPN'," +
            " sum( CASE tsr1.service_code WHEN 'CSP' THEN tsr1.used_number ELSE 0 END ) AS 'CSP'," +
            " sum( CASE tsr1.service_code WHEN 'CDC' THEN tsr1.used_number ELSE 0 END ) AS 'CDC'," +
            " sum( CASE tsr1.service_code WHEN 'CCW' THEN tsr1.used_number ELSE 0 END ) AS 'CCW'," +
            " sum( CASE tsr1.service_code WHEN 'CSC' THEN tsr1.used_number ELSE 0 END ) AS 'CSC'," +

            " sum( CASE tsr1.service_code WHEN 'CRE' THEN tsr1.used_number ELSE 0 END ) AS 'CRE'," +
            " sum( CASE tsr1.service_code WHEN 'CARE' THEN tsr1.used_number ELSE 0 END ) AS 'CARE'," +
            " sum( CASE tsr1.service_code WHEN 'CBRE' THEN tsr1.used_number ELSE 0 END ) AS 'CBRE'," +
            " sum( CASE tsr1.service_code WHEN 'CAM' THEN tsr1.used_number ELSE 0 END ) AS 'CAM'," +
            " sum( CASE tsr1.service_code WHEN 'CBM' THEN tsr1.used_number ELSE 0 END ) AS 'CBM'," +
            " 0 AS 'CBT' , " +//客桥会涉及到多客桥的情况，这里先设为0
            " '' AS 'bill_bus_data_item_id' " +
            "FROM " +
            " t_service_record  tsr1 USE INDEX(idx_service_record_recalc)" +
            "WHERE " +
            " tsr1.flight_id in :flightId " +
            " AND tsr1.invalid = '1'  " +
            "GROUP BY " +
            " flight_id")
    List<MServiceRecordVo> getServiceRecordByFlightId(@Param("flightId")List<String> flightId);


    @Query(nativeQuery = true,value = "SELECT  " +
            " flight_id, " +
            " used_number AS 'CBT', " +
            "bill_bus_data_item_id "+
            "FROM " +
            " t_service_record  use index(idx_service_record_recalc)" +
            "WHERE " +
            " invalid = '1'  " +
            " AND flight_id in :flightId " +
            " AND service_code = 'CBT' " +
            " AND used_number!=0")
    List<Map<String,Object>> getCBTServiceRecordByFlightId(@Param("flightId")List<String> flightId);
}

package com.swcares.aiot.core.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * ClassName：PassengerInfoSearchForm <br>
 * Description：(旅客信息查询条件)<br>
 * Copyright © 2020/6/16 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/6/16 17:36<br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "PassengerInfoSearchForm", description = "旅客信息查询条件")
public class PassengerInfoSearchForm {

    @NotBlank(message = "flightId不能为空")
    @Size(min=1,max=128,message = "flightId必须为1到128个字符长度")
    @ApiModelProperty(name = "flightId", value = "航班ID", required = true)
    private String flightId;

    @ApiModelProperty(name = "tkNo", value = "票号")
    private String tkNo;

    @ApiModelProperty(name = "pname", value = "旅客姓名")
    private String pname;

    @ApiModelProperty(name = "cabin", value = "舱位")
    private String cabin;
}

package com.swcares.aiot.core.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FlightBillExportDto extends FlightBillImportDto{

    @ApiModelProperty(name = "airportCode",value = "错误信息")
//    @Excel(name = "错误信息", orderNum = "26", width = 50)
    @ExcelProperty(value = "错误信息",index = 25)
    @ColumnWidth(50)
    @ContentFontStyle(color = 10,fontName="宋体",fontHeightInPoints = 11)
    @HeadFontStyle(color = 10)
    @ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER,verticalAlignment= VerticalAlignment.CENTER)
    @HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER,verticalAlignment= VerticalAlignment.CENTER)
    private String errorMsg;

}

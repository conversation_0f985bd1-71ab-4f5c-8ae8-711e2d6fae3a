package com.swcares.aiot.core.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * ClassName：com.swcares.form.SubmitForm
 * Description：提交对账
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/1/16 9:57
 * @version v1.0
 */
@Data
public class SubmitForm {

    @NotBlank(message = "airportCode不能为空")
    @Size(min=3,max=3,message = "airportCode必须为3个字符长度")
    @ApiModelProperty(name = "airportCode", value = "机场三字码", required = true)
    private String airportCode;

    @ApiModelProperty(name = "airlineCode", value = "航司二字码")
    private String airlineCode;

    @ApiModelProperty(name = "airlineShortName", value = "航司简称")
    private String airlineShortName;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @NotNull(message = "startDate不能为空")
    @ApiModelProperty(name = "startDate", value = "开始日期", required = true)
    private Date startDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @NotNull(message = "endDate不能为空")
    @ApiModelProperty(name = "endDate", value = "结束日期", required = true)
    private Date endDate;

    @ApiModelProperty(name = "flightDateType", value = "航班日期类型(1航班日期、2起降日期)")
    @NotNull(message = "日期类型不能为空")
    private int dateType;

    @ApiModelProperty(name = "feeCode", value = "费用代码")
    private String feeCode;

}

package com.swcares.aiot.core.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * ClassName：com.swcares.form.SubsidyFlightDetailDataForm
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/8/17 10:23
 * @version v1.0
 */
@Data
@ApiModel(value = "SubsidyFlightDetailDataForm" ,description = "航线补贴航班详情查询表单")
public class SubsidyFlightDetailDataForm {

    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(name = "startDate",value = "生效开始时间")
    private Date startDate;

    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(name = "endDate",value = "生效结束时间")
    private Date endDate;

    private String flightLineId;
}

package com.swcares.aiot.dao;

import com.swcares.aiot.core.common.dao.JpaBaseDao;
import com.swcares.aiot.core.model.entity.BillItemBus;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * ClassName：com.swcares.dao.BillItemBusDao
 * Description：
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2023/11/14 13:54
 * @version v1.0
 */
@Repository
public interface BillItemBusDao extends JpaBaseDao<BillItemBus, String> {

    @Modifying
    @Query(nativeQuery = true,
            value = "update t_bill_item_bus set invalid='0' where template_id in (:templateIdList) ")
    Integer deleteByTemplateId(@Param("templateIdList") List<Long> templateIdList);

    @Modifying
    @Query(nativeQuery = true,
            value = "update t_bill_item_bus set invalid='0'  ")
    Integer deleteAllItem();

    @Query(nativeQuery = true,
            value = "select item_name from t_bill_item_bus where invalid='1' and airport_code=:airportCode  ")
    List<String> getItemNameList(@Param("airportCode") String airportCode);

    @Query(nativeQuery = true,
            value = "select * from t_bill_item_bus where invalid='1' and airport_code=:airportCode  ")
    List<BillItemBus> getItemList(@Param("airportCode") String airportCode);

}

package com.swcares.aiot.core.model.vo;

import com.swcares.aiot.core.model.entity.Dept;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 *
 * ClassName：com.swcares.entities.vo.DeptVO <br>
 * Description： 部门拓展实体 <br>
 * <AUTHOR> <br>
 * date 2020/10/29 17:21<br>
 * @version v1.0 <br>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeptVO extends Dept {

    /**
     * 员工号
     */
    private String userNumber;

    /**
     * 部门子集
     */
    private List<DeptVO> deptList = Lists.newArrayList();

    /**
     * Title: adapt<br>
     * Author: wangxiong<br>
     * Description: 拷贝部门属性 <br>
     * Date:  10:26 <br>
     * @param dept
     * @return : DeptVO
     */
    public static DeptVO copyProperties(Dept dept){
        DeptVO deptVO = new DeptVO();
        BeanUtils.copyProperties(dept,deptVO);
        return deptVO;
    }
}

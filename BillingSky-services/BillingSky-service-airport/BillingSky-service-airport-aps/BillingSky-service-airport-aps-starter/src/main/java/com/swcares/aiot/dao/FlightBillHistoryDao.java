package com.swcares.aiot.dao;

import com.swcares.aiot.core.common.dao.JpaBaseDao;
import com.swcares.aiot.core.model.entity.FlightBillHistory;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-05-10 16:47
 */
@Repository
public interface FlightBillHistoryDao extends JpaBaseDao<FlightBillHistory, String> {

    @Modifying
    @Query(nativeQuery = true, value = "INSERT INTO `t_flight_bill_history` (id, `flight_bill_id`, `create_by`, `create_time`, `modified_by`, `modified_time`, `airport_code`, `airline_short_name`, `charge_price`, `fee_code`, `fee_name`, `flight_flag`, `flight_line`, `flight_no`, `flight_segment`, `flight_time`, `from_airport_code`, `pricing_amount`, `reg_no`, `settle_month`, `to_airport_code`, `unit_price`, `flight_date`, `flight_id`, `invalid`, `da_airport_code`, `flight_line_type`, `flight_model`, `flight_segment_type`, `settle_code`, `task_flag`, `service_record`, `submit`, `feedback`, `indicator_code`, `indicator_name`, `indicator_value`, `bill_bus_data_item_id`, revocation, operation, prove_file) " +
            "SELECT UUID_SHORT(), `id`, if(:name is not null, :name, settle_code), now(), `modified_by`, `modified_time`, `airport_code`, `airline_short_name`, `charge_price`, `fee_code`, `fee_name`, `flight_flag`, `flight_line`, `flight_no`, `flight_segment`, `flight_time`, `from_airport_code`, `pricing_amount`, `reg_no`, `settle_month`, `to_airport_code`, `unit_price`, `flight_date`, `flight_id`, `invalid`, `da_airport_code`, `flight_line_type`, `flight_model`, `flight_segment_type`, `settle_code`, `task_flag`, `service_record`, `submit`, `feedback`, `indicator_code`, `indicator_name`, `indicator_value`, `bill_bus_data_item_id`, revocation, :operation, `prove_file` " +
            "from t_flight_bill " +
            "WHERE id in (:billIds) ")
    void copyBillHistory(@Param("billIds") List<String> billIds, @Param("name") String name, @Param("operation") String operation);

    @Query(nativeQuery = true, value = "select * from t_flight_bill_history where flight_bill_id = :fltBillId  order by create_time desc limit :limit")
    List<FlightBillHistory> getHistoriesByFltBillId(@Param("fltBillId") String fltBillId, @Param("limit") Integer limit);

    @Query(nativeQuery = true, value = "select count(1) from t_flight_bill_history where flight_bill_id = :fltBillId ")
    long count(@Param("fltBillId") String fltBillId);

    @Modifying
    @Query(nativeQuery = true, value = "update t_flight_bill_history set invalid='0' where flight_bill_id in (:billIdList) ")
    void deleteByBillIdList(@Param("billIdList") List<String> billIdList);
}

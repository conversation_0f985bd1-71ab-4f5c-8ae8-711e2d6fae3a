package com.swcares.aiot.controller;

import com.swcares.aiot.TenantConvertUtil;
import com.swcares.aiot.client.IExcessLuggageTransOrderBizClient;
import com.swcares.aiot.core.cons.ExceptionCodes;
import com.swcares.aiot.model.dto.ExcessLuggageTransOrderBizPageDto;
import com.swcares.aiot.model.vo.ExcessLuggageTransOrderPageVo;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * ClassName：com.swcares.aiot.controller.ExcessLuggageTransOrderAssBizController<br>
 * Description：航司端逾重行李划拨账单接口 <br>
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved.<br>
 * Company：Aviation Cares Of Southwest Chen Du LTD<br>
 *
 * <AUTHOR>
 * date 2025/4/17 9:48<br>
 * @version v1.0<br>
 */
@Slf4j
@RestController
@ApiVersion("机场端-行李-api")
@RequestMapping("/excessLuggageTransOrderAps")
@Api(tags = "机场端逾重行李划拨账单接口")
public class ExcessLuggageTransOrderApsBizController {

    @Resource
    private IExcessLuggageTransOrderBizClient iExcessLuggageTransOrderBizClient;

    @ApiOperation(value = "划拨账单分页查询")
    @PostMapping("/page")
    PagedResult<List<ExcessLuggageTransOrderPageVo>> page(@RequestBody ExcessLuggageTransOrderBizPageDto dto) {
        String tenantCode = TenantConvertUtil.getTenantCode(TenantHolder.getTenant());
        if (ObjectUtils.isEmpty(tenantCode)) {
            log.error("机场端-逾重行李划拨站但接口未获取到当前租户的编码！tenantId={}", TenantHolder.getTenant());
            throw new BusinessException(ExceptionCodes.TENANT_CODE_NOT_FOUND_ERROR);
        }
        dto.setPartyCode(tenantCode);
        return iExcessLuggageTransOrderBizClient.page(dto);
    }
}

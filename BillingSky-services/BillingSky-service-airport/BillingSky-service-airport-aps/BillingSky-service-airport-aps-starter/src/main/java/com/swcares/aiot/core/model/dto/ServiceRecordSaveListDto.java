package com.swcares.aiot.core.model.dto;

import com.swcares.aiot.core.model.entity.ServiceRecord;
import com.swcares.aiot.core.model.entity.ServiceRecordConfirm;
import lombok.Data;

import java.util.*;

/**
 * ClassName：com.swcares.aiot.core.model.dto.ServiceRecordSaveListDto
 * Description：
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/10/21 19:08
 * @version v1.0
 */
@Data
public class ServiceRecordSaveListDto {

    //过夜仅进港或仅出港航班，数值缓存map
    private Map<String, ServiceRecord> isStayOvernightServiceRecordMap = new HashMap<>();
    //批量删除集合
    private Set<String> deleteList = new HashSet<>();
    private Set<String> deleteConfirmList = new HashSet<>();
    private Set<String> signDeleteList = new HashSet<>();
    private List<ServiceRecord> saveList = new ArrayList<>();
    private List<ServiceRecordConfirm> saveConfirmList = new ArrayList<>();
    //确认到航班时，先查出该航班所有的业务保障数据塞进去，然后处理新数据时，相同则删除，如果不同则将flightId塞进changeFlightIdSet
    private Map<String, Map<String, List<ServiceRecord>>> flightServiceMap = new HashMap<>();
    private Set<String> changeFlightIdSet = new HashSet<>();
}

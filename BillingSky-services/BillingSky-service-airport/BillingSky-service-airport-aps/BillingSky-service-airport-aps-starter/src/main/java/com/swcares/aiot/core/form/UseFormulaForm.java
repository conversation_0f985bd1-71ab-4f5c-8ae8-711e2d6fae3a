package com.swcares.aiot.core.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/*
 *
 * ClassName：UseFormulaForm <br>
 * Description：航司采用公式提交表单<br>
 * Copyright © 2020-5-18 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020-5-18 16:16<br>
 * @version v1.0 <br>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "UseFormulaForm",description = "航司采用公式提交表单")
public class UseFormulaForm {
    /*
     * field：公式id<br>
     * @since v1.0<br>
     */
    @NotBlank(message = "不能为空！")
    @ApiModelProperty(name = "formulaId",value = "公式id")
    private String formulaId;

	/*
     * field：航司id<br>
     * @since v1.0<br>
     */
    @Size(min = 1,max = 64,message = "航司id长度小于64位")
    @ApiModelProperty(name = "airlineId",value = "航司id")
    private String airlineId;
    /*
     * field：是否采用公式标识<br>
     * @since v1.0<br>
     */
    @ApiModelProperty(name = "active",value = "是否采用公式")
    private String active = "1";
    /*
     * field：计费方式<br>
     * @since v1.0<br>
     */
    @ApiModelProperty(name = "calcWay",value = "计费方式")
    private String calcWay;
    /*
     * field：计算变量：调整系数等<br>
     * @since v1.0<br>
     */
    @ApiModelProperty(name = "calcVariable",value = "计算变量 调整系数")
    private String calcVariable;
    /*
     * field：生效开始时间<br>
     * @since v1.0<br>
     */
    @NotBlank(message = "不能为空！")
    @ApiModelProperty(name = "startDate",value = "生效开始时间")
    private String startDate;
    /*
     * field：生效结束时间<br>
     * @since v1.0<br>
     */
    @NotBlank(message = "不能为空！")
    @ApiModelProperty(name = "endDate",value = "生效结束时间")
    private String endDate;

}

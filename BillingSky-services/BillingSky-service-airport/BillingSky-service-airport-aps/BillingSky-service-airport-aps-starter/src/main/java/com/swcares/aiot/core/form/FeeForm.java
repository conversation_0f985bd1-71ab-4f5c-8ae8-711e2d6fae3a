package com.swcares.aiot.core.form;

import com.swcares.aiot.core.common.util.valid.group.Update;
import com.swcares.aiot.core.model.vo.FormulaVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.Date;


/*
 *
 * ClassName：FeeForm <br> Description：用于创建费用时的提交表单<br> Copyright © 2020-5-14 xnky.travelsky.net Inc.
 * All rights reserved. <br> Company：Aviation Cares Of Southwest Chen Du LTD <br>
 * 
 * <AUTHOR> <br> date 2020-5-14 16:43<br>
 * 
 * @version v1.0 <br>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "FeeForm", description = "费用")
public class FeeForm {
    /*
     * field：费用Id<br>
     * 
     * @since v1.0<br>
     */
    @NotBlank(message = "费用Id不能为空！", groups = {Update.class})
    @ApiModelProperty(name = "feeId", value = "费用Id")
    private String feeId;
    /*
     * field：费用类别，用于区分是民航标准费用还是自定义费用<br>
     * 
     * @since v1.0<br>
     */
    @Pattern(regexp = "^[SC]+$", message = "费用类别输入内容不正确")
    @NotBlank(message = "费用类别不能为空！")
    @Size(max = 1, message = "费用类别长度为1个字符")
    @ApiModelProperty(name = "feeType", value = "费用类别", required = true)
    private String feeType;
    /*
     * field：费用名称，不允许重复<br>
     * 
     * @since v1.0<br>
     */
    @NotBlank(message = "费用名称不能为空！")
    @Size(max = 20, message = "费用名称长度为小于20个字符")
    @ApiModelProperty(name = "feeName", value = "费用名称", required = true)
    private String feeName;
    /*
     * field：费用代码<br>
     * 
     * @since v1.0<br>
     */
    @Pattern(regexp = "^[A-Z0-9-]+$", message = "费用代码输入内容不正确")
    @NotBlank(message = "费用代码不能为空！")
    @Size(max = 20, message = "费用代码长度为小于20个字符")
    @ApiModelProperty(name = "feeCode", value = "费用代码", required = true)
    private String feeCode;
    /*
     * field：使用该规则的机场代码<br>
     * 
     * @since v1.0<br>
     */
    // @Pattern(regexp = "^[A-Z]$",message = "机场三字码输入内容不正确")
    @ApiModelProperty(name = "airportCode", value = "机场三字码")
    private String airportCode;
    /*
     * field：公式名称，同一费用代码下，公式名不允许重复<br>
     * 
     * @since v1.0<br>
     */

    @ApiModelProperty(name = "formulaName", value = "公式名称")
    private String formulaName;
    /*
     * field：用于描述该公式的用途，使用场景<br>
     * 
     * @since v1.0<br>
     */
    @ApiModelProperty(name = "description", value = "公式描述")
    private String description;
    /*
     * field：机场类别<br>
     * 
     * @since v1.0<br>
     */
    @ApiModelProperty(name = "airportLevel", value = "机场类别")
    private int airportLevel;
    /*
     * field：机场属性，描述机场是否为出入境机场<br>
     * 
     * @since v1.0<br>
     */
    @Pattern(regexp = "^[0-2]+$", message = "机场属性输入内容不正确")
    @Size(min = 1, max = 1, message = "机场属性长度为1个字符")
    @ApiModelProperty(name = "airportType", value = "机场属性")
    private String airportType;
    /*
     * field：航班属性，描述航班是国内还是国际航班<br>
     * 
     * @since v1.0<br>
     */
    @Pattern(regexp = "^[ID]+$", message = "航班属性输入内容不正确")
    @Size(min = 1, max = 1, message = "航班属性长度为1个字符")
    @ApiModelProperty(name = "flightType", value = "航班属性")
    private String flightType;
    /*
     * field：归集方式，描述是尽起飞收费，还是起降各一定系数等信息<br>
     * 
     * @since v1.0<br>
     */
    @Pattern(regexp = "^[DAHC]+$", message = "归集方式输入内容不正确")
    @Size(min = 1, max = 1, message = "归集方式长度为1个字符")
    @ApiModelProperty(name = "belongWay", value = "归集方式")
    private String belongWay;
    /*
     * field：计价量条件，1:单位单价|2:带条件的单位单价|3:固定价格，可不与任何项关联（含市场调节价）<br>
     * 
     * @since v1.0<br>
     */
    @Pattern(regexp = "^[1-3]+$", message = "计价量条件输入内容不正确")
    @Size(min = 1, max = 1, message = "计价量条件长度为1个字符，")
    @ApiModelProperty(name = "pricingWay", value = "计价量条件")
    private String pricingWay;
    /*
     * field：收费单位，长度为32个字符<br>
     * 
     * @since v1.0<br>
     */
    @Pattern(regexp = "^[TNHFMS]+$", message = "收费单位输入内容不正确")
    @Size(min = 1, max = 1, message = "收费单位长度为1个字符，")
    @ApiModelProperty(name = "feeUnit", value = "收费单位")
    private String feeUnit;
    /*
     * field：公式，用于计算费用<br>
     * 
     * @since v1.0<br>
     */
    @ApiModelProperty(name = "formula", value = "公式")
    private String formula;

    @ApiModelProperty(name = "formulaVoList", value = "带条件公式使用 ")
    private ArrayList<FormulaVo> formulaVoList;

    @ApiModelProperty(name = "startTime", value = "公式启用时间")
    private Date startTime;

    @ApiModelProperty(name = "endTime", value = "公式停用时间")
    private Date endTime;

    @ApiModelProperty(name = "isServiceFee", value = "机务费用")
    private Integer isServiceFee;

    @ApiModelProperty(name = "airlineId", value = "航司id")
    private String airlineId;

    @ApiModelProperty(name = "formulaType", value = "公式参数类型1：纯数字|2：航班数据类型|3：业务保障数据类型|4：混合类型")
    private String formulaType;

    @ApiModelProperty(name = "isTransit", value = "是否过站 0不限；1过站；2非过站")
    @Column(name = "is_transit", columnDefinition = "是否过站 0不限；1过站；2非过站")
    private String isTransit;


    @ApiModelProperty(name = "altSpecial",value = "备降航班特殊收费:1是；0否")
    private String altSpecial;

    @ApiModelProperty(name = "specialVariable",value = "关联数据项")
    private String specialVariable;
}

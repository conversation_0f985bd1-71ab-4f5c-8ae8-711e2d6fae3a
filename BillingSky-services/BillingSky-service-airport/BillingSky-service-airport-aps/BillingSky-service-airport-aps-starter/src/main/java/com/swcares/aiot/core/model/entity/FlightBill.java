package com.swcares.aiot.core.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.aiot.core.common.entity.AirportBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ClassName：FlightBill <br>
 * Description：(航班费用明细账单实体类)<br>
 * Copyright © 2020/6/1 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020/6/1 11:10<br>
 * @version v1.0 <br>
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "t_flight_bill")
@ApiModel(value = "FlightBill", description = "航班费用明细账单")
public class FlightBill extends AirportBaseEntity implements Serializable, Cloneable {

    @Column(name = "bill_bus_data_item_id", columnDefinition = "VARCHAR(128)    COMMENT '签单保障项id'")
    @ApiModelProperty(name = "billBusDataItemId", value = "签单保障项id")
    private String billBusDataItemId;// CBT

    @ApiModelProperty(name = "flightId", value = "航班ID")
    @Column(name = "flight_id", columnDefinition = "VARCHAR(128)  COMMENT '航班ID'")
    private String flightId;//CBT

    @ApiModelProperty(name = "airlineShortName", value = "航司简称")
    @Column(name = "airline_short_name", columnDefinition = "VARCHAR(32)    COMMENT '航司简称'")
    private String airlineShortName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(name = "flightDate", value = "航班日期")
    @Column(name = "flight_date", columnDefinition = "DATE    COMMENT '航班日期'")
    private Date flightDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "flightTime", value = "起降时间")
    @Column(name = "flight_time", columnDefinition = "DATETIME    COMMENT '起降时间'")
    private Date flightTime;

    @ApiModelProperty(name = "flightNo", value = "航班号")
    @Column(name = "flight_no", columnDefinition = "VARCHAR(32)    COMMENT '航班号'")
    private String flightNo;

    @ApiModelProperty(name = "flightLine", value = "航线")
    @Column(name = "flight_line", columnDefinition = "VARCHAR(32)    COMMENT '航线'")
    private String flightLine;

    @ApiModelProperty(name = "flightLineType", value = "航线性质;国际:I|国内:D")
    @Column(name = "flight_line_type", columnDefinition = "CHAR(1)    COMMENT '航线性质 国际:I|国内:D'")
    private String flightLineType;

    @ApiModelProperty(name = "flightSegment", value = "航段")
    @Column(name = "flight_segment", columnDefinition = "VARCHAR(32)    COMMENT '航段'")
    private String flightSegment;

    @ApiModelProperty(name = "flightSegmentType", value = "航段性质;国际:I|国内:D")
    @Column(name = "flight_segment_type", columnDefinition = "CHAR(1)    COMMENT '航段性质 国际:I|国内:D'")
    private String flightSegmentType;

    @ApiModelProperty(name = "daAirportCode", value = "采集机场三字码")
    @Column(name = "da_airport_code", columnDefinition = "VARCHAR(3)    COMMENT '采集机场三字码'")
    private String daAirportCode;

    @ApiModelProperty(name = "fromAirportCode", value = "起飞机场三字码")
    @Column(name = "from_airport_code", columnDefinition = "VARCHAR(3)    COMMENT '起飞机场三字码'")
    private String fromAirportCode;

    @ApiModelProperty(name = "toAirportCode", value = "到达机场三字码")
    @Column(name = "to_airport_code", columnDefinition = "VARCHAR(3)    COMMENT '到达机场三字码'")
    private String toAirportCode;

    @ApiModelProperty(name = "regNo", value = "飞机注册号")
    @Column(name = "reg_no", columnDefinition = "VARCHAR(32)    COMMENT '飞机注册号'")
    private String regNo;

    @ApiModelProperty(name = "flightModel", value = "机型")
    @Column(name = "flight_model", columnDefinition = "VARCHAR(32)    COMMENT '机型'")
    private String flightModel;

    @ApiModelProperty(name = "flightFlag", value = "起降标识")
    @Column(name = "flight_flag", columnDefinition = "CHAR(1)    COMMENT '起降标识'")
    private String flightFlag;

    @ApiModelProperty(name = "taskFlag", value = "任务标识")
    @Column(name = "task_flag", columnDefinition = "VARCHAR(3)    COMMENT '任务标识'")
    private String taskFlag;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(name = "settleMonth", value = "结算月份")
    @Column(name = "settle_month", columnDefinition = "DATE    COMMENT '结算月份'")
    private Date settleMonth;

    @ApiModelProperty(name = "settleCode", value = "结算代码")
    @Column(name = "settle_code", columnDefinition = "VARCHAR(3)    COMMENT '结算代码'")
    private String settleCode;

    @ApiModelProperty(name = "feeCode", value = "费用代码")
    @Column(name = "fee_code", columnDefinition = "VARCHAR(32)    COMMENT '费用代码'")
    private String feeCode;//CBT

    @ApiModelProperty(name = "feeName", value = "费用名称")
    @Column(name = "fee_name", columnDefinition = "VARCHAR(64)    COMMENT '费用名称'")
    private String feeName;

    @ApiModelProperty(name = "PRICING_AMOUNT", value = "计价量 计价量条件|=0 计价量指标项目的数量|=1 和归集方式相关")
    @Column(name = "pricing_amount", columnDefinition = "DECIMAL(32,4)    COMMENT '计价量 计价量条件|=0 计价量指标项目的数量|=1 " +
            "和归集方式相关'")
    private BigDecimal pricingAmount;

    @ApiModelProperty(name = "unitPrice", value = "收费单价")
    @Column(name = "unit_price", columnDefinition = "DECIMAL(32,4)    COMMENT '收费单价'")
    private BigDecimal unitPrice;

    @ApiModelProperty(name = "chargePrice", value = "收费金额")
    @Column(name = "charge_price", columnDefinition = "DECIMAL(32,4)    COMMENT '收费金额'")
    private BigDecimal chargePrice;

    @ApiModelProperty(name = "invalid", value = "数据有效 有效:1|无效:0")
    @Column(name = "invalid", columnDefinition = "CHAR(1)    COMMENT '数据有效 有效:1|无效:0'")
    private String invalid = "1";

    @ApiModelProperty(name = "serviceRecord", value = "特车结算代码")
    @Column(name = "service_record", columnDefinition = "VARCHAR(10)   COMMENT '特车结算代码")
    private String serviceRecord;

    @ApiModelProperty(name = "submit", value = "账单状态（0未提交 1已确认 2有争议 3待审核 4拒绝处理）")
    @Column(name = "submit", columnDefinition = "CHAR(1)    COMMENT '账单状态（0未提交 1已确认 2有争议 3待审核 4拒绝处理）")
    private String submit="0";

    @ApiModelProperty(name = "feedback ", value = "航司反馈 ")
    @Column(name = "feedback ", columnDefinition = "VARCHAR(255)    COMMENT '航司反馈 ")
    private String feedback;

    @Column(name = "indicator_code", columnDefinition = "VARCHAR(32)    COMMENT '指标项代码'")
    @ApiModelProperty(name = "indicatorCode", value = "指标项代码")
    private String indicatorCode;

    @Column(name = "indicator_name", columnDefinition = "VARCHAR(64)    COMMENT '指标项名称'")
    @ApiModelProperty(name = "indicatorName", value = "指标项名称")
    private String indicatorName;

    @Column(name = "indicator_value", columnDefinition = "VARCHAR(64)    COMMENT '指标项值'")
    @ApiModelProperty(name = "indicatorValue", value = "指标项值")
    private String indicatorValue;

    @ApiModelProperty(name = "revocation", value = "撤销状态(1申请撤销 2拒绝撤销 3同意撤销)")
    private Integer revocation;

    @ApiModelProperty(name = "manually_delete",value = "手动删除状态（0正常，1删除）")
    private Integer manuallyDelete = 0;

    @ApiModelProperty(name = "chain_id",value = "航旅链存证id")
    private String chainId;

    @ApiModelProperty(name = "transaction_id",value = "航旅链交易ID")
    private String transactionId;

    @ApiModelProperty(value = "证明文件(json格式)")
    private String proveFile;

    @ApiModelProperty(name = "serviceStartTime",value = "指标项开始时间")
    private Date serviceStartTime ;

    @ApiModelProperty(name = "serviceEndTime",value = "指标项结束时间")
    private Date serviceEndTime ;
}

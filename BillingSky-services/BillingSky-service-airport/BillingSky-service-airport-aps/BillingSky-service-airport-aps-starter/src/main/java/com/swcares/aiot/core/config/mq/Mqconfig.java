package com.swcares.aiot.core.config.mq;

import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.Connection;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;

import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.swcares.aiot.core.importer.listener.MqSignListener;

import lombok.extern.slf4j.Slf4j;

/**
 * ClassName：com.swcares.config.mq.Mqconfig
 * Description：
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/6/13 15:33
 * @version v1.0
 */
//@Configuration
@Slf4j
public class Mqconfig {

    @Value("${spring.rabbitmq.host}")
    private String host;
    @Value("${spring.rabbitmq.port}")
    private int port;
    @Value("${spring.rabbitmq.username}")
    private String username;
    @Value("${spring.rabbitmq.password}")
    private String password;
    @Value("${spring.rabbitmq.virtual-host}")
    private String virtualHost;



    //@Bean("newContainerFactory")
    public SimpleRabbitListenerContainerFactory newContainerFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("connectionFactory") ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory listenerContainerFactory =
                new SimpleRabbitListenerContainerFactory();
        listenerContainerFactory.setAcknowledgeMode(AcknowledgeMode.MANUAL);

        configurer.configure(listenerContainerFactory, connectionFactory);
        //注册一个拦截器来全局处理日志接收
        log.debug("configuration secondQueue ........................");
        try(Connection connection = connectionFactory.createConnection(); Channel channel = connection.createChannel(false)){
            // 注册交换机
            channel.exchangeDeclare(MqSignListener.DIRECT_EXCHANGE, ExchangeTypes.DIRECT, true);
            // 注册队列
            channel.queueDeclare(MqSignListener.QUEUE_NAME_SYNC_FULL, true, false, false, null);
            // 绑定队列交换机
            channel.queueBind(MqSignListener.QUEUE_NAME_SYNC_FULL, MqSignListener.DIRECT_EXCHANGE,
                    MqSignListener.ROUTING_KEY_SYNC_FULL);

            // 注册队列
            AMQP.Queue.DeclareOk declareOk= channel.queueDeclare(MqSignListener.QUEUE_NAME_SYNC_INCREMENTAL, false, false, false,
                    null);

            // 绑定队列交换机
            channel.queueBind(MqSignListener.QUEUE_NAME_SYNC_INCREMENTAL,
                    MqSignListener.DIRECT_EXCHANGE, MqSignListener.ROUTING_KEY_SYNC_INCREMENTAL);


            // 注册队列
            channel.queueDeclare(MqSignListener.QUEUE_NAME_ITEM_UPDATE, true, false, false, null);
            // 绑定队列交换机
            channel.queueBind(MqSignListener.QUEUE_NAME_ITEM_UPDATE, MqSignListener.DIRECT_EXCHANGE,
                    MqSignListener.ROUTING_KEY_ITEM_UPDATE);


            // 注册队列
            channel.queueDeclare(MqSignListener.QUEUE_NAME_KEY_ITEM_INSERT, true, false, false,
                    null);
            // 绑定队列交换机
            channel.queueBind(MqSignListener.QUEUE_NAME_KEY_ITEM_INSERT,
                    MqSignListener.DIRECT_EXCHANGE, MqSignListener.ROUTING_KEY_ITEM_INSERT);

            // 注册队列
            channel.queueDeclare(MqSignListener.QUEUE_NAME_KEY_ITEM_DELETE, true, false, false,
                    null);
            // 绑定队列交换机
            channel.queueBind(MqSignListener.QUEUE_NAME_KEY_ITEM_DELETE,
                    MqSignListener.DIRECT_EXCHANGE, MqSignListener.ROUTING_KEY_ITEM_DELETE);

            // 注册队列
            channel.queueDeclare(MqSignListener.QUEUE_NAME_SYNC_BUS_FULL, true, false, false,
                    null);
            // 绑定队列交换机
            channel.queueBind(MqSignListener.QUEUE_NAME_SYNC_BUS_FULL,
                    MqSignListener.DIRECT_EXCHANGE, MqSignListener.ROUTING_KEY_SYNC_BUS_FULL);

            // 注册队列
            channel.queueDeclare(MqSignListener.QUEUE_NAME_KEY_BUS_ITEM_FULL, true, false, false,
                    null);
            // 绑定队列交换机
            channel.queueBind(MqSignListener.QUEUE_NAME_KEY_BUS_ITEM_FULL,
                    MqSignListener.DIRECT_EXCHANGE, MqSignListener.ROUTING_KEY_BUS_ITEM_FULL);


            // 注册队列
            channel.queueDeclare(MqSignListener.QUEUE_NAME_KEY_BUS_ITEM_DELETE, true, false, false,
                    null);
            // 绑定队列交换机
            channel.queueBind(MqSignListener.QUEUE_NAME_KEY_BUS_ITEM_DELETE,
                    MqSignListener.DIRECT_EXCHANGE, MqSignListener.ROUTING_KEY_BUS_ITEM_DELETE);

        } catch (Exception e) {
            log.error("出现业务异常", e);
        }

        //return listenerContainerFactory;
        return null;
    }

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aiot.mapper.FlightInfoBizMapper">

    <select id="pageFlightInfoByCondition" resultType="com.swcares.aiot.core.model.vo.FlightInfoVoNew">
        SELECT
            tfi.id, tfi.variable_status,
            CASE
                WHEN tfic.flight_no IS NOT NULL
                    AND tfi.flight_no != tfic.flight_no THEN
                    CONCAT( tfi.flight_no, ',', tfic.flight_no ) ELSE tfi.flight_no
                END flight_no,
            CASE
                WHEN tfic.flight_time IS NOT NULL
                    AND tfi.flight_time != tfic.flight_time THEN
                    CONCAT( tfi.flight_time, ',', tfic.flight_time ) ELSE tfi.flight_time
                END flight_time,
            CASE
                WHEN tfic.flight_flag IS NOT NULL
                    AND tfi.flight_flag != tfic.flight_flag THEN
                    CONCAT( tfi.flight_flag, ',', tfic.flight_flag ) ELSE tfi.flight_flag
                END flight_flag,
            CASE
                WHEN tfic.flight_line IS NOT NULL
                    AND tfi.flight_line != tfic.flight_line THEN
                    CONCAT( tfi.flight_line, ',', tfic.flight_line ) ELSE tfi.flight_line
                END flight_line,
            CASE
                WHEN tfic.flight_segment IS NOT NULL
                    AND tfi.flight_segment != tfic.flight_segment THEN
                    CONCAT( tfi.flight_segment, ',', tfic.flight_segment ) ELSE tfi.flight_segment
                END flight_segment,
            CASE
                WHEN tfic.flight_line_type IS NOT NULL
                    AND tfi.flight_line_type != tfic.flight_line_type THEN
                    CONCAT( tfi.flight_line_type, ',', tfic.flight_line_type ) ELSE tfi.flight_line_type
                END flight_line_type,
            CASE
                WHEN tfic.reg_no IS NOT NULL
                    AND tfi.reg_no != tfic.reg_no THEN
                    CONCAT( tfi.reg_no, ',', tfic.reg_no ) ELSE tfi.reg_no
                END reg_no,
            CASE
                WHEN tfic.adult_number IS NOT NULL
                    AND tfi.adult_number != tfic.adult_number THEN
                    CONCAT( tfi.adult_number, ',', tfic.adult_number ) ELSE tfi.adult_number
                END adult_number,
            CASE
                WHEN tfic.child_number IS NOT NULL
                    AND tfi.child_number != tfic.child_number THEN
                    CONCAT( tfi.child_number, ',', tfic.child_number ) ELSE tfi.child_number
                END child_number,
            CASE
                WHEN tfic.infant_number IS NOT NULL
                    AND tfi.infant_number != tfic.infant_number THEN
                    CONCAT( tfi.infant_number, ',', tfic.infant_number ) ELSE tfi.infant_number
                END infant_number,
            CASE
                WHEN tfic.first_class_number IS NOT NULL
                    AND tfi.first_class_number != tfic.first_class_number THEN
                    CONCAT( tfi.first_class_number, ',', tfic.first_class_number ) ELSE tfi.first_class_number
                END first_class_number,
            CASE
                WHEN tfic.club_class_number IS NOT NULL
                    AND tfi.club_class_number != tfic.club_class_number THEN
                    CONCAT( tfi.club_class_number, ',', tfic.club_class_number ) ELSE tfi.club_class_number
                END club_class_number,
            CASE
                WHEN tfic.economy_class_number IS NOT NULL
                    AND tfi.economy_class_number != tfic.economy_class_number THEN
                    CONCAT( tfi.economy_class_number, ',', tfic.economy_class_number ) ELSE tfi.economy_class_number
                END economy_class_number,
            CASE
                WHEN tfic.transit_adult_number IS NOT NULL
                    AND tfi.transit_adult_number != tfic.transit_adult_number THEN
                    CONCAT( tfi.transit_adult_number, ',', tfic.transit_adult_number ) ELSE tfi.transit_adult_number
                END transit_adult_number,
            CASE
                WHEN tfic.transit_child_number IS NOT NULL
                    AND tfi.transit_child_number != tfic.transit_child_number THEN
                    CONCAT( tfi.transit_child_number, ',', tfic.transit_child_number ) ELSE tfi.transit_child_number
                END transit_child_number,
            CASE
                WHEN tfic.transit_infant_number IS NOT NULL
                    AND tfi.transit_infant_number != tfic.transit_infant_number THEN
                    CONCAT( tfi.transit_infant_number, ',', tfic.transit_infant_number ) ELSE tfi.transit_infant_number
                END transit_infant_number,
            CASE
                WHEN tfic.diplomatic_passport_number IS NOT NULL
                    AND tfi.diplomatic_passport_number != tfic.diplomatic_passport_number THEN
                    CONCAT( tfi.diplomatic_passport_number, ',', tfic.diplomatic_passport_number ) ELSE tfi.diplomatic_passport_number
                END diplomatic_passport_number,
            CASE
                WHEN tfic.card_holder_number IS NOT NULL
                    AND tfi.card_holder_number != tfic.card_holder_number THEN
                    CONCAT( tfi.card_holder_number, ',', tfic.card_holder_number ) ELSE tfi.card_holder_number
                END card_holder_number,
            CASE
                WHEN tfic.accompanying_card_holder_number IS NOT NULL
                    AND tfi.accompanying_card_holder_number != tfic.accompanying_card_holder_number THEN
                    CONCAT( tfi.accompanying_card_holder_number, ',', tfic.accompanying_card_holder_number ) ELSE tfi.accompanying_card_holder_number
                END accompanying_card_holder_number,
            CASE
                WHEN tfic.important_number IS NOT NULL
                    AND tfi.important_number != tfic.important_number THEN
                    CONCAT( tfi.important_number, ',', tfic.important_number ) ELSE tfi.important_number
                END important_number,
            CASE
                WHEN tfic.accompanying_important_number IS NOT NULL
                    AND tfi.accompanying_important_number != tfic.accompanying_important_number THEN
                    CONCAT( tfi.accompanying_important_number, ',', tfic.accompanying_important_number ) ELSE tfi.accompanying_important_number
                END accompanying_important_number,
            CASE
                WHEN tfic.bag_number IS NOT NULL
                    AND tfi.bag_number != tfic.bag_number THEN
                    CONCAT( tfi.bag_number, ',', tfic.bag_number ) ELSE tfi.bag_number
                END bag_number,
            CASE
                WHEN tfic.bag IS NOT NULL
                    AND tfi.bag != tfic.bag THEN
                    CONCAT( TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM tfi.bag)), ',', TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM tfic.bag)) )
                    ELSE TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM tfi.bag))
                END bag,
            CASE
                WHEN tfic.mail IS NOT NULL
                    AND tfi.mail != tfic.mail THEN
                    CONCAT( TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM tfi.mail)), ',',TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM tfic.mail)) )
                    ELSE TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM tfi.mail))
                END mail,
            CASE
                WHEN tfic.cargo IS NOT NULL
                    AND tfi.cargo != tfic.cargo THEN
                    CONCAT( TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM tfi.cargo)), ',', TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM tfic.cargo)) )
                    ELSE TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM tfi.cargo))
                END cargo,
            tfi.data_status as dataStatus,
            CASE
                WHEN tfic.confirm_code IS NOT NULL
                    AND tfi.confirm_code != tfic.confirm_code THEN
                    CONCAT( tfi.confirm_code, ',', tfic.confirm_code ) ELSE tfi.confirm_code
                END confirm_code,
            CASE
                WHEN tfic.is_modify_flight_time IS NOT NULL
                    AND tfi.is_modify_flight_time != tfic.is_modify_flight_time THEN
                    CONCAT( tfi.is_modify_flight_time, ',', tfic.is_modify_flight_time ) ELSE tfi.is_modify_flight_time
                END is_modify_flight_time,
            CASE
                WHEN tfic.flight_date IS NOT NULL
                    AND tfi.flight_date != tfic.flight_date THEN
                    CONCAT( tfi.flight_date, ',', tfic.flight_date ) ELSE tfi.flight_date
                END flight_date,
            CASE
                WHEN tfic.task_flag IS NOT NULL
                    AND tfi.task_flag != tfic.task_flag THEN
                    CONCAT( tfi.task_flag, ',', tfic.task_flag ) ELSE tfi.task_flag
                END task_flag,
            CASE
                WHEN tfic.psg_number IS NOT NULL
                    AND tfi.psg_number != tfic.psg_number THEN
                    CONCAT( tfi.psg_number, ',', tfic.psg_number ) ELSE tfi.psg_number
                END psg_number,
            CASE
                WHEN tfic.stay_start_time IS NOT NULL
                    AND tfi.stay_start_time != tfic.stay_start_time THEN
                    CONCAT ( tfi.stay_start_time, ',', tfic.stay_start_time ) ELSE tfi.stay_start_time
                END stay_start_time,
            CASE
                WHEN tfic.stay_end_time IS NOT NULL
                    AND tfi.stay_end_time != tfic.stay_end_time THEN
                    CONCAT( tfi.stay_end_time, ',', tfic.stay_end_time ) ELSE tfi.stay_end_time
                END stay_end_time,
            CASE
                WHEN tfic.stay_time IS NOT NULL
                    AND tfi.stay_time != tfic.stay_time THEN
                    CONCAT( tfi.stay_time, ',', tfic.stay_time ) ELSE tfi.stay_time
                END stay_time
        FROM
            t_flight_info AS tfi
                LEFT JOIN t_flight_info_cache tfic ON tfi.id = tfic.id
        <where>
            tfi.airport_code = #{form.airportCode}
            AND tfi.invalid = '1'
            <if test="form.startDate != null and form.endDate != null"> and tfi.flight_date BETWEEN #{form.startDate} AND #{form.endDate} </if>
            <if test="form.flightTimeStartDate != null and form.flightTimeEndDate != null"> and tfi.flight_time BETWEEN #{form.flightTimeStartDate} AND #{form.flightTimeEndDate} </if>
            <if test="form.airlineCode != null and form.airlineCode != ''"> and tfi.airline_code = #{form.airlineCode} </if>
            <if test="form.regNo != null and form.regNo != ''"> and tfi.reg_no = #{form.regNo} </if>
            <if test="form.flightNo != null and form.flightNo != ''"> and tfi.flight_no = #{form.flightNo} </if>
            <if test="form.flightFlag != null and form.flightFlag != ''"> and tfi.flight_flag = #{form.flightFlag} </if>
            <if test="form.dataStatus != null"> and tfi.data_status = #{form.dataStatus} </if>
            <if test="form.dataLost != null and form.dataLost != '' and form.dataLost != '2'.toString()">
                <choose>
                    <when test="form.dataLost == '0'.toString()">
                        and tfi.reg_no is not null and tfi.flight_time is not null
                        and tfi.task_flag is not null and tfi.psg_number > 0
                    </when>
                    <when test="form.dataLost == '1'.toString()">
                        and tfi.data_status in (0, 3)
                        AND ( tfi.reg_no IS NULL OR tfi.flight_time IS NULL OR tfi.task_flag IS NULL OR tfi.psg_number = 0 )
                    </when>
                </choose>
            </if>
            <if test="form.variableStatus != null">
               AND tfi.variable_status = #{form.variableStatus}
            </if>
        </where>
        ORDER BY tfi.flight_time DESC
    </select>

    <select id="getACCAData" resultType="com.swcares.aiot.core.model.vo.ACCAExcelVo">
        SELECT
            t.airport_code,
            t.flight_date,
            t.flight_no,
            t.reg_no,
            t.flight_line,
            t.flight_line_type,
            t.flight_segment,
            t.flight_segment_type,
            t.flight_flag,
            t.flight_time,
            t.adult_number,
            t.child_number,
            t.infant_number,
            t.transit_adult_number,
            t.transit_child_number,
            t.transit_infant_number,
            t.bag,
            t.mail,
            t.cargo,
            t.task_flag
        FROM
            t_flight_info AS t
        <where>
            t.invalid = '1'
            <if test="form.startDate != null and form.endDate != null"> and t.flight_date BETWEEN #{form.startDate} AND #{form.endDate} </if>
            <if test="form.flightTimeStartDate != null and form.flightTimeEndDate != null"> and t.flight_time BETWEEN #{form.flightTimeStartDate} AND #{form.flightTimeEndDate} </if>
            <if test="form.airportCode != null and form.airportCode != ''"> and t.airport_code = #{form.airportCode} </if>
            <if test="form.airlineCode != null and form.airlineCode != ''"> and t.airline_code = #{form.airlineCode} </if>
            <if test="form.flightNo != null and form.flightNo != ''"> and t.flight_no = #{form.flightNo} </if>
            <if test="form.flightFlag != null and form.flightFlag != ''"> and t.flight_flag = #{form.flightFlag} </if>
        </where>
    </select>

</mapper>

package com.swcares.aiot.fallback;

import com.swcares.aiot.client.ICustomerBizClient;
import com.swcares.aiot.utils.LocalDateTimeUtils;
import com.swcares.baseframe.common.base.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;

import java.util.Collections;

/**
 * <AUTHOR>
 */
@Slf4j
public class CustomerBizFallback implements FallbackFactory<ICustomerBizClient> {

    @Override
    public ICustomerBizClient create(Throwable cause) {
        return customerIataA -> {
            log.error("{}:产生了熔断-[{}]", LocalDateTimeUtils.ofCtt(), "计算中心(calculate)");
            return BaseResult.ok(Collections.emptyList());
        };
    }
}

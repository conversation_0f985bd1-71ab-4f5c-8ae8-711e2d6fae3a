package com.swcares.aiot.model.vo;

import com.swcares.aiot.model.dto.ActIndexCaluDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ActExpensesCaluVo {
    @ApiModelProperty("费用项-内部唯一编码")
    private String expensesSole;

    @ApiModelProperty("费用项-名称")
    private String expensesName;

    @ApiModelProperty("费用项-编码")
    private String expensesCode;

    @ApiModelProperty("费用项-来源 [1 通过航班数据精准计算，0 通过阈值计算]")
    private Integer expensesOrigin;

    @ApiModelProperty(value = "费用金额")
    private BigDecimal expensesAmount;

    @ApiModelProperty("公式-集合")
    private List<ActFormulaCaluVo> actFormulaCaluVos;

    @ApiModelProperty("有效公式与表达式-条件成立的公式与表达式")
    private ValidActFormulaExpressCaluVo validActFormulaExpressCaluVo;

    @ApiModelProperty("错误信息")
    private String errMsg;

    @ApiModelProperty(value = "指标项对应的数值")
    private List<ActIndexCaluDto> indexCaluDtos;
}

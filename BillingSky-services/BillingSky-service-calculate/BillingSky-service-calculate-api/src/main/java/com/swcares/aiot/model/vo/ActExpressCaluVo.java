package com.swcares.aiot.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class ActExpressCaluVo {
    @ApiModelProperty("表达式-条件表达式")
    private String criteria;

    @ApiModelProperty("表达式-条件表达式-比较结果")
    private Boolean criteriaResult;

    @ApiModelProperty("表达式-计价数量")
    private String valuationQuantity;

    @ApiModelProperty("表达式-计价数量-结果")
    private BigDecimal valuationQuantityResult;

    @ApiModelProperty("表达式-计价单价")
    private String valuationUnitPrice;

    @ApiModelProperty("表达式-计价单价-结果")
    private BigDecimal valuationUnitPriceResult;
}

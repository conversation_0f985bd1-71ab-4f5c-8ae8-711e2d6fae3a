package com.swcares.aiot.client;

import com.swcares.aiot.fallback.ThresholdBizFallback;
import com.swcares.aiot.model.dto.ThrThresholdRetrDto;
import com.swcares.aiot.model.vo.ThrThresholdRetrVo;
import com.swcares.baseframe.common.feign.FeignClientInterceptor;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient(value = "calculate",
        contextId = "iThresholdBizRemoteClient",
        path = "/calculate/thresholdBiz",
//        url = "${swcares.calculate.feign.url:' '}",
        fallbackFactory = ThresholdBizFallback.class,
        configuration = FeignClientInterceptor.class)
public interface IThresholdBizClient {
    /**
     * 查询-获取某个客户的阈值设置
     *
     * @param thrThresholdRetrDto 阈值设置
     * @return 阈值设置
     */
    @ApiOperation(value = "查询-获取某个客户的阈值设置")
    @PostMapping("/retrieveThreshold")
    Map<String, String> retrieveThreshold(@RequestBody ThrThresholdRetrDto thrThresholdRetrDto);
    /**
     * 查询-获取某个客户的阈值设置
     *
     * @return 阈值设置
     */
    @ApiOperation(value = "查询-获取所有客户的阈值设置")
    @PostMapping("/retrieveThreshold2")
    List<ThrThresholdRetrVo> retrieveThreshold2();
}

package com.swcares.aiot.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * date 2025年04月17日21:29
 */
@ApiModel("费用项-公式-获取")
@Data
public class ExpensesWithFormulaGetVo {

    @ApiModelProperty("客户-甲方-编码")
    private String customerIataA;

    @ApiModelProperty("客户-甲方-名称")
    private String customerNameA;

    @ApiModelProperty("费用项-集合")
    private List<ActCustomer2Vo> actExpensesVos;
}

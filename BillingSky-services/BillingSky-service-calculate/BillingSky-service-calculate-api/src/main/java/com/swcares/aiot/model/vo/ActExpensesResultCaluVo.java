package com.swcares.aiot.model.vo;

import com.swcares.aiot.model.dto.ActCustomerCaluDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ActExpensesResultCaluVo {
    @ApiModelProperty(value = "业务id-航班id")
    private String flightId;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty("要重新计算的目标")
    private List<ActCustomerCaluDto> actCustomerCaluDtos;
}

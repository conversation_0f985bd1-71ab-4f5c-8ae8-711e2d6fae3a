spring:
  application:
    name: endpoint
  # 允许做bean的覆盖定义
  main:
    allow-bean-definition-overriding: true
  cloud:
    ########################################## Spring nacos 配置  ###################################
    nacos:
      server-addr: ${NACOS_HOST}:${NACOS_PORT}
      discovery:
        namespace: ${NACOS_NAMESPACE}
        group: ${NACOS_GROUP}
        register-enabled: true
        username: ${NACOS_USERNAME}
        password: ${NACOS_PASSWORD}

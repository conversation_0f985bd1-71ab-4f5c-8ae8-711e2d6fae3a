package com.swcares.aiot.core.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EndpointpageDto extends PageDto {
    @ApiModelProperty(value = "接口名称", example = "计算中心-客户管理-分页获取客户数据")
    private String name;

    @ApiModelProperty(value = "接口地址", example = "/calculate/customerBiz/pageCustomer")
    private String uri;
}

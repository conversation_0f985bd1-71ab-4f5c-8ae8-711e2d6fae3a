package com.swcares.aiot;

import cn.hutool.setting.Setting;
import com.worm.MybatisPlusGenProperties;
import com.worm.MybatisPlusGenUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class GenDbCode {
    private static final Setting SETTING_DATASOURCE = new Setting("datasource.setting");
    public static void main(String[] args) {
        SETTING_DATASOURCE.autoLoad(true);
        MybatisPlusGenProperties mybatisPlusGenProperties = new MybatisPlusGenProperties();
        mybatisPlusGenProperties.setEnable(true);
        //
        mybatisPlusGenProperties.setOutputPath("./BillingSky-services/BillingSky-service-endpoint/BillingSky-service-endpoint-core");
        // 数据库名
        mybatisPlusGenProperties.setDbName("bls_endpoint");
        // 主机ip
        mybatisPlusGenProperties.setHost(SETTING_DATASOURCE.getStr("host"));
        // 端口
        mybatisPlusGenProperties.setPort(SETTING_DATASOURCE.getInt("port"));
        //
        mybatisPlusGenProperties.setUname("root");
        //
        mybatisPlusGenProperties.setPwd("Aiotsw@test");
        //
        mybatisPlusGenProperties.setIncludeTableNames(Arrays.asList("tripartite", "accredit", "endpoint"));
        MybatisPlusGenUtils.rebuild(mybatisPlusGenProperties);
    }
}

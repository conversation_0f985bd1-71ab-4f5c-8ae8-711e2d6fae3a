package com.swcares.aiot.core.service.impl;

import com.swcares.aiot.core.entity.Tripartite;
import com.swcares.aiot.core.mapper.TripartiteMapper;
import com.swcares.aiot.core.service.ITripartiteService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 三方系统管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Service
public class TripartiteServiceImpl extends ServiceImpl<TripartiteMapper, Tripartite> implements ITripartiteService {

}

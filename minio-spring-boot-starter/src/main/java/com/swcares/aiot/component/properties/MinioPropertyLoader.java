package com.swcares.aiot.component.properties;


import com.alibaba.fastjson.parser.ParserConfig;
import com.swcares.baseframe.common.prop.PropertiesUtils;
import com.swcares.baseframe.utils.io.FileUtils;
import com.swcares.baseframe.utils.lang.StringUtils;
import org.springframework.boot.env.OriginTrackedMapPropertySource;
import org.springframework.boot.env.PropertiesPropertySourceLoader;
import org.springframework.boot.env.PropertySourceLoader;
import org.springframework.boot.env.YamlPropertySourceLoader;
import org.springframework.core.Ordered;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * ClassName：com.swcares.baseframe.common.io.MinioPropertyLoader <br>
 * Description：配置文件加载（Boot） <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年8月6日 下午5:07:23 <br>
 * @version v1.0 <br>
 */
public class MinioPropertyLoader implements PropertySourceLoader, Ordered {

    private static boolean isLoadPropertySource = false;
    private final PropertiesPropertySourceLoader propertiesPropertySourceLoader = new PropertiesPropertySourceLoader();
    private final YamlPropertySourceLoader yamlPropertySourceLoader = new YamlPropertySourceLoader();

    @Override
    public String[] getFileExtensions() {
        return new String[]{"properties", "yml", "yaml"};
    }

    @Override
    public List<PropertySource<?>> load(String name, Resource resource) throws IOException {
        List<PropertySource<?>> propertySources = new ArrayList<>();
        if (!isLoadPropertySource) {
            isLoadPropertySource = true;
            // 开启 FastJSON 安全模式
            ParserConfig.getGlobalInstance().setSafeMode(true);
            Properties properties = PropertiesUtils.getInstance().getProperties();
            propertySources.add(new OriginTrackedMapPropertySource("swcares", properties));
        } else {
            String ext = FileUtils.getFileExtension(resource.getFilename());
            if (StringUtils.inString(ext, propertiesPropertySourceLoader.getFileExtensions())) {
                propertySources.addAll(propertiesPropertySourceLoader.load(name, resource));
            } else if (StringUtils.inString(ext, yamlPropertySourceLoader.getFileExtensions())) {
                propertySources.addAll(yamlPropertySourceLoader.load(name, resource));
            }
        }
        return propertySources;
    }

    @Override
    public int getOrder() {
        return Integer.MIN_VALUE + 1
                ;
    }

}
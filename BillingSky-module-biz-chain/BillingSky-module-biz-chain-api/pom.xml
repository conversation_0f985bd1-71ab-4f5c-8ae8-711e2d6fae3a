<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.swcares.aiot</groupId>
        <artifactId>BillingSky-module-biz-chain</artifactId>
        <version>2.30.0-SNAPSHOT</version>
    </parent>
    <artifactId>BillingSky-module-biz-chain-api</artifactId>
    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.baseframe</groupId>
            <artifactId>common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.worm</groupId>
            <artifactId>worm-toolkit-hutool</artifactId>
        </dependency>
    </dependencies>
</project>
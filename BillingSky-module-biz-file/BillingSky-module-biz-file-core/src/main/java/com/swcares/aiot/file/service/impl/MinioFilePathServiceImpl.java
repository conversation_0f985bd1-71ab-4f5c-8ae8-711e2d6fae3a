package com.swcares.aiot.file.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aiot.file.entity.MinioFilePath;
import com.swcares.aiot.file.mapper.MinioFilePathMapper;
import com.swcares.aiot.file.service.MinioFilePathService;
import org.springframework.stereotype.Service;

/**
 * ClassName：com.swcares.aiot.file.service.impl.MinioFilePathServiceImpl <br>
 * Description：租户MINIO文件夹配置表 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-09-02 <br>
 * @version v1.0 <br>
 */
@Service
public class MinioFilePathServiceImpl extends ServiceImpl<MinioFilePathMapper, MinioFilePath> implements MinioFilePathService {

}

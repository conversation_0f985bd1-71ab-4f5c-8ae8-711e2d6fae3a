package com.swcares.aiot.core.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName：com.swcares.aiot.core.model.dto.MinioConfigDTO <br>
 * Description：租户Minio配置表 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-30 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="MinioConfigDTO", description="租户Minio配置")
public class MinioConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "权限;WRITE:写权限,READ:读权限")
    private String auth;

    @ApiModelProperty(value = "ACCESS KEY")
    private String accessKey;

    @ApiModelProperty(value = "SECRET KEY")
    private String secretKey;

    @ApiModelProperty(value = "私有BUCKET")
    private String privateBucket;

    @ApiModelProperty(value = "状态;ENABLE:可用,DISABLE不可用")
    private String status;

    @ApiModelProperty(value = "是否删除;1是，0否")
    private Integer deleted;

}

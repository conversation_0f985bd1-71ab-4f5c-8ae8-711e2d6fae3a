package com.swcares.aiot.core.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * ClassName：com.swcares.aiot.core.model.dto.UploadFileDTO <br>
 * Description：上传文件DTO <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024/9/2 21:30 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "UploadFileDTO", description = "上传文件流请求参数")
public class UploadFileDTO {

    @ApiModelProperty(value = "租户id，必传")
    private Long tenantId = 0L;

    @ApiModelProperty(value = "文件编号，非必传，如果没传，默认UUID")
    private String fileCode;

    @ApiModelProperty(value = "Minio文件path，和busiType二选一，filePath优先")
    private String filePath;

    @ApiModelProperty(value = "业务类型，和filePath二选一，filePath优先")
    private String busiType;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "登录用户名，非必传")
    private String userName;

    @JsonIgnore
    @ApiModelProperty(value = "文件，必传")
    private MultipartFile file;
}

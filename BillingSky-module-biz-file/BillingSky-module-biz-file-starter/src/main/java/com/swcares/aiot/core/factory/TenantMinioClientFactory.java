package com.swcares.aiot.core.factory;

import com.swcares.aiot.core.cons.MinioCons;
import com.swcares.aiot.core.cons.MinioErrorCodeCons;
import com.swcares.aiot.core.model.vo.MinioConfigVO;
import com.swcares.aiot.core.config.MinIOConfiguration;
import com.swcares.aiot.service.IMinioConfigBizService;
import com.swcares.baseframe.common.exception.BusinessException;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ClassName：com.swcares.aiot.core.factory.TenantMinioClientFactory <br>
 * Description：租户MinioClient工厂类 <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2024/8/30 15:11 <br>
 * @version v1.0 <br>
 */

@Slf4j
@Component
public class TenantMinioClientFactory {
    @Resource
    private IMinioConfigBizService iMinioConfigBizService;
    @Resource
    private MinIOConfiguration minIOConfiguration;

    /**
     * minio配置map
     */
    private static final Map<String, MinioClient> TENANT_MINIO_CLIENT_MAP = new ConcurrentHashMap<>();

    private TenantMinioClientFactory() {

    }

    /**
     * Description 初始化租户minio <br>
     *
     * author dengbanglin <br>
     * Date 2024/9/2 09:14
     */
    @PostConstruct
    public void initMinioClient() {
        // 获取可用的minio配置
        List<MinioConfigVO> configList = iMinioConfigBizService.getTenantMinioConfigList();
        if (CollectionUtils.isNotEmpty(configList)) {
            for (MinioConfigVO config : configList) {
                createMinioClient(config);
            }
        }
    }

    /**
     * Description 创建minio客户端 <br>
     *
     * @param config 租户minio配置
     * @return MinioClient
     * <AUTHOR> <br>
     * Date 2024/9/2 09:28
     */
    private MinioClient createMinioClient(MinioConfigVO config) {
        try {
            MinioClient minioClient = new MinioClient(minIOConfiguration.getUrl(), config.getAccessKey(), config.getSecretKey());
            addMinioClient(config.getAuth(), config.getTenantId(), minioClient);

            return minioClient;
        } catch (Exception e) {
            log.error("创建Minio连接失败, tenantId = {}, accessKey = {}", config.getTenantId(), config.getAccessKey(), e);
        }

        return null;
    }

    /**
     * Description 更新配置 <br>
     *
     * @param auth        权限
     * @param tenantId    租户id
     * @param minioClient 配置
     * author dengbanglin <br>
     * Date 2024/8/30 15:56
     */
    public static void addMinioClient(String auth, Long tenantId, MinioClient minioClient) {
        TENANT_MINIO_CLIENT_MAP.put(auth + tenantId, minioClient);
    }

    /**
     * Description 删除配置 <br>
     *
     * @param auth     权限
     * @param tenantId 租户id
     * author dengbanglin <br>
     * Date 2024/9/4 14:03
     */
    public static void deleteMinioClient(String auth, Long tenantId) {
        TENANT_MINIO_CLIENT_MAP.remove(auth + tenantId);
    }

    /**
     * Description 获取MinioClient <br>
     *
     * @param tenantId 租户id
     * @param auth     权限
     * @return MinioClient
     * author dengbanglin <br>
     * Date 2024/9/2 20:11
     */
    public MinioClient getMinioClient(Long tenantId, String auth) {
        // 当前租户id
        MinioClient minioClient = TENANT_MINIO_CLIENT_MAP.get(auth + tenantId);
        if (minioClient == null) {
            MinioConfigVO config = iMinioConfigBizService.getTenantMinioConfig(tenantId, auth);
            if (config != null) {
                minioClient = createMinioClient(config);
                if (minioClient != null) {
                    return minioClient;
                }
            }

            // 租户未配置minio
            log.error("租户【{}】未配置写权限的Minio", tenantId);
            throw new BusinessException(MinioErrorCodeCons.NOT_CONFIG_MINIO);
        }

        return minioClient;
    }

    /**
     * Description 获取写权限MinioClient <br>
     *
     * @param tenantId 租户id
     * @return MinioClient
     * author dengbanglin <br>
     * Date 2024/8/30 15:58
     */
    public MinioClient getWriteMinioClient(Long tenantId) {
        return getMinioClient(tenantId, MinioCons.MINIO_AUTH_WRITE);
    }

    /**
     * Description 获取写权限MinioClient <br>
     *
     * @param tenantId 租户id
     * @return MinioClient
     * author dengbanglin <br>
     * Date 2024/8/30 15:58
     */
    public MinioClient getReadMinioClient(Long tenantId) {
        return getMinioClient(tenantId, MinioCons.MINIO_AUTH_READ);
    }

}

package com.swcares.aiot.core.cons;

/**
 * ClassName：com.swcares.aiot.core.constant.MinioCons <br>
 * Description：常量 <br>
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024/8/30 14:43 <br>
 * @version v1.0 <br>
 */
public class MinioCons {

    private MinioCons() {
    }

    /**
     * 租户minio配置配置状态—可用
     */
    public static final String ENABLE = "ENABLE";

    /**
     * 租户minio配置缓存key
     */
    public static final String CACHE_KEY_MINIO_CONFIG = "cache_data:minio_config:";

    /**
     * 租户业务文件夹缓存key
     */
    public static final String CACHE_KEY_MINIO_BUSI_FILE_PATH = "cache_data:minio_busi_file_path:";

    /**
     * minio权限—写
     */
    public static final String MINIO_AUTH_WRITE = "WRITE";

    /**
     * minio权限—读
     */
    public static final String MINIO_AUTH_READ = "READ";

    /**
     * 删除标识-未删除
     */
    public static final Integer NOT_DELETE = 0;

    /**
     * 默认文件夹
     */
    public static final String BASE_FILE_PATH = "/base";

    /**
     * 最大批量下载文件数
     */
    public static final int DOWNLOAD_MAX_FILE_NUMBER = 1000;


    /**
     * 附件状态-已删除
     */
    public static final Integer FILE_STATUS_DELETED = 0;


    /**
     * 附件状态-正常
     */
    public static final Integer FILE_STATUS_ENABLE = 1;


    /**
     * 附件状态-上传失败
     */
    public static final Integer FILE_STATUS_UPLOAD_FAIL = 2;

}

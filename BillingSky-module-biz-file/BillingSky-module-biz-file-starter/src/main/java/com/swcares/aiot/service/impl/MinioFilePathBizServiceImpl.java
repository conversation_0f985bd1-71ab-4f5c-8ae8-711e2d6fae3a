package com.swcares.aiot.service.impl;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.Cached;
import com.swcares.aiot.core.cons.MinioCons;
import com.swcares.aiot.file.entity.MinioFilePath;
import com.swcares.aiot.file.service.MinioFilePathService;
import com.swcares.aiot.service.IMinioFilePathBizService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.aiot.service.impl.MinioFilePathBizServiceImpl <br>
 * Description：租户MINIO文件夹配置表 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-09-02 <br>
 * @version v1.0 <br>
 */
@Service
public class MinioFilePathBizServiceImpl implements IMinioFilePathBizService {
    @Resource
    private MinioFilePathService minioFilePathService;

    @Override
    @Cached(name = MinioCons.CACHE_KEY_MINIO_BUSI_FILE_PATH, key ="#tenantId + #busiType", expire = 3600)
    public String getBusiFilePath(Long tenantId, String busiType) {
        List<MinioFilePath> pathList = minioFilePathService.lambdaQuery()
                .eq(MinioFilePath::getTenantId, tenantId)
                .eq(MinioFilePath::getBusiType, busiType)
                .eq(MinioFilePath::getStatus, MinioCons.ENABLE)
                .eq(MinioFilePath::getDeleted, MinioCons.NOT_DELETE)
                .list();
        if (CollectionUtils.isNotEmpty(pathList)) {
            return pathList.get(0).getFilePath();
        }

        return null;
    }

    @Override
    @CacheInvalidate(name = MinioCons.CACHE_KEY_MINIO_BUSI_FILE_PATH, key = "#tenantId + #busiType")
    public void delBusiFilePathCache(Long tenantId, String busiType) {
        // 空方法，删除缓存
    }
}

package com.swcares.aiot.statemachine.biz.dapter;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.swcares.aiot.statemachine.biz.events.EnumRevocationStatusChangeEvent;
import com.swcares.aiot.statemachine.biz.status.EnumRevocationStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.config.EnableStateMachine;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;

import javax.annotation.Resource;
import java.util.EnumSet;

/**
 * 定义状态机规则和配置状态机
 *
 * <AUTHOR>
 */
@Slf4j
@EnableStateMachine(name = RevocationStatusStateMachineAdapter.MACHINE_ID)
public class RevocationStatusStateMachineAdapter extends EnumStateMachineConfigurerAdapter<EnumRevocationStatus, EnumRevocationStatusChangeEvent> {

    public static final String MACHINE_ID = "revocationStatusStateMachine";

    @Resource
    private StateMachineListenerAdapter<EnumRevocationStatus, EnumRevocationStatusChangeEvent> stateMachineListenerAdapter;


    @Getter
    @Setter
    private EnumRevocationStatus initEnumBillStatus;

    @Override
    public void configure(StateMachineConfigurationConfigurer<EnumRevocationStatus, EnumRevocationStatusChangeEvent> config) throws Exception {
        log.debug("定义状态机对象：【{}】", RevocationStatusStateMachineAdapter.MACHINE_ID);
        config
                .withConfiguration()
                .autoStartup(true)
                .machineId(MACHINE_ID)
                .listener(stateMachineListenerAdapter);
    }

    @Override
    public void configure(StateMachineStateConfigurer<EnumRevocationStatus, EnumRevocationStatusChangeEvent> states) throws Exception {
        log.debug("{}===状态机初始化状态：{}", LocalDateTimeUtil.now(), EnumRevocationStatus.HOLLOW_REVOCATION);
        states
                .withStates()
                // 定义初始状态
                .initial(EnumRevocationStatus.HOLLOW_REVOCATION)
                // 所有可能的状态
                .states(EnumSet.allOf(EnumRevocationStatus.class));
    }

    @Override
    public void configure(StateMachineTransitionConfigurer<EnumRevocationStatus, EnumRevocationStatusChangeEvent> transitions) throws Exception {
        log.debug("{}===状态机定义事件与状态关系", LocalDateTimeUtil.now());
        transitions
                // 机场申请撤销事件： -  --》 申请撤销
                .withExternal().source(EnumRevocationStatus.HOLLOW_REVOCATION).target(EnumRevocationStatus.APPLY_REVOCATION).event(EnumRevocationStatusChangeEvent.AIRPORT_APPLY_REVOCATION)
                // 机场申请撤销事件： 拒绝撤销  --》 申请撤销
                .and()
                .withExternal().source(EnumRevocationStatus.REJECT_REVOCATION).target(EnumRevocationStatus.APPLY_REVOCATION).event(EnumRevocationStatusChangeEvent.AIRPORT_APPLY_REVOCATION)
                // 航司拒绝撤销事件： 申请撤销  --》 拒绝撤销
                .and()
                .withExternal().source(EnumRevocationStatus.APPLY_REVOCATION).target(EnumRevocationStatus.REJECT_REVOCATION).event(EnumRevocationStatusChangeEvent.AIRLINE_REJECT_REVOCATION)
                // 航司同意撤销事件： 申请撤销 --》 同意撤销
                .and()
                .withExternal().source(EnumRevocationStatus.APPLY_REVOCATION).target(EnumRevocationStatus.AGREE_REVOCATION).event(EnumRevocationStatusChangeEvent.AIRLINE_AGREE_REVOCATION)
                // 机场提交对账事件： 同意撤销 --》 -
                .and()
                .withExternal().source(EnumRevocationStatus.AGREE_REVOCATION).target(EnumRevocationStatus.HOLLOW_REVOCATION).event(EnumRevocationStatusChangeEvent.AIRPORT_SUBMIT_RECONCILIATION);
    }
}

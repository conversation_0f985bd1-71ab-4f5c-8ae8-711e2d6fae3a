package com.swcares.aiot.statemachine.biz.listener;

import com.swcares.aiot.statemachine.biz.BizStatemachineTemplate;
import com.swcares.aiot.statemachine.biz.dapter.BillStatusStateMachineAdapter;
import com.swcares.aiot.statemachine.biz.events.EnumBillStatusChangeEvent;
import com.swcares.aiot.statemachine.biz.status.EnumBillStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.statemachine.annotation.OnTransition;
import org.springframework.statemachine.annotation.WithStateMachine;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@WithStateMachine(id = BillStatusStateMachineAdapter.MACHINE_ID)
public class BillStatusListener<T> {

    @Resource
    private BizStatemachineTemplate<EnumBillStatus, EnumBillStatusChangeEvent, T> bizStatemachineTemplate;

    /**
     * 航司自动对账成功事件： 待审核 -->> 已确认
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "AWAIT_EXAMINE", target = "CONFIRMED")
    public void transitionAwaitExamine2Confirmed(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.CONFIRMED.getStatus());
    }

    /**
     * 航司自动对账失败事件： 待审核  --》 有差异
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "AWAIT_EXAMINE", target = "DISCREPANCY")
    public void transitionAwaitExamine2Discrepancy(Message<EnumBillStatusChangeEvent> message) {
        // 传输的数据
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.DISCREPANCY.getStatus());
    }

    /**
     * 航司有争议事件： 待审核 --》有争议
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "AWAIT_EXAMINE", target = "CONTROVERSIAL")
    public void transitionAwaitExamine2Controversial(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.CONTROVERSIAL.getStatus());
    }

    /**
     * 航司有争议事件： 有差异 --》有争议
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "DISCREPANCY", target = "CONTROVERSIAL")
    public void transitionDiscrepancy2Controversial(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.CONTROVERSIAL.getStatus());
    }

    /**
     * 机场拒绝处理事件： 有争议 --》 拒绝处理
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "CONTROVERSIAL", target = "REFUSE_PROCESS")
    public void transitionControversial2RefuseProcess(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.REFUSE_PROCESS.getStatus());
    }

    /**
     * 航司同意撤销事件： 已确认 -->> 已撤销
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "CONFIRMED", target = "REVOKED")
    public void transitionConfirmed2Revoked(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.REVOKED.getStatus());
    }

    /**
     * 航司同意撤销事件: 待审核 -->> 已撤销
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "AWAIT_EXAMINE", target = "REVOKED")
    public void transitionAwaitExamine2Revoked(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.REVOKED.getStatus());
    }

    /**
     * 机场提交对账事件： 有争议-->>待审核
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "CONTROVERSIAL", target = "AWAIT_EXAMINE")
    public void transitionControversial2AwaitExamine(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.AWAIT_EXAMINE.getStatus());
    }

    /**
     * 机场提交对账事件： 已撤销-->>待审核
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "REVOKED", target = "AWAIT_EXAMINE")
    public void transitionRevoked2AwaitExamine(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.AWAIT_EXAMINE.getStatus());
    }

    /**
     * 机场提交对账事件：  未提交 -->> 待审核
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "NOT_SUBMITTED", target = "AWAIT_EXAMINE")
    public void transitionNotSubmitted2AwaitExamine(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.AWAIT_EXAMINE.getStatus());
    }

    /**
     * 机场开账事件： 有争议 -->> 未提交
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "CONTROVERSIAL", target = "NOT_SUBMITTED")
    public void transitionControversial2NotSubmitted(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.NOT_SUBMITTED.getStatus());
    }

    /**
     * 机场开账（再次开账）事件： 拒绝处理 -->> 未提交
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "REFUSE_PROCESS", target = "NOT_SUBMITTED")
    public void transitionRefuseProcess2NotSubmitted(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.NOT_SUBMITTED.getStatus());
    }

    /**
     * 机场开账（再次开账）事件： 拒绝处理 -->> 有争议
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "REFUSE_PROCESS", target = "CONTROVERSIAL")
    public void transitionRefuseProcess2Controversial(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.CONTROVERSIAL.getStatus());
    }

    /**
     * 机场开账（再次开账）事件： 拒绝处理 -->> 已确认
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "REFUSE_PROCESS", target = "CONFIRMED")
    public void transitionRefuseProcess2Confirmed(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.CONFIRMED.getStatus());
    }


    /**
     * 机场开账事件： 已撤销 -->> 未提交
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "REVOKED", target = "NOT_SUBMITTED")
    public void transitionRevoked2NotSubmitted(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.NOT_SUBMITTED.getStatus());
    }

    /**
     * 机场开账事件： 有争议 -->> 已确认
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "CONTROVERSIAL", target = "CONFIRMED")
    public void transitionControversial2Confirmed(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.CONFIRMED.getStatus());
    }

    /**
     * 机场开账事件： 已确认 -->> 有争议
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "CONFIRMED", target = "CONTROVERSIAL")
    public void transitionConfirmed2Controversial(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.CONTROVERSIAL.getStatus());
    }

    /**
     * 航司已确认事件： 有差异 -->> 已确认
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "DISCREPANCY", target = "CONFIRMED")
    public void transitionDiscrepancy2Confirmed(Message<EnumBillStatusChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumBillStatus.CONFIRMED.getStatus());
    }
}

package com.swcares.aiot.statemachine.listener;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;
import org.springframework.statemachine.state.State;
import org.springframework.statemachine.transition.Transition;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ChangeStateMachineListenerAdapter<S, E> extends StateMachineListenerAdapter<S, E> {
    @Override
    public void stateChanged(State<S, E> from, State<S, E> to) {
        if (ObjectUtil.isNotNull(from)) {
            log.debug("State change from [{}] to [{}] ({}-->>{})", from.getId(), to.getId(), from.getId(), to.getId());
        }
    }

    @Override
    public void stateContext(StateContext<S, E> stateContext) {
        log.debug("状态机上下文:{}", stateContext);
    }

    @Override
    public void transitionStarted(Transition<S, E> transition) {
        State<S, E> source = transition.getSource();
        if (ObjectUtil.isNotNull(transition.getSource())) {
            log.debug("开始状态:{}", source.getId());
        } else {
            log.debug("源头为空");
        }
    }

    @Override
    public void transitionEnded(Transition<S, E> transition) {
        log.debug("目标状态:{}", transition.getTarget().getId());
    }
}

package com.swcares.aiot.statemachine.persist;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.statemachine.StateMachineContext;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.data.redis.RedisStateMachineContextRepository;
import org.springframework.statemachine.data.redis.RedisStateMachinePersister;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.RepositoryStateMachinePersist;
import org.springframework.statemachine.persist.StateMachinePersister;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class StatusMachinePersisterConfig {
    @Resource
    private RedisConnectionFactory redisConnectionFactory;

    /**
     * 持久化到内存map中
     */
    @Bean
    @Primary
    public <S extends Enum<S>, E extends Enum<S>, T> StateMachinePersister<S, E, T> defaultStateMachinePersister() {
        return new DefaultStateMachinePersister<>(new StateMachinePersist<S, E, T>() {
            private final Map<T, StateMachineContext<S, E>> map = new HashMap<>();

            @Override
            public void write(StateMachineContext<S, E> context, T contextObj) {
                log.info("持久化状态机,context:{},contextObj:{}", JSONUtil.toJsonStr(context), JSONUtil.toJsonStr(contextObj));
                map.put(contextObj, context);
            }

            @Override
            public StateMachineContext<S, E> read(T contextObj) {
                log.info("获取状态机,contextObj:{}", JSONUtil.toJsonStr(contextObj));
                StateMachineContext<S, E> stateMachineContext = map.get(contextObj);
                log.info("获取状态机结果,stateMachineContext:{}", JSONUtil.toJsonStr(stateMachineContext));
                return stateMachineContext;
            }
        });
    }

    /**
     * 持久化到redis中，在分布式系统中使用
     */
    @Bean
    public <E, S> RedisStateMachinePersister<E, S> redisStateMachinePersister() {
        RedisStateMachineContextRepository<E, S> repository = new RedisStateMachineContextRepository<>(redisConnectionFactory);
        RepositoryStateMachinePersist<E, S> repositoryStateMachinePersist = new RepositoryStateMachinePersist<>(repository);
        return new RedisStateMachinePersister<>(repositoryStateMachinePersist);
    }
}

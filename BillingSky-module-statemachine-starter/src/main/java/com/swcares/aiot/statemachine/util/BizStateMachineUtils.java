package com.swcares.aiot.statemachine.util;

import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.statemachine.dto.EventDto;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.state.State;
import org.springframework.statemachine.support.AbstractStateMachine;
import org.springframework.statemachine.transition.Transition;

import java.lang.reflect.Method;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@UtilityClass
@Slf4j
public class BizStateMachineUtils {
    /**
     * 获取key
     *
     * @param stateMachine 状态机
     * @param eventDto     内部对象
     * @param <S>          状态
     * @param <E>          事件
     * @param <T>          数据类型
     * @return key
     */
    public static <S extends Enum<S>, E extends Enum<E>, T> String getFormatKey(StateMachine<S, E> stateMachine, EventDto<S, E, T> eventDto) {
        String key = CharSequenceUtil.format("{}_{}", stateMachine.getUuid(), eventDto.getFilterValue());
        log.info("状态机的状态保存的key:{}", key);
        return key;
    }


    /**
     * 设置状态机的当前状态
     * 此方法没有验证
     *
     * @param stateMachine 状态机
     * @param state        状态
     * @param <S>          状态
     * @param <E>          事件
     */
    public static <S, E> void setCurrentState(StateMachine<S, E> stateMachine, S state) {
        if (stateMachine instanceof AbstractStateMachine) {
            Object voidMono = setCurrentState((AbstractStateMachine<S, E>) stateMachine, state);
            log.info("方法执行结果:{}", voidMono);
            log.info("StateMachine Current:{}", stateMachine);
        } else {
            throw new IllegalArgumentException("Provided StateMachine is not a valid type");
        }
    }

    /**
     * 将状态机修改为指定状态
     *
     * @param stateMachine 状态机
     * @param state        指定状态
     * @param <S>          状态
     * @param <E>          事件
     * @return 修改状态后的状态机
     */
    private static <S, E> Object setCurrentState(AbstractStateMachine<S, E> stateMachine, S state) {
        try {
            Method setCurrentState = AbstractStateMachine.class.getDeclaredMethod("setCurrentState", State.class, Message.class, Transition.class, boolean.class, StateMachine.class);
            // 暴力破解
            setCurrentState.setAccessible(true);
            // 方法调用
            return setCurrentState.invoke(stateMachine, findState(stateMachine, state), null, null, false, stateMachine);
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 查找对应的状态机
     *
     * @param stateMachine 状态机的状态
     * @param stateId      状态机的id
     * @param <S>          状态
     * @param <E>          事件
     * @return 满足条件的状态机
     * @throws Throwable 异常
     */
    private static <S, E> State<S, E> findState(AbstractStateMachine<S, E> stateMachine, S stateId) throws Throwable {
        return stateMachine.getStates()
                .stream()
                .filter(state -> state.getId() == stateId)
                .findFirst()
                .orElseThrow((Supplier<Throwable>) () -> new IllegalArgumentException("Specified State ID is not valid"));
    }
}

package com.swcares.aiot.statemachine.biz.status;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum EnumBillStatus {
    NOT_SUBMITTED(0, "未提交"),
    
    CONFIRMED(1, "已确认"),

    CONTROVERSIAL(2, "有争议"),

    AWAIT_EXAMINE(3, "待审核"),

    REFUSE_PROCESS(4, "拒绝处理"),

    REVOKED(5, "已撤销"),

    DISCREPANCY(6, "有差异");


    private final Integer status;
    private final String desc;

    public static EnumBillStatus of(Integer dataStatus) {
        return Arrays.stream(EnumBillStatus.values()).filter(enumDataPushStatus -> Objects.equals(enumDataPushStatus.getStatus(), dataStatus)).findAny().orElse(null);
    }
}

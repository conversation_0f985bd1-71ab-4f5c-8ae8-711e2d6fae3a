package com.swcares.aiot.statemachine.biz.dapter;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.aiot.statemachine.biz.events.EnumAirportSyncStatusChangeEvent;
import com.swcares.aiot.statemachine.biz.status.EnumAirportSynStatus;
import com.swcares.aiot.statemachine.biz.status.EnumDataPushStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.config.EnableStateMachine;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;

import javax.annotation.Resource;
import java.util.EnumSet;

/**
 * <AUTHOR>
 */
@Slf4j
@EnableStateMachine(name = AirportSyncStateMachineAdapter.MACHINE_ID)
public class AirportSyncStateMachineAdapter extends EnumStateMachineConfigurerAdapter<EnumAirportSynStatus, EnumAirportSyncStatusChangeEvent> {
    public static final String MACHINE_ID = "airportSyncStateMachine";

    @Resource
    private StateMachineListenerAdapter<EnumAirportSynStatus, EnumAirportSyncStatusChangeEvent> stateMachineListenerAdapter;

    @Override
    public void configure(StateMachineConfigurationConfigurer<EnumAirportSynStatus, EnumAirportSyncStatusChangeEvent> config) throws Exception {
        log.info(CharSequenceUtil.format("定义状态机对象：【{}】", AirportSyncStateMachineAdapter.MACHINE_ID));
        config
                .withConfiguration()
                .autoStartup(true)
                .listener(stateMachineListenerAdapter)
                .machineId(MACHINE_ID);
    }

    @Override
    public void configure(StateMachineStateConfigurer<EnumAirportSynStatus, EnumAirportSyncStatusChangeEvent> states) throws Exception {
        log.info(CharSequenceUtil.format("{}===状态机初始化：{}", LocalDateTimeUtil.now(), EnumDataPushStatus.PUSH_WAITING));
        // 定义初始状态，以及可能的所有状态
        states
                .withStates()
                .initial(EnumAirportSynStatus.AIRLINE_HANDLING)
                .states(EnumSet.allOf(EnumAirportSynStatus.class));
    }

    @Override
    public void configure(StateMachineTransitionConfigurer<EnumAirportSynStatus, EnumAirportSyncStatusChangeEvent> transitions) throws Exception {
        log.info(CharSequenceUtil.format("{}===状态机定义事件与状态关系", LocalDateTimeUtil.now()));
        // 配置状态转换事件关系
        transitions
                // 航司发送机场事件： 航司处理中  --》 已反馈机场
                .withExternal().source(EnumAirportSynStatus.AIRLINE_HANDLING).target(EnumAirportSynStatus.FEEDBACK_AIRPORT).event(EnumAirportSyncStatusChangeEvent.AIRLINE_SEND_AIRPORT)
                // 机场申请撤回事件： 航司处理中 --》机场申请撤回
                .and().withExternal().source(EnumAirportSynStatus.AIRLINE_HANDLING).target(EnumAirportSynStatus.AIRPORT_APPLY_REVOCATION).event(EnumAirportSyncStatusChangeEvent.AIRPORT_APPLY_REVOCATION)
                // 航司审核-机场申请撤回-同意事件： 机场申请撤回 --》已撤回机场
                .and().withExternal().source(EnumAirportSynStatus.AIRPORT_APPLY_REVOCATION).target(EnumAirportSynStatus.REVOCATION_AIRPORT).event(EnumAirportSyncStatusChangeEvent.AIRLINE_AUDIT_AGREE)
                // 航司审核-机场申请撤回-不同意事件： 机场申请撤回 --》航司处理中
                .and().withExternal().source(EnumAirportSynStatus.AIRPORT_APPLY_REVOCATION).target(EnumAirportSynStatus.AIRLINE_HANDLING).event(EnumAirportSyncStatusChangeEvent.AIRLINE_AUDIT_REFUSE);

    }
}

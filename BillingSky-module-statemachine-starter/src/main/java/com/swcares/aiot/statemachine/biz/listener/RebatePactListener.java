package com.swcares.aiot.statemachine.biz.listener;

import com.swcares.aiot.statemachine.biz.BizStatemachineTemplate;
import com.swcares.aiot.statemachine.biz.dapter.RebatePactStateMachineAdapter;
import com.swcares.aiot.statemachine.biz.events.EnumRebatePactChangeEvent;
import com.swcares.aiot.statemachine.biz.status.EnumRebatePactStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.statemachine.annotation.OnTransition;
import org.springframework.statemachine.annotation.WithStateMachine;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * date 2025年04月14日20:18
 * 账单履约变更事件监听器
 */
@Slf4j
@WithStateMachine(id = RebatePactStateMachineAdapter.MACHINE_ID)
public class RebatePactListener<T> {

    @Resource
    private BizStatemachineTemplate<EnumRebatePactStatus, EnumRebatePactChangeEvent, T> bizStatemachineTemplate;

    /**
     * 账单发起履约          账单未发起履约  --》 账单未确认
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "REBATE_PACT_UN_INITIATED", target = "REBATE_PACT_UN_CONFIRM")
    public void transitionRpui2Rpuc(Message<EnumRebatePactChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumRebatePactStatus.REBATE_PACT_UN_CONFIRM.getStatus());
    }

    /**
     * 账单确认履约          账单未确认  --》 账单已确认
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "REBATE_PACT_UN_CONFIRM", target = "REBATE_PACT_CONFIRMED")
    public void transitionRpuc2Rpco(Message<EnumRebatePactChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumRebatePactStatus.REBATE_PACT_CONFIRMED.getStatus());
    }

    /**
     * 支付成功             账单已确认  --》 付款成功
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "REBATE_PACT_CONFIRMED", target = "REBATE_PACT_PAY_SUCCESS")
    public void transitionRpco2Rpps(Message<EnumRebatePactChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumRebatePactStatus.REBATE_PACT_PAY_SUCCESS.getStatus());
    }

    /**
     * 付款失败             账单已确认  --》 付款失败
     */
    @Transactional(rollbackFor = Exception.class)
    @OnTransition(source = "REBATE_PACT_CONFIRMED", target = "REBATE_PACT_PAY_FAIL")
    public void transitionRpco2Rppf(Message<EnumRebatePactChangeEvent> message) {
        bizStatemachineTemplate.transitionStateChange(message, EnumRebatePactStatus.REBATE_PACT_PAY_FAIL.getStatus());
    }

}

package com.swcares.aiot.trace.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title ：OpenTelemetryProperties <br>
 * Package ：com.swcares.aiot.trace.config <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 * Description : 配置属性 <br>
 *
 * <AUTHOR> <br>
 * date 2024年 10月22日 11:15 <br>
 * @version v1.0 <br>
 */
@ConfigurationProperties(prefix = "swcares.trace")
@Component
@Data
public class OpenTelemetryProperties {

    /**
     * 是否启用trace功能
     */
    private boolean enabled = true;

    /**
     * 是否在响应头中添加traceId
     */
    private boolean addTraceIdToHeader = true;

    /**
     * trace响应头的名称
     */
    private String traceIdHeaderName = "X-Trace-Id";

    private String format = "json";

    /**
     * 需要进行trace的URL pattern
     */
    private String[] urlPatterns = new String[]{"/**"};
}

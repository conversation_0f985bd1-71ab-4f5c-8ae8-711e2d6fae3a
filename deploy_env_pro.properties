# 2-pro_common_app
DEPLOY_WAY=docker
FLYWAY_TOGGLE=false
LOGGING_LEVEL=INFO
SWAGGER_TOGGLE=false
MYBATIS_LOG_IMPL=org.apache.ibatis.logging.slf4j.Slf4jImpl
SERVER_PORT=80
# 2-pro_common_database
DATASOURCE_IP=*************
DATASOURCE_PORT=3306
DATASOURCE_USERNAME=alading
DATASOURCE_PASSWORD=kaiya@pro
# 2-pro_common_uc
UC_DOCKER_NAME=user-center
UC_ADDRESS=http://user-center/uaa

# 2-pro_common_job
JOB_ADMIN_IP=xxl-job-admin
JOB_ADMIN_NAME=xxl-job-admin
JOB_ADMIN_PORT=8080
JOB_ADMIN_PROTOCOL=http
JOB_EXECUTOR_IP_PORT=http://bls-job-executor-service:9999
JOB_EXECUTOR_NAME=bls-executor
# 2-pro_common_kafka
BROKERS=*************:32568,\
        *************:32568,\
        *************:32568,\
        *************:32568,\
        *************:32568,\
        *************:32470,\
        *************:32470,\
        *************:32470,\
        *************:32470,\
        *************:32470,\
        *************:30609,\
        *************:30609,\
        *************:30609,\
        *************:30609,\
        *************:30609
STREAM_EXCHANGE_ENGINE=stream.exchange.engine.pro
STREAM_EXCHANGE_TRIGGER=stream.exchange.trigger.pro
STREAM_GROUP_ENGINE=stream.group.engine.pro
STREAM_GROUP_TRIGGER=stream.group.trigger.pro
REPLICATION_FACTOR=3
# 2-pro_common_minio
MINIO_PUBLIC_URL=https://bls.swcares.com.cn
MINIO_PROTOCOL=http
MINIO_HOST=*************
MINIO_PORT_VAR=9000
MINIO_ACCESS_KEY=kaiya
MINIO_SECRET_KEY=kaiya@pro
MINIO_PUBLIC_BUCKET=bls-public
MINIO_PRIVATE_BUCKET=bls-private
# 2-pro_common_nacos
NACOS_HOST=*************
NACOS_PORT=38848
NACOS_NAMESPACE=BLS_PRO
NACOS_GROUP=BILLING-SKY-GROUP
NACOS_USERNAME=nacos
NACOS_PASSWORD=Kaiya@pro
# 2-pro_common_rabbitMQ
RABBITMQ_HOST=*************
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=admin
RABBITMQ_PASSWORD=kaiya@pro
RABBITMQ_VIRTUAL_HOST=/
# 2-pro_common_redis
REDIS_HOST=*************
REDIS_PORT_VAR=6379
REDIS_DATABASE=0
REDIS_PASSWORD=kaiya@pro
# 3-pro_airline_ass
AIRLINE_ASS_SERVER_PORT=80
AIRLINE_ASS_DATASOURCE_IP=*************
AIRLINE_ASS_DATASOURCE_PORT=3306
AIRLINE_ASS_DATASOURCE_DBNAME=bls_airline_ass
AIRLINE_ASS_DATASOURCE_USERNAME=alading
AIRLINE_ASS_DATASOURCE_PASSWORD=kaiya@pro
AIRLINE_ASS_CALCULATE_FEIGN_URL=http://bls-gateway
# 3-pro_gateway
GATEWAY_SERVER_PORT=80
# 3-pro_travelsky
TRAVELSKY_DATASOURCE_DBNAME=bls_travelsky
TRAVELSKY_DATASOURCE_IP=*************
TRAVELSKY_DATASOURCE_PASSWORD=kaiya@pro
TRAVELSKY_DATASOURCE_PORT=3306
TRAVELSKY_DATASOURCE_USERNAME=alading
TRAVELSKY_SERVER_PORT=80
# 3-pro_user_center
DATASOURCE_DBNAME=bls_uc
# 4-pro_airport_aps
AIRPORT_APS_CORS_ALLOWED_ORIGINS=all
AIRPORT_APS_DATASOURCE_DBNAME=aps_ten_sys
AIRPORT_APS_JPA_SHOW_SQL_TOGGLE=true
AIRPORT_APS_SERVER_PORT=80
AIRPORT_APS_SSSS_URL=http://bls-gateway/ssss/sensor/gather
AIRPORT_APS_DATACENTER_URL=http://gateway/datacenter
AIRPORT_APS_SIGN_URL=http://bls-gateway/signature
# 4-pro_airport_as_message
AIRPORT_MESSAGE_DATASOURCE_DBNAME=fs-mc-system
AIRPORT_MESSAGE_SERVER_PORT=80
AIRPORT_MESSAGE_JPUSH_APP_KEY=484a169745299fdab48b26c6
AIRPORT_MESSAGE_JPUSH_MASTER_SECRET=82ded29335dfcb9e6b503b28
# 4-pro_airport_as_node
AIRPORT_NODE_DATASOURCE_DBNAME=fs-ns-system
AIRPORT_NODE_MINIO_PUBLIC_BUCKET=node-safeguards-file-public
AIRPORT_NODE_SERVER_PORT=80
# 4-pro_airport_as_sign
AIRPORT_SIGN_DATASOURCE_DBNAME=fs-sign-bill-system
AIRPORT_SIGN_MINIO_PUBLIC_BUCKET=sign-bill-file-public
AIRPORT_SIGN_SERVER_PORT=80
# 4-pro_airport_flight
AIRPORT_FLIGHT_DATASOURCE_DBNAME=bc-flight-system
AIRPORT_FLIGHT_SERVER_PORT=80
# 4-pro_syne_sess
SYNE_SESS_SERVER_PORT=80
SYNE_SESS_DATASOURCE_IP=*************
SYNE_SESS_DATASOURCE_PORT=3306
SYNE_SESS_DATASOURCE_DBNAME=bls_syne_sess
SYNE_SESS_DATASOURCE_USERNAME=alading
SYNE_SESS_DATASOURCE_PASSWORD=kaiya@pro
# 4-pro_syne_ssss
SYNE_SSSS_SERVER_PORT=80
SYNE_SSSS_DATASOURCE_IP=*************
SYNE_SSSS_DATASOURCE_PORT=3306
SYNE_SSSS_DATASOURCE_DBNAME=bls_syne_ssss
SYNE_SSSS_DATASOURCE_USERNAME=alading
SYNE_SSSS_DATASOURCE_PASSWORD=kaiya@pro
# 4-pro_syne_stss
SYNE_STSS_SERVER_PORT=80
SYNE_STSS_DATASOURCE_IP=*************
SYNE_STSS_DATASOURCE_PORT=3306
SYNE_STSS_DATASOURCE_DBNAME=bls_syne_stss
SYNE_STSS_DATASOURCE_USERNAME=alading
SYNE_STSS_DATASOURCE_PASSWORD=kaiya@pro
UC_DATASOURCE_DBNAME=aiot-uc
# gateway—docker-name 用于外部进行调用
BLS_GATE_DOCKER_SERVER_NAME=bls-gateway-new
# 5-test_calculate
CALCULATE_SERVER_PORT=80
CALCULATE_DATASOURCE_IP=*************
CALCULATE_DATASOURCE_PORT=3306
CALCULATE_DATASOURCE_DBNAME=bls_calculate
CALCULATE_DATASOURCE_USERNAME=alading
CALCULATE_DATASOURCE_PASSWORD=kaiya@pro

# 6-uat_endpoint
ENDPOINT_SERVER_PORT=80
ENDPOINT_DATASOURCE_IP=*************
ENDPOINT_DATASOURCE_PORT=3306
ENDPOINT_DATASOURCE_DBNAME=bls_endpoint
ENDPOINT_DATASOURCE_USERNAME=alading
ENDPOINT_DATASOURCE_PASSWORD=kaiya@pro

KINGDEE_CLIENT_ID=publicreimburs
KINGDEE_CLIENT_SECRET=P@sswOrd20242024
KINGDEE_USERNAME=interface
KINGDEE_ACCOUNT_ID=1133252546643623936
KINGDEE_LOGIN_URL=http://************:8022/ierp/kapi/oauth2/getToken
KINGDEE_REI_EST_BILL_URL=http://************:8022/ierp/kapi/v2/chx/em/CreateDocumentPublicreimbursBill



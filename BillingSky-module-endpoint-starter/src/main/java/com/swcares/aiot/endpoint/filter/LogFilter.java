package com.swcares.aiot.endpoint.filter;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import javax.servlet.*;
import java.io.IOException;
import java.util.UUID;


/**
 * 日志过滤器
 *
 * <AUTHOR>
 */
@Slf4j
public class LogFilter implements Filter {
    public static final String TRACE_ID = "TRACE_ID";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        log.info("================日志过滤器====================");
        MDC.put(TRACE_ID, UUID.randomUUID().toString());
        log.info("记录请求日志");
        chain.doFilter(request, response);
        log.info("记录响应日志");
    }
}

package com.swcares.aiot.util;

import com.swcares.baseframe.common.exception.BusinessException;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：AssertUtil <br>
 * Package：com.swcares.aiot.vip.common.utils <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 09月01日 18:57 <br>
 * @version v1.0 <br>
 */
@Slf4j
@UtilityClass
public class AssertUtil {

    /**
     * Title: assertNull </br>
     * Description:  断言对象为空，如果不为空抛出异常  </br>
     * author 周扬
     * date 2021/9/7 9:17
     *
     * @param obj           断言对象
     * @param exceptionCode 异常编码
     */
    public static void assertNull(Object obj, Integer exceptionCode) {
        if (obj == null) {
            return;
        }
        if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            if (!list.isEmpty()) {
                throw new BusinessException(exceptionCode);
            }
        }

    }

}

package com.swcares.aiot.util;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title：RegexUtil <br>
 * Package：com.swcares.aiot.vip.util <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * Description:
 *
 * <AUTHOR> <br>
 * date 2021年 11月19日 12:47 <br>
 * @version v1.0 <br>
 */
@Slf4j
@UtilityClass
public class RegexUtil {
    public static boolean checkString(String sourceString, String regex) {
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(sourceString);
        return m.find();
    }
}

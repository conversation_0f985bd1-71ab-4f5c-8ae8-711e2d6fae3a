package com.swcares.aiot.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 阈值
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("calc_threshold")
@ApiModel(value="CalcThreshold对象", description="阈值")
public class CalcThreshold extends Model<CalcThreshold> {

    private static final long serialVersionUID = 1L;

    // TableField(convert=true, keyFlag=true, keyIdentityFlag=false, name=id, type=varchar(32), propertyName=id, columnType=STRING, comment=id, fill=null, keyWords=false, columnName=id, customMap=null)
    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=contract_id, type=varchar(32), propertyName=contractId, columnType=STRING, comment=合同id, fill=null, keyWords=false, columnName=contract_id, customMap=null)
    @ApiModelProperty(value = "合同id")
    @TableField("contract_id")
    private String contractId;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=airport_code, type=varchar(3), propertyName=airportCode, columnType=STRING, comment=机场三字码, fill=null, keyWords=false, columnName=airport_code, customMap=null)
    @ApiModelProperty(value = "机场三字码")
    @TableField("airport_code")
    private String airportCode;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=airline_code, type=varchar(3), propertyName=airlineCode, columnType=STRING, comment=航司结算代码, fill=null, keyWords=false, columnName=airline_code, customMap=null)
    @ApiModelProperty(value = "航司结算代码")
    @TableField("airline_code")
    private String airlineCode;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=express_id, type=varchar(32), propertyName=expressId, columnType=STRING, comment=表达式id, fill=null, keyWords=false, columnName=express_id, customMap=null)
    @ApiModelProperty(value = "表达式id")
    @TableField("express_id")
    private String expressId;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=indicator_id, type=varchar(32), propertyName=indicatorId, columnType=STRING, comment=指标项id, fill=null, keyWords=false, columnName=indicator_id, customMap=null)
    @ApiModelProperty(value = "指标项id")
    @TableField("indicator_id")
    private String indicatorId;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=deleted, type=tinyint(1), propertyName=deleted, columnType=BOOLEAN, comment=删除标识（1：删除 0：正常）, fill=INSERT, keyWords=false, columnName=deleted, customMap=null)
    @ApiModelProperty(value = "删除标识（1：删除 0：正常）")
    @TableField(value = "deleted", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=created_by, type=varchar(32), propertyName=createdBy, columnType=STRING, comment=创建人, fill=INSERT, keyWords=false, columnName=created_by, customMap=null)
    @ApiModelProperty(value = "创建人")
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=created_time, type=datetime, propertyName=createdTime, columnType=LOCAL_DATE_TIME, comment=创建时间, fill=INSERT, keyWords=false, columnName=created_time, customMap=null)
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=updated_by, type=varchar(32), propertyName=updatedBy, columnType=STRING, comment=更新人, fill=INSERT_UPDATE, keyWords=false, columnName=updated_by, customMap=null)
    @ApiModelProperty(value = "更新人")
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    // TableField(convert=true, keyFlag=false, keyIdentityFlag=false, name=updated_time, type=datetime, propertyName=updatedTime, columnType=LOCAL_DATE_TIME, comment=更新时间 , fill=INSERT_UPDATE, keyWords=false, columnName=updated_time, customMap=null)
    @ApiModelProperty(value = "更新时间 ")
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}

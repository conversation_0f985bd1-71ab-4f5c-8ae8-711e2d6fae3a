<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds">
    <!--控制台输出颜色-->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!-- 读取spring.application.name中的属性来生成日志文件名 -->
    <springProperty scope="context" name="logName" source="spring.application.name" defaultValue="default-name"/>
    <!-- 定义日志文件保存路径 -->
    <property name="log.path" value="/opt/logs/${logName}" />
    <!-- 日志输出格式 -->
    <property name="log.pattern.file" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level - [%thread]  %logger{20}.%method %line : %msg%n" />
    <!--console 日志颜色添加-->
    <property name="log.pattern.console" value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(-){faint} %clr([%14.14t]){faint} %clr(%logger{20}.%M{0}){cyan} %clr(%3.3L) %clr(:){faint} %msg%n"/>
    <!-- 日志字符集 -->
    <property name="file.charset" value="UTF-8" />
    <!-- 定义日志文件保留时间 -->
    <property name="file.history" value="7" />
    <!-- 文件大小 -->
    <property name="file.size" value="50MB" />
    <!-- 日志文件总大小 -->
    <property name="file.total.size.cap" value="1GB" />

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern.file}</pattern>
            <charset>${file.charset}</charset>
        </encoder>
    </appender>
    <!--RollingFileAppender继承自FileAppender，具有轮转日志文件的功能,是我们最常使用的Appender-->
    <appender name="file_debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--filter 过滤 级别过滤-->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <!-- 支持多个 JVM 同时写一个文件,也叫严格模式，该模式为开启时，滚动策略只能使用TimeBasedRollingPolicy及其子策略 -->
        <!-- 同时，该模式开启时不支持设置file标签及文件压缩,所以这一点非常终要!!!!! -->
        <prudent>false</prudent>
        <!--要写入文件的名称,该名称不会受滚动策略影响。如果文件不存在，则新建，该标签只含有滚动策略触发节点前的日志信息，经过某次轮转后，该文件被置为空，原内容被重命名或压缩-->
        <file>${log.path}/debug.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 基于时间和大小的轮转策略中fileNamePattern位置很重要，他决定了生成文件的名称及轮转时机,以及是否压缩 -->
            <!-- %d决定以什么时间维度轮转(但实际轮转时机取决于日志事件的到达时间)，比如%d{yyyy/MM}:每个月开始的时候轮转,%d默认为 yyyy-MM-dd：按天轮转 -->
            <!-- %i为文件按照maxFileSize大小规定轮转后的序号 -->
            <!-- 后缀以".zip"或".gz"结尾，则开启日志文件压缩 -->
            <fileNamePattern>${log.path}/%d{yyyy-MM, aux}/debug.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <!-- 单个日志文件最大大小，当文件达到该大小则触发截断（以及压缩）-->
            <maxFileSize>${file.size}</maxFileSize>
            <!-- 日志文件保留最大时间滚动周期，比如当filaNamePattern中%d以为dd结尾时，则保留60天-->
            <maxHistory>${file.history}</maxHistory>
            <!-- 日志文件保留的总的最大大小-->
            <totalSizeCap>${file.total.size.cap}</totalSizeCap>
        </rollingPolicy>
        <!-- encoder标签规定日志文件内容输出格式，具体参数参照logback官方文档 -->
        <encoder>
            <pattern>${log.pattern.file}</pattern>
            <charset>${file.charset}</charset>
        </encoder>
    </appender>
    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--filter 过滤 级别过滤-->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <prudent>false</prudent>
        <file>${log.path}/info.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM, aux}/info.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>${file.size}</maxFileSize>
            <maxHistory>${file.history}</maxHistory>
            <totalSizeCap>${file.total.size.cap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern.file}</pattern>
            <charset>${file.charset}</charset>
        </encoder>
    </appender>
    <appender name="file_warn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--filter 过滤 级别过滤-->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <prudent>false</prudent>
        <file>${log.path}/warn.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM, aux}/warn.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>${file.size}</maxFileSize>
            <maxHistory>${file.history}</maxHistory>
            <totalSizeCap>${file.total.size.cap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern.file}</pattern>
            <charset>${file.charset}</charset>
        </encoder>
    </appender>
    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--filter 过滤 级别过滤-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <prudent>false</prudent>
        <file>${log.path}/error.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM, aux}/error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>${file.size}</maxFileSize>
            <maxHistory>${file.history}</maxHistory>
            <totalSizeCap>${file.total.size.cap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern.file}</pattern>
            <charset>${file.charset}</charset>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="console" />
        <appender-ref ref="file_debug" />
        <appender-ref ref="file_info" />
        <appender-ref ref="file_warn" />
        <appender-ref ref="file_error" />
    </root>
</configuration>
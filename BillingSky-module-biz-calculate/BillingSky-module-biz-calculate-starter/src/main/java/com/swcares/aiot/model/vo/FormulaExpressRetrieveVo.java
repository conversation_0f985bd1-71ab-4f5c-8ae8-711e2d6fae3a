package com.swcares.aiot.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FormulaExpressRetrieveVo {
    /**
     * 公式id
     */
    @ApiModelProperty("公式id")
    private String formulaId;
    /**
     * 公式名称
     */
    @ApiModelProperty("公式名称")
    private String formulaName;
    /**
     * 航班属性
     */
    @ApiModelProperty("航班属性")
    private String flightAttribute;
    /**
     * 归集模式
     */
    @ApiModelProperty("归集模式")
    private String collectionMode;
    /**
     * 归集分类
     */
    @ApiModelProperty("归集分类")
    private String aggregationSort;
    /**
     * 收费单位
     */
    @ApiModelProperty("收费单位")
    private String chargeUnit;
    /**
     * 公式有效日期开始日期
     */
    @ApiModelProperty("公式有效日期开始日期")
    private String effectiveStartDate;
    /**
     * 公式有效日期结束日期
     */
    @ApiModelProperty("公式有效日期结束日期")
    private String effectiveEndDate;
    /**
     * 公式对应的表达式
     */
    private List<FormulaExpressRetrieveInnerVo> formulaExpressRetrieveInnerVos;
}

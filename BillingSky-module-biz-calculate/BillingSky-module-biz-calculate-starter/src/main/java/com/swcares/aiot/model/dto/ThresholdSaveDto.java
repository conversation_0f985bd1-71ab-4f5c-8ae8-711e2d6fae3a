package com.swcares.aiot.model.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.aiot.model.dto.ThresholdSaveDto
 * Description：阈值保存Dto
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/4/22 11:09
 * @version v1.0
 */
@Data
public class ThresholdSaveDto {

    @ApiModelProperty(value = "机场三字码")
    @Size(max = 3,message = "机场三字码长度不超过三位")
    @NotBlank(message = "机场三字码:不能为空")
    private String airportCode;

    @ApiModelProperty(value = "航司结算代码")
    @Size(max = 3,message = "航司结算代码长度不超过三位")
    @NotBlank(message = "航司结算代码:不能为空")
    private String airlineCode;

    @ApiModelProperty(value = "指标项id")
    @NotBlank(message = "指标项id:不能为空")
    private String indicatorId;

    @ApiModelProperty(value = "阈值条件List")
    private List<ThresholdConditionSaveOrUpdateDto> thresholdConditionSaveDtoList;
}

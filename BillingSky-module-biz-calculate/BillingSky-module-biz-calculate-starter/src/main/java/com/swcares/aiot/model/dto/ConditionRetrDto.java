package com.swcares.aiot.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class ConditionRetrDto {
    @ApiModelProperty("甲方客户列表")
    private List<String> customerIataAs;
    @ApiModelProperty("乙方客户列表")
    private List<String> customerIataBs;
    @ApiModelProperty("费用项代码")
    private List<String> expensesCodes;
    @ApiModelProperty(value = "合同id",example = "1783052791302246400")
    private String contractId;
}

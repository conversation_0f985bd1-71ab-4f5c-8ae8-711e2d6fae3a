package com.swcares.aiot.core.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * date 2025年05月07日17:13
 */
@Data
public class ExpensesRetrVo {
    @ApiModelProperty(value = "甲方",example = " ")
    private String customerIataA;

    @ApiModelProperty(value = "乙方",example = "YBP")
    @NotBlank(message = "乙方不能为空")
    private String customerIataB;
}

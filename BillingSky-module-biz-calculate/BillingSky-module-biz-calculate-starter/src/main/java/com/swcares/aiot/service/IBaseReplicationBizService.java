package com.swcares.aiot.service;

import com.swcares.aiot.model.dto.CopyExpensesWithFormulaDto;
import com.swcares.aiot.model.vo.BaseExpensesRetrieveDetailVo;
import com.swcares.aiot.model.vo.BaseFormulaExpressRetrieveVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IBaseReplicationBizService {

    /**
     * 查询基础库-费用项列表
     */
    List<BaseExpensesRetrieveDetailVo> retrieveBaseExpenses();

    /**
     * 查询基础库-费用项对应的公式以及公式对应的表达式
     */
    List<BaseFormulaExpressRetrieveVo> retrieveBaseFormulaExpress(String expensesId);

    String copyBaseExpenses(CopyExpensesWithFormulaDto copyExpensesWithFormulaDto);
}

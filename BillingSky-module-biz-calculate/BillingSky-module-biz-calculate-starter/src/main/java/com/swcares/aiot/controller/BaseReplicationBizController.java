package com.swcares.aiot.controller;

import com.swcares.aiot.core.cons.ConsCalculate;
import com.swcares.aiot.model.dto.CopyExpensesWithFormulaDto;
import com.swcares.aiot.model.vo.BaseExpensesRetrieveDetailVo;
import com.swcares.aiot.model.vo.BaseFormulaExpressRetrieveVo;
import com.swcares.aiot.service.IBaseReplicationBizService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> by yaodan
 **/
@Api(tags = "控制器-基础库-费用项-公式复制")
@RestController
@RequestMapping("/baseReplicationBiz")
@ApiVersion(ConsCalculate.SWAGGER_API_VERSION_INFO)
public class BaseReplicationBizController {
    @Resource
    private IBaseReplicationBizService iBaseReplicationBizService;

    @GetMapping("/retrieveBaseExpenses")
    @ApiOperation(value = "查询基础库-费用项列表")
    public BaseResult<List<BaseExpensesRetrieveDetailVo>> retrieveBaseExpenses() {
        return BaseResult.ok(iBaseReplicationBizService.retrieveBaseExpenses());
    }
    @GetMapping("/retrieveBaseFormulaExpress/{expensesId}")
    @ApiOperation(value = "查询基础库-费用项对应的公式以及公式对应的表达式")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "expensesId", value = "费用项ID", required = true, dataType = "String", paramType = "path")
    })
    public BaseResult<List<BaseFormulaExpressRetrieveVo>> retrieveBaseFormulaExpress(@PathVariable("expensesId") String expensesId) {
        return BaseResult.ok(iBaseReplicationBizService.retrieveBaseFormulaExpress(expensesId));
    }

    @PostMapping("/replicationBaseExpenses")
    @ApiOperation(value = "从基础库复制费用项")
    public BaseResult<String> copyBaseExpenses(@RequestBody CopyExpensesWithFormulaDto copyExpensesWithFormulaDto) {
        return BaseResult.ok(iBaseReplicationBizService.copyBaseExpenses(copyExpensesWithFormulaDto));
    }


}

package com.swcares.aiot.service.impl;

import com.swcares.aiot.mapper.ExpensesBizMapper;
import com.swcares.aiot.mapper.FormulaBizMapper;
import com.swcares.aiot.model.dto.CopyExpensesWithFormulaDto;
import com.swcares.aiot.model.vo.BaseExpensesRetrieveDetailVo;
import com.swcares.aiot.model.vo.BaseFormulaExpressRetrieveVo;
import com.swcares.aiot.service.IBaseReplicationBizService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class BaseReplicationBizServiceImpl implements IBaseReplicationBizService {
    @Resource
    private ExpensesBizMapper expensesBizMapper;
    @Resource
    private FormulaBizMapper formulaBizMapper;

    @Override
    public List<BaseExpensesRetrieveDetailVo> retrieveBaseExpenses() {
        //基础库默认查询客户编码字段 为tempB
        String customerIata = "tempB";
        return expensesBizMapper.retrieveBaseExpenses(customerIata);
    }

    @Override
    public List<BaseFormulaExpressRetrieveVo> retrieveBaseFormulaExpress(String expensesId) {
        return formulaBizMapper.retrieveBaseFormulaExpress(expensesId);
    }

    @Override
    public String copyBaseExpenses(CopyExpensesWithFormulaDto copyExpensesWithFormulaDto) {
        return "";
    }


}

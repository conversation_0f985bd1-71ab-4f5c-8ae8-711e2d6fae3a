package com.swcares.aiot.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.model.dto.ThrThresholdRetrDto;
import com.swcares.aiot.model.dto.ThresholdPageDto;
import com.swcares.aiot.model.vo.ThrThresholdRetrVo;
import com.swcares.aiot.model.vo.ThresholdPageVo;
import com.swcares.aiot.model.vo.ThresholdUnusedIndicatorVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ClassName：com.swcares.aiot.mapper.ThresholdBizMapper
 * Description：
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/4/22 13:21
 * @version v1.0
 */
public interface ThresholdBizMapper {

    IPage<ThresholdPageVo> pageThreshold(IPage<ThresholdPageVo> page, @Param("thresholdPageDto") ThresholdPageDto thresholdPageDto);

    List<ThresholdUnusedIndicatorVo> listUnusedIndicator(@Param("airportCode") String airportCode, @Param("airlineCode") String airlineCode);

    List<ThrThresholdRetrVo> retrieveThreshold(@Param("thrThresholdRetrDto") ThrThresholdRetrDto thrThresholdRetrDto);

    List<ThrThresholdRetrVo> retrieveThreshold2();
}

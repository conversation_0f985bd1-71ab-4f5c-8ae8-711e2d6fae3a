package com.swcares.aiot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aiot.model.dto.IndicatorPageDto;
import com.swcares.aiot.model.dto.IndicatorSaveDto;
import com.swcares.aiot.model.dto.IndicatorUpdateDto;
import com.swcares.aiot.model.vo.IndAllIndicatorRetrVo;
import com.swcares.aiot.model.vo.IndicatorPageVo;
import com.swcares.aiot.model.vo.IndicatorRetrieveVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IIndicatorBizService {
    /**
     * 条件分页查询
     *
     * @param indicatorPageDto 查询条件
     * @return 分页数据
     */
    IPage<IndicatorPageVo> pageIndicator(IndicatorPageDto indicatorPageDto);

    boolean saveIndicator(IndicatorSaveDto indicatorSaveDto);

    /**
     * 通过id删除指标项
     *
     * @param indicatorId 指标项id
     * @return 通过id删除指标项
     */
    Boolean deleteIndicator(String indicatorId);

    /**
     * 更新指标项详情
     *
     * @param indicatorUpdateDto 参数对象
     * @return 更新指标项详情
     */
    Boolean updateIndicator(IndicatorUpdateDto indicatorUpdateDto);

    IndicatorRetrieveVo retrieveIndicator(String indicatorId);

    /**
     * 获取所有的指标项
     *
     * @return 指标项集合
     */
    List<IndAllIndicatorRetrVo> retrieveAllIndicators();
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.swcares.aiot.mapper.ThresholdBizMapper">
    <resultMap id="expensesFormulaDtoMap" type="com.swcares.aiot.model.vo.ThresholdPageVo">
        <result property="id" column="id"/>
        <result property="indicatorName" column="indicator_name"/>
        <result property="indicatorId" column="indicator_id"/>
        <collection property="thresholdConditionPageVoList" ofType="com.swcares.aiot.model.vo.ThresholdConditionPageVo" select="selectConditionList" column="id"></collection>
    </resultMap>
    <resultMap id="conditionList" type="com.swcares.aiot.model.vo.ThresholdConditionPageVo">
        <result property="thresholdConditionId" column="id"/>
        <result property="conditionalSymbol" column="conditional_symbol"/>
        <result property="thresholdValue" column="threshold_value"/>
        <result property="thresholdIndex" column="threshold_index"/>
        <result property="type" column="type"/>
    </resultMap>

    <select id="pageThreshold" resultMap="expensesFormulaDtoMap">
        SELECT th.id,
               ind.name as indicator_name,
               ind.id   as indicator_id
        FROM calc_indicator ind
                 LEFT JOIN calc_threshold th on th.deleted = 0
            and th.indicator_id = ind.id and th.airport_code = #{thresholdPageDto.airportCode}
            and th.airline_code = #{thresholdPageDto.airlineCode}
        <where>
            ind.deleted = 0
              and ind.origin = 0
            <if test="thresholdPageDto.indicatorName != null and thresholdPageDto.indicatorName != ''">
                and ind.name like concat('%', #{thresholdPageDto.indicatorName}, '%')
            </if>
        </where>
        ORDER BY th.id desc
    </select>

    <select id="selectConditionList" resultMap="conditionList">
        select * from threshold_condition
        where deleted = 0
        and threshold_id=#{id}
    </select>

    <select id="listUnusedIndicator"
            resultType="com.swcares.aiot.model.vo.ThresholdUnusedIndicatorVo">
        select id, code, name
        from indicator
        where deleted = 0
          and id not in (select indicator_id
                         from threshold
                         where deleted = 0
                           and airport_code = #{airportCode}
                           and airline_code = #{airlineCode})
    </select>

    <select id="retrieveThreshold" resultType="com.swcares.aiot.model.vo.ThrThresholdRetrVo">
        SELECT ind.`code`                        AS indicator_code,
               GROUP_CONCAT(trc.threshold_value) AS threshold_values
        FROM calc_threshold trh
                 LEFT JOIN calc_threshold_condition trc ON trh.id = trc.threshold_id
                 LEFT JOIN calc_indicator ind ON trh.indicator_id = ind.id
        <where>
            <if test="thrThresholdRetrDto.airportCode != null and thrThresholdRetrDto.airportCode != ''">
                trh.airport_code = #{thrThresholdRetrDto.airportCode}
            </if>
            <if test="thrThresholdRetrDto.airlineCode != null and thrThresholdRetrDto.airlineCode != ''">
                AND trh.airline_code = #{thrThresholdRetrDto.airlineCode}
            </if>
            AND trh.deleted = 0
            AND trc.deleted = 0
            AND ind.deleted = 0
            GROUP BY trh.indicator_id
        </where>
    </select>

    <select id="retrieveThreshold2" resultType="com.swcares.aiot.model.vo.ThrThresholdRetrVo">
        select trh.indicator_id,
               trh.airport_code,
               trh.airline_code,
               ind.`code`                        AS indicator_code,
               GROUP_CONCAT(trc.threshold_value) AS threshold_values
        FROM calc_threshold trh
                 LEFT JOIN calc_threshold_condition trc ON trh.id = trc.threshold_id
                 LEFT JOIN calc_indicator ind ON trh.indicator_id = ind.id
        where trh.deleted = 0
          AND trc.deleted = 0
          AND ind.deleted = 0
        GROUP BY trh.indicator_id, trh.airport_code, trh.airline_code
    </select>
</mapper>

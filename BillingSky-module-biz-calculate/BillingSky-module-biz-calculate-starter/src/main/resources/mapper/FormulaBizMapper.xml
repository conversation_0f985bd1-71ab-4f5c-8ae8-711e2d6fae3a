<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.swcares.aiot.mapper.FormulaBizMapper">
    <resultMap id="forFormulaRetrVoMap" type="com.swcares.aiot.model.vo.ForFormulaRetrVo">
        <result property="expensesName" column="expenses_name"/>
        <result property="expensesCode" column="expenses_code"/>
        <result property="formulaId" column="formula_id"/>
        <result property="name" column="formula_name"/>
        <result property="flightAttribute" column="flight_attribute"/>
        <result property="collectionMode" column="collection_mode"/>
        <result property="aggregationSort" column="aggregation_sort"/>
        <result property="chargeUnit" column="charge_unit"/>
        <result property="effectiveStartDate" column="effective_start_date"/>
        <result property="effectiveEndDate" column="effective_end_date"/>
        <result property="remark" column="remark"/>
        <collection property="forExpressRetrVos"
                    ofType="com.swcares.aiot.model.vo.ForExpressRetrVo">
            <result property="expressId" column="express_id"/>
            <result property="criteria" column="express_criteria"/>
            <result property="valuationQuantity" column="valuation_quantity"/>
            <result property="valuationUnitPrice" column="valuation_unit_price"/>
        </collection>
    </resultMap>

    <select id="retrForFormulaExpress" resultMap="forFormulaRetrVoMap">
        SELECT ee.`name`                                 AS expenses_name,
               ee.`code`                                 AS expenses_code,
               fm.id                                     AS formula_id,
               fm.`name`                                 AS formula_name,
               fm.flight_attribute                       AS flight_attribute,
               fm.collection_mode                        AS collection_mode,
               fm.aggregation_sort                       AS aggregation_sort,
               fm.charge_unit                            AS charge_unit,
               fm.effective_start_date                   AS effective_start_date,
               fm.effective_end_date                     AS effective_end_date,
               fm.remark                                 AS remark,
               ep.id                                     AS express_id,
               IF(ep.criteria = 'true', '', ep.criteria) AS express_criteria,
               ep.valuation_quantity                     AS valuation_quantity,
               ep.valuation_unit_price                   AS valuation_unit_price
        FROM calc_expenses ee
                 LEFT JOIN calc_formula  fm ON ee.id = fm.expenses_id
                 LEFT JOIN calc_express  ep ON fm.id = ep.formula_id
        <where>
            fm.id = #{formulaId}
              AND fm.deleted = 0
              AND ep.deleted = 0
        </where>
    </select>

    <resultMap id="formulaExpressRetrieveDtoMap" type="com.swcares.aiot.model.vo.FormulaExpressRetrieveVo">
        <result property="formulaId" column="formula_id"/>
        <result property="formulaName" column="formula_name"/>
        <result property="flightAttribute" column="flight_attribute"/>
        <result property="collectionMode" column="collection_mode"/>
        <result property="aggregationSort" column="aggregation_sort"/>
        <result property="chargeUnit" column="charge_unit"/>
        <result property="effectiveStartDate" column="effective_start_date"/>
        <result property="effectiveEndDate" column="effective_end_date"/>
        <collection property="formulaExpressRetrieveInnerVos"
                    ofType="com.swcares.aiot.model.vo.FormulaExpressRetrieveInnerVo">
            <result property="expressCriteria" column="express_criteria"/>
            <result property="valuationQuantity" column="valuation_quantity"/>
            <result property="valuationUnitPrice" column="valuation_unit_price"/>
        </collection>
    </resultMap>

    <select id="retrieveFormulaExpress" resultMap="formulaExpressRetrieveDtoMap">
        SELECT fm.id                                     AS formula_id,
               fm.`name`                                 AS formula_name,
               fm.flight_attribute                       AS flight_attribute,
               fm.collection_mode                        AS collection_mode,
               fm.aggregation_sort                       AS aggregation_sort,
               fm.charge_unit                            AS charge_unit,
               fm.effective_start_date                   AS effective_start_date,
               fm.effective_end_date                     AS effective_end_date,
               if(ep.criteria = 'true', '', ep.criteria) AS express_criteria,
               ep.valuation_quantity                     AS valuation_quantity,
               ep.valuation_unit_price                   AS valuation_unit_price
        FROM calc_formula  fm
                 LEFT JOIN calc_express  ep ON fm.id = ep.formula_id
        <where>
            <if test="expensesId != null and expensesId != ''">
                fm.expenses_id = #{expensesId}
            </if>
            AND fm.deleted = 0
            AND ep.deleted = 0
        </where>
    </select>

    <resultMap id="BaseformulaExpressRetrieveDtoMap" type="com.swcares.aiot.model.vo.BaseFormulaExpressRetrieveVo">
        <result property="formulaId" column="formula_id"/>
        <result property="formulaName" column="formula_name"/>
        <result property="flightAttribute" column="flight_attribute"/>
        <result property="collectionMode" column="collection_mode"/>
        <result property="aggregationSort" column="aggregation_sort"/>
        <result property="chargeUnit" column="charge_unit"/>
        <result property="effectiveStartDate" column="effective_start_date"/>
        <result property="effectiveEndDate" column="effective_end_date"/>
        <collection property="baseFormulaExpressRetrieveInnerVos"
                    ofType="com.swcares.aiot.model.vo.BaseFormulaExpressRetrieveInnerVo">
            <result property="expressCriteria" column="express_criteria"/>
            <result property="valuationQuantity" column="valuation_quantity"/>
            <result property="valuationUnitPrice" column="valuation_unit_price"/>
            <result property="expressId" column="express_id"/>
            <collection property="baseIndicatorRetrieveVos" ofType="com.swcares.aiot.model.vo.BaseIndicatorRetrieveVo">
                <result property="indicatorId" column="indicator_id"/>
                <result property="indicatorName" column="indicator_name"/>
                <result property="indicatorCode" column="indicator_code"/>
                <result property="indicatorSort" column="indicator_sort"/>
                <result property="origin" column="origin"/>
            </collection>
        </collection>
    </resultMap>
    <select id="retrieveBaseFormulaExpress" resultMap="BaseformulaExpressRetrieveDtoMap">
        SELECT fm.id                                     AS formula_id,
               fm.`name`                                 AS formula_name,
               fm.flight_attribute                       AS flight_attribute,
               fm.collection_mode                        AS collection_mode,
               fm.aggregation_sort                       AS aggregation_sort,
               fm.charge_unit                            AS charge_unit,
               fm.effective_start_date                   AS effective_start_date,
               fm.effective_end_date                     AS effective_end_date,
               if(ep.criteria = 'true', '', ep.criteria) AS express_criteria,
               ep.valuation_quantity                     AS valuation_quantity,
               ep.valuation_unit_price                   AS valuation_unit_price,
               ep.id                                     AS express_id,
               ci.`name`                                 AS indicator_name,
               ci.id                                     AS indicator_id,
               ci.origin                                 AS origin,
               ci.`code`                                 AS indicator_code,
               ci.sort                                   AS indicator_sort
        FROM calc_formula fm
                 LEFT JOIN calc_express ep ON fm.id = ep.formula_id
                 LEFT JOIN calc_threshold ct on ct.express_id = ep.id
                 LEFT JOIN calc_indicator ci on ci.id = ct.indicator_id
        <where>
            <if test="expensesId != null and expensesId != ''">
                fm.expenses_id = #{expensesId}
            </if>
            AND fm.deleted = 0
            AND ep.deleted = 0
            AND ct.deleted = 0
            AND ci.deleted = 0
        </where>
        ORDER BY fm.effective_end_date DESC
    </select>
</mapper>
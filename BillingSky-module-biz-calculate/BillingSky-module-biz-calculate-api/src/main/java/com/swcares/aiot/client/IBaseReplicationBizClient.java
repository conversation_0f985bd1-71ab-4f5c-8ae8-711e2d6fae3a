package com.swcares.aiot.client;


import com.swcares.aiot.fallback.BaseReplicationBizFallback;
import com.swcares.aiot.model.vo.BaseExpensesRetrieveDetailVo;
import com.swcares.aiot.model.vo.BaseFormulaExpressRetrieveVo;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.feign.FeignClientInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * 基础库费用项远程调用接口
 */
@FeignClient(value = "${spring.application.name:calculate}",
        contextId = "IBaseReplicationBizClient",
        path = "${server.servlet.context-path:calculate}/baseReplicationBiz",
        fallbackFactory = BaseReplicationBizFallback.class,
        configuration = FeignClientInterceptor.class)
public interface IBaseReplicationBizClient {

    /**
     * 查询基础库-费用项列表
     */
    @GetMapping("/retrieveBaseExpenses")
    BaseResult<List<BaseExpensesRetrieveDetailVo>> retrieveBaseExpenses();

    /**
     * 查询基础库-费用项对应的公式以及公式对应的表达式
     */

    @GetMapping("/retrieveBaseFormulaExpress/{expensesId}")
    BaseResult<List<BaseFormulaExpressRetrieveVo>> retrieveBaseFormulaExpress(@PathVariable("expensesId") String expensesId);
}

package com.swcares.aiot.starter.properties;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "swcares.calculate.feign")
@NoArgsConstructor
public class CalculateProperties {
    /**
     * 要访问的url
     */
    @Value("${swcares.calculate.feign.url:' '}")
    private String url;
}

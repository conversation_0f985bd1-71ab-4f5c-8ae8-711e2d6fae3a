package com.swcares.aiot.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ThrThresholdRetrVo implements Serializable {

    @ApiModelProperty("机场三字码")
    private String airportCode;

    @ApiModelProperty("航司三字码")
    private String airlineCode;

    @ApiModelProperty("编码")
    private String indicatorCode;

    @ApiModelProperty("指标值列表")
    private String thresholdValues;
}

package com.swcares.aiot.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CopyFormulaDto {
    @ApiModelProperty("公式-名称")
    private String formulaName;
    @ApiModelProperty("公式-航班属性")
    private String flightAttribute;
    @ApiModelProperty("公式-归集方式")
    private String collectionMode;
    @ApiModelProperty("公式-归集分类")
    private String aggregationSort;
    @ApiModelProperty("公式-收费单位")
    private String chargeUnit;
    @ApiModelProperty("公式-公式生效开始日期")
    private LocalDate effectiveStartDate;
    @ApiModelProperty("公式-公式生效结束日期")
    private LocalDate effectiveEndDate;
    @ApiModelProperty("公式-备注")
    private String remark;
    @ApiModelProperty("表达式集合")
    private List<ExpressDto> expressDtos;

}

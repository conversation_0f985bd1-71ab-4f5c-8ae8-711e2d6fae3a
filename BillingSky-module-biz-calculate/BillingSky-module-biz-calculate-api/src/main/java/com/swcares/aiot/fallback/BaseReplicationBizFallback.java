package com.swcares.aiot.fallback;

import com.swcares.aiot.client.IBaseReplicationBizClient;
import com.swcares.aiot.model.vo.BaseExpensesRetrieveDetailVo;
import com.swcares.aiot.model.vo.BaseFormulaExpressRetrieveVo;
import com.swcares.aiot.utils.LocalDateTimeUtils;
import com.swcares.baseframe.common.base.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class BaseReplicationBizFallback implements FallbackFactory<IBaseReplicationBizClient> {
    @Override
    public IBaseReplicationBizClient create(Throwable cause) {
  return new IBaseReplicationBizClient() {

        @Override
        public BaseResult<List<BaseExpensesRetrieveDetailVo>> retrieveBaseExpenses() {
            printLog();
            return BaseResult.ok(Collections.emptyList());
        }

        @Override
        public BaseResult<List<BaseFormulaExpressRetrieveVo>> retrieveBaseFormulaExpress(String expensesId) {
            printLog();
            return BaseResult.ok(Collections.emptyList());
        }
    };
    }

    private void printLog() {
        log.error("{}:产生了熔断-[计算中心(calculate)]", LocalDateTimeUtils.ofCtt());
    }
}

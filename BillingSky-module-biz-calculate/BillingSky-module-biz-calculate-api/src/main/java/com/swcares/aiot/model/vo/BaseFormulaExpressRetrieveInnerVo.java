package com.swcares.aiot.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BaseFormulaExpressRetrieveInnerVo {
    @ApiModelProperty("条件-表达式")
    private String expressCriteria;
    @ApiModelProperty("计价数量-表达式")
    private String valuationQuantity;
    @ApiModelProperty("计价单价-表达式")
    private String valuationUnitPrice;
    @ApiModelProperty("表达式id")
    private String expressId;
    /**
     * 指标项
     */
    @ApiModelProperty("指标项")
    private List<BaseIndicatorRetrieveVo> baseIndicatorRetrieveVos;
}

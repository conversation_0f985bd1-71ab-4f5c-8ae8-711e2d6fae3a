package com.swcares.aiot.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> by yaodan
 **/
@Data
public class CopyThresholdConditionDto {
    @ApiModelProperty("条件符号")
    private String conditionalSymbol;
    @ApiModelProperty("阈值")
    private String thresholdValue;
    @ApiModelProperty("阈值下标")
    private String thresholdIndex;
    @ApiModelProperty("条件类型（1：自定义 2：内置条件）")
    private String type;
}

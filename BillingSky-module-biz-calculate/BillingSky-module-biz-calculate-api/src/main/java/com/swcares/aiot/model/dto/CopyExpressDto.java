package com.swcares.aiot.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CopyExpressDto {
    @ApiModelProperty("表达式-条件表达式")
    private String criteria;
    @ApiModelProperty("表达式-计价数量")
    private String valuationQuantity;
    @ApiModelProperty("表达式-计价单价")
    private String valuationUnitPrice;
    @ApiModelProperty("表达式-阈值集合")
    List<CopyIndicatorDto> copyIndicatorDtos;
}

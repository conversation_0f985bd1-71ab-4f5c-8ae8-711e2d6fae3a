package com.swcares.aiot.utils;

import com.swcares.aiot.model.dto.FormulaExpiredDto;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aiot.service.impl.FormulaExpiredServiceImpl
 * Description：公式过期判断工具类
 * Copyright © 2024 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2024/10/8 11:38
 * @version v1.0
 */
public class FormulaExpiredUtils {

    private FormulaExpiredUtils() {
    }

    public static boolean isExpired(List<FormulaExpiredDto> effectiveDateList) {
        if (effectiveDateList == null || effectiveDateList.isEmpty()) {
            return true;
        }
        //判断结束时间
        LocalDate endDate = LocalDate.now();
        //判断开始时间(结束时间往前三个月）
        LocalDate startDate = endDate.minusMonths(3);

        effectiveDateList = effectiveDateList.stream().sorted(Comparator.comparing(FormulaExpiredDto::getEffectiveStartDate)).collect(Collectors.toList());

        for (FormulaExpiredDto formulaExpiredDto : effectiveDateList) {
            LocalDate effectiveStartDate = formulaExpiredDto.getEffectiveStartDate();
            LocalDate effectiveEndDate = formulaExpiredDto.getEffectiveEndDate();
            if ((effectiveStartDate.isBefore(startDate) || effectiveStartDate.equals(startDate))
                    && (effectiveEndDate.isAfter(endDate) || effectiveEndDate.equals(endDate))) {
                return false;
            } else if ((effectiveStartDate.isBefore(startDate) || effectiveStartDate.equals(startDate))
                    && !(effectiveEndDate.isAfter(endDate) || effectiveEndDate.equals(endDate))) {
                startDate = effectiveEndDate.plusDays(1);
            } else {
                return true;
            }
        }
        return true;
    }

}
